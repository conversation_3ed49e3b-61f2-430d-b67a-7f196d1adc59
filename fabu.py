# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv

import os
import json
import requests
from pathlib import Path
import re
from urllib.parse import urlparse
import time
import random


# def download_and_push_image(item, device_session):
#     """
#     从 JSON 数据中解析图片下载 URL，下载图片并推送到设备中。
#     :param item: 包含图片信息的单个 JSON 数据项
#     :param device_session: 设备会话，用于推送文件
#     """
#     try:
#         # 如果 tp_sp 是字符串，先解析为 JSON
#         tp_sp = item.get("tp_sp", [])
#         if isinstance(tp_sp, str):
#             tp_sp = json.loads(tp_sp)

#         # 检查 tp_sp 是否为列表
#         if not isinstance(tp_sp, list) or not tp_sp:
#             print("未找到有效的 tp_sp 数据")
#             return

#         # 遍历 tp_sp 并提取 DownloadUrl
#         for image_info in tp_sp:
#             download_url = image_info.get("DownloadUrl")
#             if not download_url:
#                 print("未找到有效的图片下载 URL")
#                 continue

#             print(f"图片下载 URL: {download_url}")

#             # 下载图片
#             try:
#                 response = requests.get(download_url)
#                 if response.status_code != 200:
#                     print(f"图片下载失败，状态码: {response.status_code}")
#                     continue

#                 # 创建本地保存路径
#                 desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
#                 fastdown_folder = os.path.join(desktop_path, "fastdown")
#                 os.makedirs(fastdown_folder, exist_ok=True)

#                 # 保存文件
#                 file_name = os.path.basename(download_url)
#                 local_file_path = os.path.join(fastdown_folder, file_name)
#                 with open(local_file_path, "wb") as file:
#                     file.write(response.content)
#                 print(f"图片已保存到本地路径: {local_file_path}")

#                 # 推送文件到设备
#                 remote_file_path = f"/sdcard/{file_name}"
#                 device_session.push_file(local_file_path, remote_file_path)
#                 print(f"文件已推送到设备路径: {remote_file_path}")
#             except Exception as e:
#                 print(f"处理下载 URL {download_url} 时发生错误: {e}")

#     except json.JSONDecodeError as e:
#         print(f"JSON 解析错误: {e}")
#     except Exception as e:
#         print(f"发生未知错误: {e}")




def clean_filename(filename):
    """
    清理文件名中的非法字符（包括 ?、=、&、: 等），确保在 Windows 操作系统中合法。
    :param filename: 原始文件名
    :return: 清理后的合法文件名
    """
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def get_filename_from_url(url):
    """
    从 URL 中提取文件名，并去掉查询参数部分。
    :param url: 文件的下载 URL
    :return: 清理后的文件名
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)  # 提取 URL 中的文件名部分
    return file_name.split('?')[0]  # 去掉查询参数部分


def get_media_type(file_name):
    """
    根据文件扩展名判断文件类型（图片或视频）。
    :param file_name: 文件名
    :return: 媒体类型 ('image' 或 'video')
    """
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    _, ext = os.path.splitext(file_name.lower())
    
    if ext in image_extensions:
        return 'image'
    elif ext in video_extensions:
        return 'video'
    else:
        return 'unknown'  # 如果是未知类型


def download_media(item, device_session):
    """
    从 JSON 数据中解析媒体文件下载 URL（包括图片和视频），流式下载文件并推送到设备中。
    :param item: 包含媒体文件信息的单个 JSON 数据项
    :param device_session: 设备会话，用于推送文件
    :return: 返回文件类型标记（'image' 或 'video'）
    """
    media_types = []  # 用于收集所有处理过的文件类型

    try:
        # 如果 tp_sp 是字符串，先解析为 JSON
        tp_sp = item.get("tp_sp", [])
        if isinstance(tp_sp, str):
            tp_sp = json.loads(tp_sp)

        # 检查 tp_sp 是否为列表
        if not isinstance(tp_sp, list) or not tp_sp:
            print("未找到有效的 tp_sp 数据")
            return None

        # 遍历 tp_sp 并提取 DownloadUrl
        for media_info in tp_sp:
            download_url = media_info.get("DownloadUrl")
            if not download_url:
                print("未找到有效的文件下载 URL")
                continue

            print(f"文件下载 URL: {download_url}")

            # 下载文件（流式下载）
            try:
                response = requests.get(download_url, stream=True, timeout=30)  # 使用流式下载
                if response.status_code != 200:
                    print(f"文件下载失败，状态码: {response.status_code}")
                    continue

                # 创建本地保存路径
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                fastdown_folder = os.path.join(desktop_path, "fastdown")
                os.makedirs(fastdown_folder, exist_ok=True)

                # 获取清理后的文件名
                file_name = get_filename_from_url(download_url)
                file_name = clean_filename(file_name)  # 清理非法字符

                # 保存文件（流式写入）
                local_file_path = os.path.join(fastdown_folder, file_name)
                with open(local_file_path, "wb") as file:
                    for chunk in response.iter_content(chunk_size=1024):
                        if chunk:
                            file.write(chunk)
                print(f"文件已保存到本地路径: {local_file_path}")

                # 推送文件到设备
                remote_file_path = f"/sdcard/{file_name}"
                device_session.push_file(local_file_path, remote_file_path)
                print(f"文件已推送到设备路径: {remote_file_path}")

                # 获取文件类型（图片或视频）
                media_type = get_media_type(file_name)
                media_types.append(media_type)  # 将文件类型加入到列表中

            except Exception as e:
                print(f"处理下载 URL {download_url} 时发生错误: {e}")
                continue

        # 下载完所有文件后，统一返回文件类型
        if 'video' in media_types:
            return 'video'  # 如果下载过视频文件，返回 'video'
        elif 'image' in media_types:
            return 'image' 
        else:
            return 'unknown'  # 如果既没有图片也没有视频文件，返回 'unknown'

    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None



def download_and_push_media(item, device_session):
    """
    从 JSON 数据中解析媒体文件下载 URL（包括图片和视频），下载文件并推送到设备中。
    :param item: 包含媒体文件信息的单个 JSON 数据项
    :param device_session: 设备会话，用于推送文件
    :return: 返回文件类型标记（'image' 或 'video'）
    """
    media_types = []  # 用于收集所有处理过的文件类型

    # 如果 tp_sp 是字符串，先解析为 JSON
    tp_sp = item.get("tp_sp", [])
    if isinstance(tp_sp, str):
        tp_sp = json.loads(tp_sp)

    # 检查 tp_sp 是否为列表
    if not isinstance(tp_sp, list) or not tp_sp:
        print("未找到有效的 tp_sp 数据")
        raise ValueError("未找到有效的 tp_sp 数据")

    # 遍历 tp_sp 并提取 DownloadUrl
    for media_info in tp_sp:
        download_url = media_info.get("DownloadUrl")
        if not download_url:
            print("未找到有效的文件下载 URL")
            raise ValueError("未找到有效的文件下载 URL")

        print(f"文件下载 URL: {download_url}")

        # 使用流式下载
        response = requests.get(download_url, stream=True, timeout=30)  # 设置超时为30秒
        if response.status_code != 200:
            raise Exception(f"文件下载失败，状态码: {response.status_code}")

        # 创建本地保存路径
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        fastdown_folder = os.path.join(desktop_path, "fastdown")
        os.makedirs(fastdown_folder, exist_ok=True)

        # 获取清理后的文件名
        file_name = get_filename_from_url(download_url)
        file_name = clean_filename(file_name)  # 清理非法字符

        # 保存文件（流式写入）
        local_file_path = os.path.join(fastdown_folder, file_name)
        with open(local_file_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=1024):  # 分块写入文件
                if chunk:
                    file.write(chunk)
        print(f"文件已保存到本地路径: {local_file_path}")

        # 推送文件到设备
        remote_file_path = f"/sdcard/{file_name}"
        device_session.push_file(local_file_path, remote_file_path)
        print(f"文件已推送到设备路径: {remote_file_path}")

        # 获取文件类型（图片或视频）
        media_type = get_media_type(file_name)
        media_types.append(media_type)  # 将文件类型加入到列表中

    # 下载完所有文件后，统一返回文件类型
    if 'video' in media_types:
        return 'video'  # 如果下载过视频文件，返回 'video'
    elif 'image' in media_types:
        return 'image' 
    else:
        return 'unknown'  # 如果既没有图片也没有视频文件，返回 'unknown'



def extract_and_publish(device_session, data):
    try:
        # data 是已经解析为字典的对象，直接操作即可
        # 提取标题和正文
        title = data.get("title", "默认标题")
        zhengwen = data.get("zhengwen", "默认正文")
        # 话题词
        topics = data.get("topic_word", "")  # 话题词是逗号分隔的字符串
        topic_list = topics.split(",") if topics else []

        # 定位标题和正文的输入框
        title_resource_id = "com.xingin.xhs:id/c3j"
        zhengwen_resource_id = "com.xingin.xhs:id/gqy"

        # 输入标题
        device_session.find_by_id(title_resource_id).input(title)
        
        # 输入正文
        print(zhengwen)
        zhengwen_input = device_session.find_by_id(zhengwen_resource_id)
        zhengwen_input.click()
        zhengwen_input.input(zhengwen)

        # 输入一个 # 符号（只有在话题词不为空时才输入）
        if topic_list:
            zhengwen_input.input(" #", append=True)

            # 拼接话题词并设置到剪切板
            # 第一个话题词不加#，后面的都加#
            topic_text_for_clipboard = " ".join([topic.strip() if idx == 0 else f"#{topic.strip()}" for idx, topic in enumerate(topic_list)])

            # 设置剪切板内容
            device_session.set_clipboard_text(topic_text_for_clipboard)

            print(f"话题词已复制到剪切板: {topic_text_for_clipboard}")
        else:
            print("没有话题词，跳过输入 #")

        # 定位并点击发布按钮
        # publish_button_resource_id = "com.xingin.xhs:id/alv"
        # device_session.find_by_id(publish_button_resource_id).click()

        # print("标题、正文已输入，发布按钮已点击。")

    except Exception as e:
        print(f"处理过程中出错: {e}")




def click_publish_button(device_session):
    # 定位并点击发布按钮
    device_session.back()
    publish_button_resource_id = "com.xingin.xhs:id/alx"
    device_session.find_by_id(publish_button_resource_id).click()
    print("发布按钮已点击。")



def update_mingdao_status(item, status="已发布", timeout=6):
    """
    修改明道表字段状态为指定值（默认 "已发布"），针对传入的单个循环项，不做重试和异常处理。
    :param item: 包含需要更新的行记录数据（单个 JSON 对象）。
    :param status: 要设置的状态值，默认值为 "已发布"。
    :param timeout: 请求超时时间（秒），默认值为 6。
    :return: API 调用结果
    """
    url = "https://api.mingdao.com/v2/open/worksheet/editRows"
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
    }
    
    # 提取 rowid
    row_id = item.get("rowid")
    if not row_id:
        raise ValueError("未找到有效的 rowid 数据")  # 如果 rowid 不存在，直接抛出标准异常

    # 构建请求数据
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "rowIds": [row_id],  # 使用 rowid
        "controls": [
            {
                "controlId": "release_status",
                "value": status,
                "valueType": 1
            }
        ]
    }

    # 发起 POST 请求，设置超时
    try:
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        response.raise_for_status()  # 如果响应错误，抛出 HTTP 异常
    except requests.exceptions.RequestException as e:
        raise e  # 直接抛出请求异常，外部调用会处理

    # 检查响应状态码
    res = response.json()
    if res.get("success"):
        print(f"行记录 {row_id} 的状态已更新为: {status}")
        return res
    else:
        # 如果 API 返回失败，直接抛出异常
        error_message = res.get("error", "未知错误")
        raise ValueError(f"更新失败: {error_message}")





# 点击后续流程
def click_fabu_follow(device_session,media_type):
    # 获取所有符合条件的元素
    elements = device_session.find_all_by_id("com.xingin.xhs:id/dr9")
    
    if media_type == "video" :
        device_session.find_all_by_id("com.xingin.xhs:id/ig4")[0].click()
        shipin_elements = device_session.find_all_by_id("com.xingin.xhs:id/dr9")
        shipin_elements[0].click()
    else:
        # 循环点击每一个元素
        for element in elements:
            element.click() 

    # 点击第一个图片
    device_session.find_all_by_id("com.xingin.xhs:id/ffv")[0].click()

    # 点击下一步
    device_session.find_by_id("com.xingin.xhs:id/a_v").click()  

    time.sleep(random.randint(10, 30))
    # 点击视频或者图片下一步
    device_session.find_by_id("com.xingin.xhs:id/aqe").click() 


# 点击全选删除
def click_all_delete(device_session):
    try:
        # 查找 "全选" 按钮
        select_elements = device_session.find_all_by_xpath("//*[text()='全选' or @content-desc='全选']")
        
        if select_elements:
            # 点击 "全选" 按钮
            select_elements[0].click()
            print("成功点击了全选按钮")
        else:
            print("未找到包含 '全选' 的元素")
        
        # 查找 "删除" 按钮
        delete_elements = device_session.find_all_by_xpath("//*[text()='删除' or @content-desc='删除']")
        
        if delete_elements:
            # 点击 "删除" 按钮
            delete_elements[0].click()
            print("成功点击了删除按钮")
        else:
            print("未找到包含 '删除' 的元素")
    
    except Exception as e:
        print(f"发生异常：{e}")    



def click_next_finsh(device_session):
    try: 
        # 查找 "下一步" 按钮
        next_element = device_session.find_by_text("下一步")
        
        if next_element:
            # 点击 "下一步" 按钮
            next_element.click()
        else:
            print("未找到包含 '下一步' 的元素")

        # 查找 "完成" 按钮
        finsh_element = device_session.find_by_text("完成")
        
        if finsh_element:
            # 点击 "完成" 按钮
            finsh_element.click()
        else:
            print("未找到包含 '完成' 的元素")    
    
    except Exception as e:
        print(f"发生异常：{e}")        



def click_photo(device_session):
    try: 
        # 查找 "下一步" 按钮
        photo_element = device_session.find_by_text("相册")
        
        if photo_element:
            # 点击 "下一步" 按钮
            photo_element.click()
        else:
            print("未找到包含 '相册' 的元素")
    
    except Exception as e:
        print(f"发生异常：{e}")        


def click_allow(device_session):
    try: 
        # 查找 "允许" 按钮
        allow_element = device_session.find_by_text("允许")
        
        if allow_element:
            # 点击 "允许" 按钮
            allow_element.click()
        else:
            print("未找到包含 '允许' 的元素")
    
    except Exception as e:
        print(f"未找到包含 '允许' 的元素 或者元素过多")    


def click_refesh(device_session):
    try: 
        # 查找 "刷新" 按钮
        refesh_element = device_session.find_by_text("刷新")
        
        if refesh_element:
            # 点击 "刷新" 按钮
            refesh_element.click()
        else:
            print("未找到包含 '刷新' 的元素")
    
    except Exception as e:
        print(f"发生异常：{e}")               


def verify_release(device_session):
    time.sleep(1)
    page_source = device_session.get_page_source()
    if "com.xingin.xhs:id/aql" in page_source or "com.xingin.xhs:id/apg" in page_source:
        return True
    else:
        return False
   

