import os
import platform
import threading
import time

# 只在 Windows 系统上执行此操作
if platform.system() == "Windows":
    try:
        import ctypes
        from ctypes import wintypes

        kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)

        # 定义常量
        STD_INPUT_HANDLE = -10
        ENABLE_QUICK_EDIT_MODE = 0x0040
        ENABLE_EXTENDED_FLAGS = 0x0080

        # 获取标准输入句柄
        h_stdin = kernel32.GetStdHandle(STD_INPUT_HANDLE)

        # 检查句柄是否有效。在 IDE 的模拟终端中，这里通常会是无效的
        if h_stdin is None or h_stdin == wintypes.HANDLE(-1).value:
            # 这不是一个错误，只是说明当前环境不支持此操作
            print("[INFO] Not running in a standard console, skipping QuickEdit Mode check.")
        else:
            # 获取并修改控制台模式
            current_mode = wintypes.DWORD()
            kernel32.GetConsoleMode(h_stdin, ctypes.byref(current_mode))
            new_mode = (current_mode.value | ENABLE_EXTENDED_FLAGS) & ~ENABLE_QUICK_EDIT_MODE
            kernel32.SetConsoleMode(h_stdin, new_mode)
            print("[INFO] Console QuickEdit Mode has been disabled for this session.")
    except (ImportError, OSError) as e:
        # 在 IDE 或其他非标准终端中，获取或设置模式可能会失败
        # 这是一个预期的、可接受的失败，打印警告即可
        print(f"[WARNING] Could not disable console QuickEdit Mode (this is expected in IDEs): {e}")

from src.services.adb_service import adb_start

from src import create_app

if __name__ == '__main__':
    adb_env = os.environ.get('ADB_ENV', '')
    if adb_env != '':
        adb_thread = threading.Thread(target=adb_start, args=(adb_env,), daemon=True)
        adb_thread.start()

    app = create_app()
    app.run(host='0.0.0.0', port=8080, use_reloader=False)
