
from flask import Blueprint, jsonify, request
from flask import current_app
from ..utils import logger
from ..services.adb_service import pair


api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/adb/pair', methods=['GET'])
def get_device_data():
    ips = request.args.get('ips')
    port = request.args.get('port', type=int)
    code = request.args.get('code')

    if not ips or not port or not code:
        return jsonify({
            "error_code": 400,
            "message": "Missing required parameters: ip, port, or code.",
            "data": {}
        }),400

    try:
        for ip in ips.split(','):
            ip = ip.strip()
            if ip:
                result = pair(ip, port, code)
                if result:
                    return jsonify({
                        "error_code": 0,
                        "message": "",
                        "data": result
                    }), 200
    except Exception as e:
        1

    return jsonify({
        "error_code": 500,
        "message": f"Pairing failed",
        "data": {}
    }), 500


@api_blueprint.route('/xhs/notes-stats', methods=['POST'])
def trigger_notes_stats():
    """手动触发小红书笔记统计"""
    try:
        from ..services.scheduled_task_service import scheduled_task_service
        from ..services.task_service import devices_udids_local

        # 获取连接的设备
        udids_connected = devices_udids_local()

        if not udids_connected:
            return jsonify({
                "error_code": 400,
                "message": "没有找到连接的设备",
                "data": {}
            }), 400

        # 获取第一个设备进行统计
        udid_dict = udids_connected[0]

        # 在后台线程中执行统计
        import threading
        stats_thread = threading.Thread(
            target=scheduled_task_service.run_manual_stats,
            args=(udid_dict,),
            daemon=True
        )
        stats_thread.start()

        return jsonify({
            "error_code": 0,
            "message": f"已开始为设备 {udid_dict['custom_udid']} 执行笔记统计",
            "data": {
                "device": udid_dict['custom_udid'],
                "status": "started"
            }
        }), 200

    except Exception as e:
        logger.error(f"触发笔记统计失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"触发笔记统计失败: {str(e)}",
            "data": {}
        }), 500


@api_blueprint.route('/xhs/notes-stats/status', methods=['GET'])
def get_notes_stats_status():
    """获取笔记统计状态"""
    try:
        from ..services.scheduled_task_service import scheduled_task_service
        from datetime import datetime

        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute

        # 检查是否在统计时间窗口内
        is_stats_time = (current_hour == 23 and current_minute >= 30) or \
                       (current_hour == 0 and current_minute <= 30)

        return jsonify({
            "error_code": 0,
            "message": "获取状态成功",
            "data": {
                "scheduler_running": scheduled_task_service.running,
                "current_time": current_time.strftime("%H:%M:%S"),
                "is_stats_time": is_stats_time,
                "stats_window": "每天 23:30-00:30",
                "mode": "集成模式（统计功能已集成到主任务循环）"
            }
        }), 200

    except Exception as e:
        logger.error(f"获取笔记统计状态失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"获取状态失败: {str(e)}",
            "data": {}
        }), 500
