
from flask import Blueprint, jsonify, request
from flask import current_app
from ..utils import logger
from ..services.adb_service import pair


api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/adb/pair', methods=['GET'])
def get_device_data():
    ips = request.args.get('ips')
    port = request.args.get('port', type=int)
    code = request.args.get('code')

    if not ips or not port or not code:
        return jsonify({
            "error_code": 400,
            "message": "Missing required parameters: ip, port, or code.",
            "data": {}
        }),400

    try:
        for ip in ips.split(','):
            ip = ip.strip()
            if ip:
                result = pair(ip, port, code)
                if result:
                    return jsonify({
                        "error_code": 0,
                        "message": "",
                        "data": result
                    }), 200
    except Exception as e:
        1

    return jsonify({
        "error_code": 500,
        "message": f"Pairing failed",
        "data": {}
    }), 500
