import datetime
import hashlib
import logging
import sys
import time
import re
import traceback

from lxml import etree

from ..utils import logger, glv, CommonMobileUtils, CommonUtils
from ..services.tool_service import ToolService
from ..services.common_service import common_service
from .hot_logic import HotLogic

class SpiderLogic:

    def __init__(self, custom_udid):
        self.domain = "https://api.open.hctalent.cn/channel"
        self.custom_udid = custom_udid
        self.device_session = None

        self.hot_logic = HotLogic(custom_udid)

        ToolService.build_common_var(self.custom_udid, "xhs_name", "")
        ToolService.build_common_var(self.custom_udid, "xhs_account_number", "")

    def init(self, device_session):
        self.device_session = device_session
        self.hot_logic.init(device_session)

    # 获取抓取动作
    def grab_config(self, action, out_put_num):
        url = self.domain + "/grab/get-task"
        req = {
            "action": action,
            "outPutNum": out_put_num,
            "token": "maotai"
        }
        return common_service.comm_post(url, req, 1)


    # spider处理主函数
    def run(self):
        # spider_configs = self.get_test_spider_configs()
        # ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_configs[0])
        # self.spider_record_use_user_id(spider_configs)
        # return

        xhs_account_number = glv[self.custom_udid]["xhs_account_number"]
        if "***********" in xhs_account_number:
            try:
                self.hot_logic.spider_weibo_top()
            except Exception as e:
                pass

            try:
                self.hot_logic.spider_baidu_top()
            except Exception as e:
                pass

            try:
                self.hot_logic.spider_douyin_top()
            except Exception as e:
                pass

            try:
                self.hot_logic.spider_xhs_top()
            except Exception as e:
                pass

            self.device_session.activate_app("com.xingin.xhs")

        # self.back_to_index()
        # spider_config_list = self.grab_config("更新话题词数据", 20)
        # if len(spider_config_list) > 0:
        #     self.update_topic_word_browse_count(spider_config_list)
        spider_config_list = self.grab_config("更新关键词笔记数", 20)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.update_search_word_notes_num(spider_config_list)
            self.back_to_index()

        spider_config_list = self.grab_config("采集搜索词", 1)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.grab_search_word(spider_config_list)
            self.back_to_index()


        spider_config_list = self.grab_config("采集固定的词", 1)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.spider_record(spider_config_list)
            self.back_to_index()


        spider_config_list = self.grab_config("定向采集博主", 1)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.spider_record_use_user_id(spider_config_list)
            self.back_to_index()


        spider_config_list = self.grab_config("采集关键词用户", 1)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.spider_user_use_search_word(spider_config_list)
            self.back_to_index()


        spider_config_list = self.grab_config("监听博主", 10)
        if len(spider_config_list) > 0:
            self.back_to_index()
            self.cron_listen_grab_user(spider_config_list)
            self.back_to_index()

        time.sleep(30)

    def set_channel_account_info(self):
        # 回退到主页面
        self.back_to_index()
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/0_resource_name_obfuscated' and @content-desc='我']", "click")
        xiaohongshu_name = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/0_resource_name_obfuscated' and contains(@text,'小红书号：')]/../../preceding-sibling::android.widget.TextView[1]").get_attribute("text")
        account_number = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/0_resource_name_obfuscated' and contains(@text,'小红书号：')]").get_attribute("text").replace("小红书号：", "")
        ToolService.build_common_var(self.custom_udid, "xhs_name", xiaohongshu_name)
        ToolService.build_common_var(self.custom_udid, "xhs_account_number", account_number)
        print(glv[self.custom_udid])

    def spider_user_use_search_word(self, spider_config_list):
        for spider_config in spider_config_list:
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)
            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]

            # 从首页点搜索进去输入搜索词
            self.click_input_from_index_search_button(search_word)

            CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='用户']", "click")

            user_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@text='关注']/../android.widget.TextView[1]")
            if len(user_list) == 0:
                self.back_to_index()
                return
            # 已经处理过的博主
            already_spider_user_name = {}
            # 抓多少个
            spider_count = 100
            while True:
                user_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@text='关注']/../android.widget.TextView[1]")
                for user_obj in user_list:
                    xhs_user_name = user_obj.get_attribute("text")
                    if xhs_user_name in already_spider_user_name:
                        continue
                    already_spider_user_name[xhs_user_name] = xhs_user_name
                    # 点击博主
                    user_obj.click()
                    # 解析作者的信息
                    grab_user = self.parse_user()
                    grab_user["searchWord"] = glv[self.custom_udid]["xhs_spider_config"]["input"]
                    grab_user["grabTaskId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"]
                    grab_user["grabTaskInputId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"]
                    grab_user["token"] = "maotai"
                    url = self.domain + "/grab/add-grab-user"
                    res = common_service.comm_post(url, grab_user, 1)
                    print(res)
                    # 回退到列表
                    CommonMobileUtils.back(self.device_session, 1)
                    if len(already_spider_user_name) >= spider_count:
                        break

                page_source = self.device_session.get_page_source()
                if "无更多内容" in page_source or len(already_spider_user_name) >= spider_count:
                    break
                self.device_session.swipe(300, 1100, 300, 600)

            # 更新任务状态为完成
            self.update_grab_task_input_status(2)
            # 回退到首页
            self.back_to_index()

    def cron_listen_grab_user(self, spider_config_list):
        for spider_config in spider_config_list:
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)
            search_word = glv[self.custom_udid]["xhs_spider_config"]["grabUser"]["userId"]
            self.spider_record_in_author_index(search_word)

    def spider_record_use_user_id(self, spider_config_list):
        for spider_config in spider_config_list:
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)
            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]
            self.spider_record_in_author_index(search_word)

    def spider_record_in_author_index(self,search_word):
            # 从首页点搜索进去输入搜索词
            self.click_input_from_index_search_button(search_word)

            CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='用户']", "click")

            # 点击用户
            text = "小红书号：" + search_word
            user_list = self.device_session.find_all_by_xpath(f".//android.widget.TextView[@text='{text}']")
            if len(user_list) == 1:
                user_list[0].click()
            else:
                # 更新任务状态为完成
                self.update_grab_task_input_status(2)
                # 回退到首页
                self.back_to_index()
                return

            # 解析作者的信息
            grab_user = self.parse_user()
            already_spider_title = {}
            same_count = 0
            while True:
                # 判断是否已经抓完
                if len(already_spider_title) > glv[self.custom_udid]["xhs_spider_config"]["spiderRecordNum"]:
                    logger.warning("抓取的数量>配置的数量")
                    break
                # 相同的次数
                if same_count > 5:
                    logger.warning(f"same_count:{same_count}")
                    break
                start_count = len(already_spider_title)
                # 开始抓取作者的笔记
                record_obj_list = self.device_session.find_all_by_xpath(".//android.widget.FrameLayout[ends-with(@content-desc,'赞，')]")
                logger.warning(f"record_obj_list:{record_obj_list}")

                if len(record_obj_list) > 0:
                    for record_obj in record_obj_list:
                        try:
                            # 判断是否已经抓过
                            content_desc = record_obj.get_attribute("content-desc")
                            if content_desc in already_spider_title:
                                continue
                            already_spider_title[content_desc] = 1

                            # 点进去进行抓取
                            record_obj.click()

                            share_url, other_record_id = self.parse_share_url()

                            # 判断是否已经抓过
                            # url = self.domain + "/grab/repeat-grab-record"
                            # repeat_req = {
                            #     "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
                            #     "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
                            #     "userId": grab_user["userId"],
                            #     "otherRecordId": other_record_id,
                            #     "source": 5,
                            #     "token": "maotai"
                            # }
                            #
                            # is_repeat = common_service.comm_post(url, repeat_req, 1)
                            #
                            # if is_repeat:
                            #     logging.debug("帖子重复")
                            #     CommonMobileUtils.back(self.device_session, 1)
                            #     continue

                            page_source = self.device_session.get_page_source()
                            if "moreOperateIV" in page_source:
                                parse_res = self.parse_record_pic_word(grab_user)
                            else:
                                parse_res = self.parse_record_video(grab_user)
                            if parse_res is None:
                                logging.debug("解析帖子为None")
                                CommonMobileUtils.back(self.device_session, 1)
                                continue

                            parse_res["url"] = share_url
                            parse_res["otherRecordId"] = other_record_id
                            parse_res["searchWord"] = "定向采集某作者"
                            parse_res["spiderRoundFlag"] = glv[self.custom_udid]["xhs_spider_config"]["spiderRoundFlag"]
                            parse_res["grabTaskId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"]
                            parse_res["grabTaskInputId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"]
                            parse_res["status"] = glv[self.custom_udid]["xhs_spider_config"]["isVideoToText"]

                            # 添加帖子详情
                            url = self.domain + "/grab/add-grab-record"
                            record_res = common_service.comm_post(url, parse_res, 1)

                            spider_comments = glv[self.custom_udid]["xhs_spider_config"]["comments"]
                            if parse_res["comment"] > 0 and spider_comments > 0:
                                if "moreOperateIV" in page_source:
                                    self.spider_record_word_comment(record_res)
                                else:
                                    self.spider_record_video_comment(record_res)
                        except Exception as e:
                            exc_type, exc_value, exc_traceback = sys.exc_info()
                            print(f"异常类型: {exc_type.__name__}")
                            print(f"异常信息: {exc_value}")
                            traceback.print_tb(exc_traceback)

                        # 回退到指定页面
                        xpath_list = [".//android.widget.TextView[@text='笔记']"]
                        CommonMobileUtils.back_to_appoint_page_use_xpath_all_have(self.device_session, xpath_list)

                # 是否抓完的标记
                if start_count == len(already_spider_title):
                    same_count = same_count + 1
                self.device_session.swipe(300, 1100, 300, 600)

            # 更新任务状态为完成
            self.update_grab_task_input_status(2)
            # 回退到首页
            self.back_to_index()

    # 更新任务状态
    def update_grab_task_input_status(self,status):
        # 更新任务状态
        req = {
            "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
            "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
            "status": status,
            "token": "maotai"
        }
        url = self.domain + "/grab/update-task"
        common_service.comm_post(url, req, 2)

    # 抓取话题词的浏览次数
    def update_topic_word_browse_count(self,spider_config_list):
        # spider_config_list = [{"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [], "title": "一键更新：雾化器区别", "action": "更新话题词数据", "actionList": [], "spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "", "spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1, "commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 1, "isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0, "lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "", "recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "", "dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "雾1化2器3区4别", "mingdaoId": "", "grabTaskId": 57416, "grabTaskInputId": 60212, "grabUser": None, "grabRecordDetail": None, "noticeWecom": None, "token": ""}, {"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [], "title": "一键更新：高性价比雾化器", "action": "更新话题词数据", "actionList": [], "spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "", "spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1, "commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 1, "isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0, "lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "", "recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "", "dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "高性价比雾化器", "mingdaoId": "", "grabTaskId": 57418, "grabTaskInputId": 60214, "grabUser": None, "grabRecordDetail": None, "noticeWecom": None, "token": ""}, {"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [], "title": "一键更新：雾化器怎么买", "action": "更新话题词数据", "actionList": [], "spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "", "spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1, "commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 1, "isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0, "lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "", "recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "", "dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "雾2化3器4怎5么买", "mingdaoId": "", "grabTaskId": 57420, "grabTaskInputId": 60216, "grabUser": None, "grabRecordDetail": None, "noticeWecom": None, "token": ""}, {"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [], "title": "一键更新：雾化器自制可以加药吗", "action": "更新话题词数据", "actionList": [], "spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "", "spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1, "commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 1, "isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0, "lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "", "recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "", "dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "雾化器自制可以加药吗", "mingdaoId": "", "grabTaskId": 57446, "grabTaskInputId": 60242, "grabUser": None, "grabRecordDetail": None, "noticeWecom": None, "token": ""}]

        # 更新大于28个字的话题词的浏数为0
        need_spider_config_list = self.update_topic_gt_28(spider_config_list)
        if len(need_spider_config_list) == 0:
            return
        url = self.domain + "/grab/update-search-word"
        for spider_config in need_spider_config_list:
            topic_word = spider_config["input"]
            # 回退到首页
            self.back_to_index()
            # 设置全局变量
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)
            # 点击搜索按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/i4s' and @content-desc='搜索']","click")
            # 重置输入关键词
            self.device_session.find_by_xpath(".//android.widget.EditText").input("")
            # 输入关键词
            self.device_session.find_by_xpath(".//android.widget.EditText").input(topic_word)
            # 点击搜索按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/fou' and @text='搜索']", "click")
            # 点击话题按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='话题']", "click")
            time.sleep(1)
            # 解析多少浏览量
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            topic_word_obj_list = etree_xml.xpath(".//android.widget.TextView[@text='" + topic_word + "']/../..", namespaces={'x': 'urn:h17-org:v3'})
            browse_count = 0
            topic_discuss_count = 0
            if len(topic_word_obj_list) > 0:
                topic_html = etree.tostring(topic_word_obj_list[0], encoding="utf-8").decode("utf-8")
                topic_match = re.search(r'\"([^\"]*?)浏览', topic_html)
                if topic_match:
                    browse_count = ToolService.convert_l_c_c(topic_match.group(1))
                discuss_match = re.search(r'¥(.*?)讨论', topic_html)
                if discuss_match:
                    topic_discuss_count = ToolService.convert_l_c_c(discuss_match.group(1))

            update_grab_search_word = {}
            update_grab_search_word["source"] = 5
            update_grab_search_word["word"] = topic_word
            update_grab_search_word["isTopicWord"] = 1
            update_grab_search_word["browseCount"] = browse_count
            update_grab_search_word["topicDiscussCount"] = topic_discuss_count
            update_grab_search_word["lastBrowseCountUpdated"] = int(time.time())
            update_grab_search_word["grabTaskId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"]
            update_grab_search_word["grabTaskInputId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"]
            update_grab_search_word["token"] = "maotai"
            print(update_grab_search_word)
            common_service.comm_post(url, update_grab_search_word, 1)
            # 更新任务状态为完成
            self.update_grab_task_input_status(2)

        # 回退到首页
        self.back_to_index()


    def update_topic_gt_28(self, spider_config_list):
        need_spider_config_list = []
        url = self.domain + "/grab/update-search-word"
        for spider_config in spider_config_list:
            # 设置全局变量
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)
            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]
            if len(search_word) > 28:
                update_grab_search_word = {}
                update_grab_search_word["source"] = 5
                update_grab_search_word["word"] = search_word
                update_grab_search_word["isTopicWord"] = 1
                update_grab_search_word["browseCount"] = 0
                update_grab_search_word["lastBrowseCountUpdated"] = int(time.time())
                update_grab_search_word["grabTaskId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"]
                update_grab_search_word["grabTaskInputId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"]
                update_grab_search_word["token"] = "maotai"
                common_service.comm_post(url, update_grab_search_word, 1)
            else:
                need_spider_config_list.append(spider_config)

        return need_spider_config_list

    def spider_record(self, spider_config_list):
        # spider_config_list = [{"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [],"title": "一键更新：雾化器区别", "action": "更新话题词数据", "actionList": [],"spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "","spiderRecordNum": 20, "isSpiderTopicWord": 0, "likesGt": 0, "comments": 20,"commentsGt": 0, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 3,"isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0,"lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordPosition":"", "recordPublishTime":"一天内", "recordType": "","recordSort": "最新","recordRange":"", "startTime": "", "endTime": "", "dailyStartTime": "","dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "学习机", "mingdaoId": "","grabTaskId": 57416, "grabTaskInputId": 60212, "grabUser": None,"grabRecordDetail": None, "noticeWecom": None, "token": ""},{"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [],"title": "一键更新：高性价比雾化器", "action": "更新话题词数据", "actionList": [],"spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "","spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1,"commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 3,"isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0,"lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "","recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "","dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "高性价比雾化器","mingdaoId": "", "grabTaskId": 57418, "grabTaskInputId": 60214, "grabUser": None,"grabRecordDetail": None, "noticeWecom": None, "token": ""}]

        for spider_config in spider_config_list:
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)

            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]

            # 从首页点搜索进去输入搜索词
            self.click_input_from_index_search_button(search_word)

            self.search_filter()

            # 解析更新索引关键词
            index_words = self.parse_index_word()
            url = self.domain + "/grab/update-search-word"
            req = {
                "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
                "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
                "source": 5,
                "word": search_word,
                "indexWords": index_words,
                "token": "maotai"
            }
            common_service.comm_post(url, req, 1)

            already_spider_title = {}
            error_count = 0
            while True:
                if error_count > 2:
                    break
                if len(already_spider_title) > 500:
                    # 更新任务状态为完成
                    self.update_grab_task_input_status(2)
                    break
                page_source = self.device_session.get_page_source()
                if "无更多内容" in page_source:
                    # 更新任务状态为完成
                    self.update_grab_task_input_status(2)
                    break
                is_complete = False
                try:
                    record_obj_list = self.device_session.find_all_by_xpath(".//androidx.viewpager.widget.ViewPager//androidx.recyclerview.widget.RecyclerView/android.widget.FrameLayout")
                    if len(record_obj_list) == 0:
                        error_count = error_count + 1
                        continue
                    etree_xml = etree.fromstring(page_source.encode('utf-8'))
                    record_index = -1

                    for record_obj in record_obj_list:
                        record_index = record_index + 1
                        title_list = etree_xml.xpath(f".//androidx.viewpager.widget.ViewPager//androidx.recyclerview.widget.RecyclerView/android.widget.FrameLayout[@index='{record_index}']//android.widget.TextView[1]/@text",namespaces={'x': 'urn:h17-org:v3'})
                        if len(title_list) == 0:
                            continue

                        title = title_list[0]
                        print(title)
                        title_md5 = hashlib.md5(title.encode(encoding="UTF-8")).hexdigest()
                        if title_md5 in already_spider_title:
                            continue
                        already_spider_title[title_md5] = 1
                        # 点击帖子
                        record_obj.click()

                        # 抓取具体的详情
                        is_complete = self.spider_record_in_detail(search_word)
                        if is_complete:
                            break

                        # 回到搜索列表页
                        self.back_to_search_index()

                except Exception as e:
                    error_count = error_count + 1
                    print(e)

                if is_complete:
                    break

                # 回到搜索列表页
                self.back_to_search_index()

                # 向上滚动
                self.device_session.swipe(170, 1100, 170, 600)

    def search_filter(self):
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='全部']", "click")
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='重置']", "click")

        if glv[self.custom_udid]["xhs_spider_config"]["recordType"] is not None and glv[self.custom_udid]["xhs_spider_config"]["recordType"] != "":
            CommonMobileUtils.click_use_xpath(self.device_session,f".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[@text='{glv[self.custom_udid]["xhs_spider_config"]["recordType"]}']", "click")
        if glv[self.custom_udid]["xhs_spider_config"]["recordSort"] is not None and glv[self.custom_udid]["xhs_spider_config"]["recordSort"] != "":
            CommonMobileUtils.click_use_xpath(self.device_session, f".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[@text='{glv[self.custom_udid]["xhs_spider_config"]["recordSort"]}']","click")
        if glv[self.custom_udid]["xhs_spider_config"]["recordPublishTime"] is not None and glv[self.custom_udid]["xhs_spider_config"]["recordPublishTime"] != "":
            CommonMobileUtils.click_use_xpath(self.device_session, f".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[@text='{glv[self.custom_udid]["xhs_spider_config"]["recordPublishTime"]}']","click")
        if glv[self.custom_udid]["xhs_spider_config"]["recordRange"] is not None and glv[self.custom_udid]["xhs_spider_config"]["recordRange"] != "":
            CommonMobileUtils.click_use_xpath(self.device_session, f".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[@text='{glv[self.custom_udid]["xhs_spider_config"]["recordRange"]}']","click")
        if glv[self.custom_udid]["xhs_spider_config"]["recordPosition"] is not None and glv[self.custom_udid]["xhs_spider_config"]["recordPosition"] != "":
            CommonMobileUtils.click_use_xpath(self.device_session, f".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[@text='{glv[self.custom_udid]["xhs_spider_config"]["recordPosition"]}']","click")

        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='全部']", "click")

    def click_input_from_index_search_button(self,search_word):
        # 回退到首页
        self.back_to_index()
        # 点击右上角的搜索标记
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.Button[@content-desc='搜索']","click")
        edit_text_obj = self.device_session.find_by_xpath(".//android.widget.EditText")
        # 重置输入关键词
        edit_text_obj.input("")
        # 输入关键词
        edit_text_obj.input(search_word)
        # 点击搜索按钮
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.Button[@text='搜索']", "click")

    def back_to_search_index(self):
        # 搜索列表页标记
        xpath_list = [
            ".//android.widget.TextView[@text='搜索']",
            ".//android.widget.TextView[@text='全部']"
        ]
        CommonMobileUtils.back_to_appoint_page_use_xpath_all_have(self.device_session, xpath_list)

    def parse_index_word(self):
        last_index_word = ""
        index_word_list = []
        while True:
            category_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@text='综合']/../../..//android.widget.TextView")
            if len(category_obj_list) == 0:
                break
            bound = []

            for category_obj in category_obj_list:
                bound = category_obj.get_bounding()
                index_word = category_obj.get_attribute("text")
                if index_word not in index_word_list:
                    index_word_list.append(index_word)

            if len(index_word_list) == 0:
                break

            if last_index_word != "" and last_index_word == index_word_list[-1]:
                break

            last_index_word = index_word_list[-1]

            x = bound[0] - 200
            if x <= 0:
                x = 1
            self.device_session.swipe(bound[0], bound[1], x, bound[1])

        if "综合" in index_word_list:
            index_word_list.remove("综合")
        return ",".join(index_word_list)

    def parse_share_url(self):
        page_source = self.device_session.get_page_source()
        if "moreOperateIV" in page_source:
            # 点击分享按钮
            self.device_session.find_by_id("com.xingin.xhs:id/moreOperateIV").click()
        else:
            # 点击分享按钮
            self.device_session.find_by_xpath(".//android.widget.Button[starts-with(@content-desc, '分享')]").click()
            # 移动漏出复制链接
            bound = self.device_session.find_by_xpath(".//android.widget.Button[@content-desc='生成分享图']").get_bounding()
            self.device_session.swipe(bound[0], bound[1], bound[0] - 300, bound[1])

        # 点击复制链接
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@content-desc='复制链接']","click")
        url = ""
        other_record_id = ""
        match = re.search(r'http://(.*?)(，|\s+)', self.device_session.get_clipboard())
        if match:
            url = "http://" + match.group(1)
            common_utils = CommonUtils()
            url = common_utils.get_redirected_url(url)

            match_other_record_id = re.search(r'https://www.xiaohongshu.com/discovery/item/(.*?)\?.*', url)
            if match_other_record_id:
                other_record_id = match_other_record_id.group(1)
        print(url)
        print(other_record_id)

        return url, other_record_id

    def spider_record_in_detail(self,search_word):
        page_source = self.device_session.get_page_source()
        parse_res = {}

        share_url, other_record_id = self.parse_share_url()
        if other_record_id == "":
            print("解析分享链接错误")
            return False

        if "moreOperateIV" in page_source:
            parse_res = self.parse_record_pic_word(None)
        else:
            parse_res = self.parse_record_video(None)

        print(parse_res)
        if parse_res is None:
            return False

        parse_res["url"] = share_url
        parse_res["otherRecordId"] = other_record_id
        parse_res["searchWord"] = search_word
        parse_res["spiderRoundFlag"] = glv[self.custom_udid]["xhs_spider_config"]["spiderRoundFlag"]
        parse_res["grabTaskId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"]
        parse_res["grabTaskInputId"] = glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"]
        parse_res["status"] = glv[self.custom_udid]["xhs_spider_config"]["isVideoToText"]

        if glv[self.custom_udid]["xhs_spider_config"]["isAnalyzeAddress"] == 1:
            parse_res["status"] = 4
        try:
            # 添加帖子详情
            url = self.domain + "/grab/add-grab-record"
            record_res = common_service.comm_post(url, parse_res, 1)
            spider_comments = glv[self.custom_udid]["xhs_spider_config"]["comments"]
            if parse_res["comment"] > 0 and spider_comments > 0:
                if "moreOperateIV" in page_source:
                    self.spider_record_word_comment(record_res)
                else:
                    self.spider_record_video_comment(record_res)
            return record_res["isComplete"]
        except Exception as e:
            print(e)
            return False

    def parse_record_pic_word(self, grab_user):
        comment = 0
        xpath_str = ".//android.widget.Button[starts-with(@content-desc, '评论')]"
        comment_button_obj_list = self.device_session.find_all_by_xpath(xpath_str)

        if len(comment_button_obj_list) > 0:
            text = comment_button_obj_list[0].get_attribute("content-desc")
            if text is not None:
                match = re.search(r'(\d+)', text)
                if match:
                    comment = int(match.group(1))

        print(comment)
        if comment < glv[self.custom_udid]["xhs_spider_config"]["commentsGt"]:
            return None

        if grab_user is None:
            # 点击作者的名字
            self.device_session.find_by_id("com.xingin.xhs:id/nickNameTV").click()
            # 解析作者的信息
            grab_user = self.parse_user()
            CommonMobileUtils.back(self.device_session, 1)

        text_view_objs = self.device_session.find_all_by_xpath(".//android.widget.LinearLayout[@resource-id='com.xingin.xhs:id/noteContentLayout']/../..//android.widget.TextView")
        print(text_view_objs)
        title = ""
        contents = ""
        if len(text_view_objs)>=2:
            title = text_view_objs[0].get_attribute("text")
            contents = text_view_objs[1].get_attribute("text")

        if len(text_view_objs)==1:
            contents = text_view_objs[0].get_attribute("text")

        # 解析标签
        topicWordList = []
        if glv[self.custom_udid]["xhs_spider_config"]["isSpiderTopicWord"] == 1:
            topicWordList = re.findall(r'#([A-Za-z\u4e00-\u9fff]{1,10})', contents)
        # 解析作者名字
        user_name = self.device_session.find_by_id("com.xingin.xhs:id/nickNameTV").get_attribute("text")
        # 解析帖子内容
        parse_res = {
            "source": 5,
            "searchWord": "",
            "title": title,
            "status": 0,
            "type": 0,
            "userName": user_name,
            "coverPic": "",
            "contents": contents,
            "likes": 0,
            "collect": 0,
            "comment": comment,
            "url": "",
            "otherRecordId": "",
            "topicWordList": topicWordList,
            "inCity": "",
            "updated": 0,
            "grabUser": grab_user,
            "token": "maotai"
        }
        print(parse_res)
        i = 0
        while True:
            i = i + 1
            if i > 2:
                break
            # 解析时间和地点
            time_and_add_objs = self.device_session.find_all_by_xpath(".//androidx.recyclerview.widget.RecyclerView//android.view.View[last()]")
            if len(time_and_add_objs)>0:
                text = time_and_add_objs[0].get_attribute("content-desc")
                parse_res["inCity"] = self.get_city(text)
                parse_res["updated"] = self.cal_updated(text)
                if len(parse_res["inCity"])>0 or parse_res["updated"]>0:
                    break
            self.device_session.swipe(170, 1100, 170, 400)

        collect_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'收藏')]")
        if len(collect_objs)>0:
            match = re.search(r'(\d+)', collect_objs[0].get_attribute("content-desc"))
            if match:
                parse_res["collect"] = int(match.group(1))
        like_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'点赞')]")
        if len(like_objs)>0:
            match = re.search(r'(\d+)', like_objs[0].get_attribute("content-desc"))
            if match:
                parse_res["likes"] = int(match.group(1))

        print(parse_res)
        return parse_res

    def spider_record_word_comment(self, record_res):
        user_name = self.device_session.find_by_id("com.xingin.xhs:id/nickNameTV").get_attribute("text")
        self.spider_comment(record_res,user_name)

    def spider_comment(self,record_res,user_name):
        spider_comments = glv[self.custom_udid]["xhs_spider_config"]["comments"]
        already_spider_comment = {}
        already_spider_num = 0
        # 解析时间和地点以及评论
        i = 0
        while True:
            i = i + 1
            if i > 100:
                break
            comments = []
            # 解析评论
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            comment_obj_list = etree_xml.xpath(".//android.widget.TextView[substring(@text, string-length(@text) - string-length('回复') + 1) = '回复']/..", namespaces={'x': 'urn:h17-org:v3'})
            for comment_obj in comment_obj_list:
                comment_reply = {"userName": "", "contents": "", "time": 0, "place": "", "likes": 0}
                desc_list = comment_obj.xpath(".//android.widget.TextView/@text", namespaces={'x': 'urn:h17-org:v3'})
                if len(desc_list) == 0:
                    continue
                comment_reply["userName"] = desc_list[0]

                if user_name in comment_reply["userName"]:
                    continue

                contents = ""
                for desc in desc_list:
                    if "回复" in desc:
                        contents = desc.replace("回复", "")
                        break

                contents_md5 = hashlib.md5(contents.encode(encoding="UTF-8")).hexdigest()
                if contents_md5 in already_spider_comment:
                    continue
                already_spider_comment[contents_md5] = 1

                content, time_stamp = self.handle_content_and_time(contents)
                comment_reply["contents"] = content
                comment_reply["time"] = time_stamp
                comment_reply["place"] = self.get_city(contents)
                if len(comment_reply["place"]) > 0:
                    comment_reply["contents"] = content.replace(comment_reply["place"], "")

                comment_reply["likes"] = 0

                comment_grab_user = None
                if glv[self.custom_udid]["xhs_spider_config"]["isSpiderCommentUser"] > 0:
                    comment_grab_user_obj_list = self.device_session.find_all_by_xpath(f".//android.widget.TextView[@text='{comment_reply["userName"]}']")
                    if len(comment_grab_user_obj_list) > 0:
                        comment_grab_user_obj_list[0].click()
                        comment_grab_user = self.parse_user()
                        CommonMobileUtils.back(self.device_session, 1)
                comments.append({"grabComment": comment_reply, "grabUser": comment_grab_user})

            # 添加评论
            if len(comments) > 0:
                print(comments)
                url = self.domain + "/grab/add-comment"
                req = {
                    "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
                    "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
                    "grabRecordDetailMingdaoId": record_res["mingdaoId"],
                    "grabRecordDetailId": record_res["grabId"],
                    "grabUserGrabId": record_res["grabUserGrabId"],
                    "grabUserMingdaoId": record_res["grabUserMingdaoId"],
                    "grabCommentList": comments,
                    "token": "maotai"
                }
                common_service.comm_post(url, req, 1)

            already_spider_num = already_spider_num + len(comments)

            if already_spider_num > spider_comments:
                break

            is_end = False
            end_flag_list = ["到底了", "还没有评论", "快来抢首评"]
            for end_flag in end_flag_list:
                if end_flag in page_source:
                    is_end = True
                    break
            if is_end:
                break
            self.device_session.swipe(170, 1100, 170, 400)

    def parse_record_video(self, grab_user):
        comment = 0
        collect_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'评论')]")
        if len(collect_objs)>0:
            match = re.search(r'(\d+)', collect_objs[0].get_attribute("content-desc"))
            if match:
                comment = int(match.group(1))
        if comment < glv[self.custom_udid]["xhs_spider_config"]["commentsGt"]:
            return None

        if grab_user is None:
            # 点击作者的名字
            self.device_session.find_by_id("com.xingin.xhs:id/matrixNickNameView").click()
            # 解析作者的信息
            grab_user = self.parse_user()
            CommonMobileUtils.back(self.device_session, 1)

        # 内容
        title = self.device_session.find_by_id("com.xingin.xhs:id/noteContentText").get_attribute("text").replace("...", "").replace("展开", "")
        logger.debug(f"{title}")

        contents = self.device_session.find_by_id("com.xingin.xhs:id/noteContentText").get_attribute("content-desc")
        logger.debug(f"{contents}")

        # 解析标签
        topicWordList = []
        if glv[self.custom_udid]["xhs_spider_config"]["isSpiderTopicWord"] == 1:
            topicWordList = re.findall(r'#([A-Za-z\u4e00-\u9fff]{1,10})', contents)
        # 解析作者名字
        user_name = self.device_session.find_by_id("com.xingin.xhs:id/matrixNickNameView").get_attribute("text")
        logger.debug(f"{user_name}")

        # 解析帖子内容
        parse_res = {
            "source": 5,
            "searchWord": "",
            "title": title,
            "status": 0,
            "type": 1,
            "userName": user_name,
            "coverPic": "",
            "contents": contents,
            "likes": 0,
            "collect": 0,
            "comment": comment,
            "url": "",
            "otherRecordId": "",
            "topicWordList": topicWordList,
            "inCity": "",
            "updated": 0,
            "grabUser": grab_user,
            "token": "maotai"
        }

        collect_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'收藏')]")
        if len(collect_objs)>0:
            match = re.search(r'(\d+)', collect_objs[0].get_attribute("content-desc"))
            if match:
                parse_res["collect"] = int(match.group(1))

        like_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'点赞')]")
        if len(like_objs)>0:
            match = re.search(r'(\d+)', like_objs[0].get_attribute("content-desc"))
            if match:
                parse_res["likes"] = int(match.group(1))

        # 计算更新时间戳
        parse_res["updated"] = self.cal_updated(contents)

        print(parse_res)
        return parse_res

    def spider_record_video_comment(self, record_res):
        user_name = self.device_session.find_by_id("com.xingin.xhs:id/matrixNickNameView").get_attribute("text")

        comment = 0
        collect_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[starts-with(@content-desc,'评论')]")
        if len(collect_objs)>0:
            match = re.search(r'(\d+)', collect_objs[0].get_attribute("content-desc"))
            if match:
                comment = int(match.group(1))
        if comment == 0:
            return
        collect_objs[0].click()

        self.spider_comment(record_res,user_name)
        # 关闭评论
        CommonMobileUtils.back(self.device_session, 1)

    def parse_user(self):
        grab_user = {
            "source": 5,
            "userId": "",
            "userName": "",
            "type": 0,
            "typeDesc": "",
            "info": "",
            "tags": "",
            "likes": 0,
            "fans": 0,
            "follow": 0,
            "collect": 0,
            "notesNum": 0,
            "bloggerLink": ""
        }
        grab_user["userId"] = self.device_session.find_by_xpath(".//android.widget.TextView[starts-with(@text, '小红书号：')]").get_attribute("text").replace("小红书号：", "")
        grab_user["userName"] = self.device_session.find_by_xpath(".//android.view.View[starts-with(@content-desc, '头像,')]").get_attribute("content-desc").replace("头像,", "")

        desc_objs = self.device_session.find_all_by_xpath(".//android.widget.Button[ends-with(@content-desc,'关注')]/../../../preceding-sibling::android.widget.LinearLayout[1]//android.widget.TextView")
        desc_list = []
        for desc_obj in desc_objs:
            desc_list.append(desc_obj.get_attribute("text"))
        if len(desc_list)>0:
            grab_user["info"] = desc_list.pop(0)
            grab_user["tags"] = ', '.join(desc_list)
        grab_user["follow"] = ToolService.convert_l_c_c(self.device_session.find_by_xpath(".//android.widget.Button[ends-with(@content-desc, '关注')]").get_attribute("content-desc").replace("关注", ""))
        grab_user["fans"] = ToolService.convert_l_c_c(self.device_session.find_by_xpath(".//android.widget.Button[ends-with(@content-desc, '粉丝')]").get_attribute("content-desc").replace("粉丝", ""))

        # 点击获赞与收藏
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.Button[ends-with(@content-desc, '获赞与收藏')]","click")
        try:
            grab_user["likes"] = ToolService.convert_l_c_c(self.device_session.find_by_xpath(".//android.widget.TextView[@text='当前获得点赞数']/following-sibling::android.widget.TextView[1]").get_attribute("text"))
            grab_user["collect"] = ToolService.convert_l_c_c(self.device_session.find_by_xpath(".//android.widget.TextView[@text='当前获得收藏数']/following-sibling::android.widget.TextView[1]").get_attribute("text"))
            grab_user["notesNum"] = ToolService.convert_l_c_c(self.device_session.find_by_xpath(".//android.widget.TextView[@text='当前发布笔记数']/following-sibling::android.widget.TextView[1]").get_attribute("text"))
            # 点击我知道了
            CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.TextView[@text='我知道了']","click")
        except Exception as e:
            print(e)

        print(grab_user)
        return grab_user

    def grab_search_word(self, spider_config_list):
        # spider_config_list = [{"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [],"title": "一键更新：雾化器区别", "action": "更新话题词数据", "actionList": [],"spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "","spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1,"commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 3,"isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0,"lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "","recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "","dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "学习机", "mingdaoId": "","grabTaskId": 57416, "grabTaskInputId": 60212, "grabUser": None,"grabRecordDetail": None, "noticeWecom": None, "token": ""},{"pushLocationId": 0, "wordPackageRowId": "", "mingdaoGrabSearchWordList": [],"title": "一键更新：高性价比雾化器", "action": "更新话题词数据", "actionList": [],"spiderRoundFlag": "****************", "source": "小红书", "spiderInput": "","spiderRecordNum": 1, "isSpiderTopicWord": 0, "likesGt": 1, "comments": 1,"commentsGt": 1, "statusStr": "", "typeStr": "", "spiderSearchWordLevel": 3,"isSpiderCommentUser": 0, "isAnalyzeAddress": 0, "isVideoToText": 0,"lightWordSpiderIsLongTail": 0, "topicWordSpiderIsLongTail": 0, "recordType": "","recordSort": "", "startTime": "", "endTime": "", "dailyStartTime": "","dailyEndTime": "", "xhsGrabTagAccountId": "", "input": "高性价比雾化器","mingdaoId": "", "grabTaskId": 57418, "grabTaskInputId": 60214, "grabUser": None,"grabRecordDetail": None, "noticeWecom": None, "token": ""}]
        # 点击右上角的搜索标记
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@content-desc='搜索']","click")
        for spider_config in spider_config_list:
            # 设置全局变量
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)

            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]
            global recursion_spider_search_word_count
            recursion_spider_search_word_count = 0
            self.spider_word_recursion([search_word], 0)

            # 更新任务状态为完成
            self.update_grab_task_input_status(2)

        # 回退到首页
        self.back_to_index()

    def spider_word_recursion(self, search_words, level):
        global recursion_spider_search_word_count
        recursion_spider_search_word_count = recursion_spider_search_word_count + 1
        if recursion_spider_search_word_count > glv[self.custom_udid]["xhs_spider_config"]["spiderSearchWordLevel"]:
            return
        for search_word in search_words:
            search_word_list = self.spider_search_word(search_word, level)
            if len(search_word_list) == 0:
                continue
            if search_word in search_word_list:
                search_word_list.remove(search_word)
            self.spider_word_recursion(search_word_list, level + 1)

    def spider_search_word(self, search_word, level):
        edit_text_obj = self.device_session.find_by_xpath(".//android.widget.EditText")
        # 重置输入关键词
        edit_text_obj.input("")
        # 输入关键词
        edit_text_obj.input(search_word)
        # 展示所有的联想词
        CommonMobileUtils.back(self.device_session, 1)
        search_word_obj_list = self.device_session.find_all_by_xpath(".//android.widget.Button//android.widget.TextView")
        search_words = []
        grab_search_word_child_list = []
        # 联想的搜索词
        for search_word_obj in search_word_obj_list:
            grab_search_word_child = {}
            grab_search_word_child["word"] = search_word_obj.get_attribute("text")
            grab_search_word_child["level"] = level + 1
            grab_search_word_child_list.append(grab_search_word_child)
            search_words.append(grab_search_word_child["word"])

        if len(grab_search_word_child_list) == 0:
            return grab_search_word_child_list

        req = {
            "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
            "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
            "source": 5,
            "parentWord": search_word,
            "level": level,
            "grabSearchWordChild": grab_search_word_child_list,
            "token": "maotai"
        }
        url = self.domain + "/grab/add-search-word"
        logger.warning(f"{url}{req}")

        common_service.comm_post(url, req, 1)
        return search_words

    def update_search_word_notes_num(self, spider_config_list):
        for spider_config in spider_config_list:
            ToolService.build_common_var(self.custom_udid, "xhs_spider_config", spider_config)

            search_word = glv[self.custom_udid]["xhs_spider_config"]["input"]

            # 从首页点搜索进去输入搜索词
            self.click_input_from_index_search_button(search_word)

            self.search_filter()

            page_source = self.device_session.get_page_source()
            if "没有找到相关内容" not in page_source:
                exec_time_config_set = glv[self.custom_udid]["xhs_spider_config"]["swipeTime"]
                start_time = time.time()
                last_flag = ""
                exec_time = 0

                while True:
                    if exec_time > exec_time_config_set:
                        break
                    round_exec_time = time.time() - start_time
                    if round_exec_time > 10:
                        exec_time = exec_time + 10
                        start_time = time.time()
                        last_flag_str = self.device_session.find_by_xpath(".//androidx.recyclerview.widget.RecyclerView//android.widget.TextView[1]").get_attribute("text")
                        last_flag_md5 = hashlib.md5(last_flag_str.encode(encoding="UTF-8")).hexdigest()
                        if last_flag == last_flag_md5:
                            break
                        last_flag = last_flag_md5
                    self.device_session.swipe(300, 1100, 300, 50, duration=50, stop_pause=0)

                logger.warning(f"总执行时间：{exec_time}")
            else:
                exec_time_config_set = glv[self.custom_udid]["xhs_spider_config"]["swipeTime"]
                exec_time = 0

            swipeCountNotesNum = exec_time*10
            isSwipeBottom = 1
            if exec_time > exec_time_config_set:
                isSwipeBottom = 0

            update_grab_search_word = {
                "lastSwipeCountNotesNumUpdated": int(time.time()),
                "swipeCountNotesNum": swipeCountNotesNum,
                "isSwipeBottom": isSwipeBottom,
                "source": 5,
                "word": search_word,
                "lastNotesNumUpdated": int(time.time()),
                "grabTaskId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskId"],
                "grabTaskInputId": glv[self.custom_udid]["xhs_spider_config"]["grabTaskInputId"],
                "token": "maotai"
            }

            url = self.domain + "/grab/update-search-word"
            common_service.comm_post(url, update_grab_search_word, 1)

            # 更新任务状态为完成
            self.update_grab_task_input_status(2)

    def close(self):
        ids = ["com.xingin.xhs:id/dao"]
        for idstr in ids:
            page_source = self.device_session.get_page_source()
            if idstr in page_source:
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='" + idstr + "']", "click")

    def back_to_index(self):
        self.close()
        xpath_list = [".//android.view.ViewGroup[@content-desc='首页']",
                      ".//android.view.ViewGroup[@content-desc='我']"]
        CommonMobileUtils.back_to_appoint_page_use_xpath_all_have(self.device_session,xpath_list)
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.view.ViewGroup[@content-desc='首页']", "click")

    def cal_updated(self,text):
        updated = 0
        try:
            if "昨天" in text:
                today = datetime.date.today()
                oneday = datetime.timedelta(days=1)
                yesterday = today - oneday
                date_obj = time.strptime(str(yesterday), "%Y-%m-%d")
                updated = int(time.mktime(date_obj))
            elif "今天" in text:
                updated = int(time.time())
            else:
                    match = re.search(r'(\d+-\d+-\d+)', text)
                    if match:
                        date_obj = time.strptime(match.group(1), "%Y-%m-%d")
                        updated = int(time.mktime(date_obj))
                    else:
                        match = re.search(r'(\d+-\d+)', text)
                        if match:
                            date_obj = time.strptime(str(datetime.datetime.now().year) + "-" + match.group(1), "%Y-%m-%d")
                            updated = int(time.mktime(date_obj))
        except Exception as e:
            print(e)
        return updated

    def handle_content_and_time(self,contents):
        updated = 0
        try:
            reg = r"(\d+)小时前"
            match = re.search(reg, contents)
            if match:
                replace_content = re.sub(reg, "", contents)
                return replace_content, int(time.time())

            reg = r"(\d{4}-\d{2}-\d{2})"
            match = re.search(reg, contents)
            if match:
                time_str = match.group(1)
                time_array = time.strptime(time_str, "%Y-%m-%d")
                updated = int(time.mktime(time_array))
                replace_content = re.sub(reg, "", contents)
                return replace_content, updated

            reg = r"(\d+)天前"
            match = re.search(reg, contents)
            if match:
                replace_content = re.sub(reg, "", contents)
                updated = int(time.time()) - (int(match.group(1)) * 24 * 60 * 60)
                return replace_content, updated

            reg = r"(昨天)"
            match = re.search(reg, contents)
            if match:
                updated = int(time.time()) - (24 * 60 * 60)
                replace_content = re.sub(reg, "", contents)
                return replace_content, updated

            reg = r"(\d{2}:\d{2})"
            match = re.search(reg, contents)
            if match:
                updated = int(time.time())
                replace_content = re.sub(reg, "", contents)
                return replace_content, updated

            reg = r"(\d{2}-\d{2})"
            match = re.search(reg, contents)
            if match:
                time_str = "2024-" + match.group(1)
                time_array = time.strptime(time_str, "%Y-%m-%d")
                updated = int(time.mktime(time_array))
                replace_content = re.sub(reg, "", contents)
                return replace_content, updated
        except Exception as e:
            print(e)
        return contents, updated

    def get_city(self,contents):
        city = ""
        reg = r"(陕西|河北|四川|青海|贵州|江西|山西|湖北|河南|黑龙江|海南|湖南|福建|吉林|云南|辽宁|甘肃|台湾|江苏|广东|浙江|安徽|山东|扎兰屯|珲春|东莞|上饶|凌海|乌苏|莱阳|柳州|泸州|长葛|辽阳|鞍山|新余|济宁|吕梁|遵化|泊头|卫辉|玉林|石首|广水|祁阳|宣城|漯河|武冈|泉州|衢州|贵港|恩施|武穴|南京|新密|苗栗|盖州|锡林浩特|邹平|鹤山|双鸭山|建德|霍尔果斯|武安|孝义|哈尔滨|毕节|京山|仪征|铁力|临沂|和田|抚州|宝鸡|昆玉|鹰潭|宜城|华阴|利川|图们|都江堰|高雄|黄石|龙南|库车|莆田|迁安|大庆|抚顺|温州|三门峡|五常|丹江口|麻城|临夏|桃园|可克达拉|常德|德惠|登封|荆州|嘉峪关|牡丹江|丰镇|宜兴|邵武|达州|屏东|佛山|龙港|荣成|承德|百色|邯郸|平泉|康定|南充|泸水|五家渠|钦州|永安|吐鲁番|会理|阳泉|海阳|宜都|洛阳|昭通|高平|内江|嫩江|芜湖|文山|乌鲁木齐|马公|贵溪|枝江|文昌|英德|重庆|景德镇|津市|南雄|中山|滁州|新乐|北流|郑州|通辽|北镇|昌吉|樟树|保山|晋中|青岛|彭州|仁怀|临汾|永城|义马|安康|阿图什|斗六|高州|江油|新沂|梅河口|资兴|北京|宿州|盘州|运城|中卫|监利|玉环|常熟|东方|蒙自|张家界|邹城|保定|安达|石家庄|平顶山|广安|镇江|图木舒克|忻州|肇东|成都|吉林|鸡西|攀枝花|万源|枣庄|界首|罗定|乐昌|海城|株洲|咸阳|桦甸|福安|丹东|清远|天水|吴川|凌源|宁德|雅安|吉安|本溪|清镇|阜康|龙岩|银川|遂宁|德兴|桂林|阿拉山口|长治|老河口|新郑|海林|旬阳|井冈山|江门|天门|新星|瓦房店|彬州|阆中|子长|尚志|安顺|武威|泰州|如皋|临江|余姚|孟州|深圳|博乐|南安|秦皇岛|济南|句容|开封|满洲里|呼伦贝尔|聊城|醴陵|德令哈|霍州|巴中|化州|射洪|陆丰|招远|涟源|阳江|岑溪|昆山|赤水|延安|韶关|肥城|丽水|宣威|龙井|酒泉|衡阳|儋州|温岭|盐城|福鼎|楚雄|阿拉尔|根河|湘乡|太原|溧阳|天长|资阳|连州|大冶|新北|台山|虎林|安陆|乐山|三沙|凯里|天津|福泉|合山|琼海|泰兴|乌兰浩特|巩义|海伦|华蓥|沙湾|石河子|高密|什邡|巴彦淖尔|德州|兴平|珠海|兰州|固原|江山|广州|北票|高安|宁乡|江阴|临海|铁岭|揭阳|云浮|福清|泰安|威海|喀什|河源|应城|莱西|松滋|横州|阿尔山|黔西|五大连池|烟台|包头|伊春|侯马|鄂州|靖江|阿克苏|武汉|苏州|桐乡|汉川|乐平|湛江|磐石|兴义|绥芬河|那曲|竹北|南宁|日喀则|兰溪|巢湖|东宁|淮北|六安|福州|汉中|乐陵|新泰|广汉|三河|张掖|隆昌|蛟河|武夷山|沙河|额尔古纳|南昌|潜山|奎屯|长春|湖州|富锦|昌邑|九江|丰城|廉江|双河|花莲|霍林郭勒|恩平|周口|基隆|肇庆|合作|伊宁|晋城|澄江|安庆|平湖|淮南|沁阳|汕尾|曲阜|南宫|昌都|徐州|萍乡|河津|舟山|张家口|长垣|桂平|丹阳|铜川|神木|黄山|龙口|乳山|吉首|瑞金|北海|灵宝|郴州|潮州|华亭|项城|辛集|栖霞|漳平|赤峰|贵阳|临清|讷河|白银|公主岭|广元|灯塔|西昌|绥化|嘉义|乌兰察布|开原|韶山|汨罗|库尔勒|玉门|拉萨|鄂尔多斯|大石桥|邛崃|安丘|四平|海宁|商丘|锦州|台南|防城港|宜春|辽源|瑞丽|渭南|滦州|河间|瑞昌|西宁|太保|连云港|朴子|葫芦岛|东阳|自贡|大同|明光|河池|齐齐哈尔|宁波|兴宁|西安|定州|五指山|永济|邵阳|平度|日照|彰化|陇南|眉山|调兵山|简阳|弥勒|金华|亳州|凭祥|潜江|马尔康|乐清|平凉|二连浩特|诸暨|石嘴山|雷州|格尔木|茂名|洮南|晋江|南平|平果|益阳|和龙|都匀|青州|德阳|原平|涿州|同江|营口|松原|惠州|启东|洪湖|绵竹|唐山|庐山|荥阳|曲靖|南投|普宁|安阳|黄骅|荔浦|宜宾|黄冈|峨眉山|马鞍山|丽江|铁门关|梅州|白山|呼和浩特|淄博|同仁|延吉|赤壁|许昌|扬中|滨州|北安|焦作|兴仁|龙泉|东营|大连|信宜|汾阳|双辽|南通|海口|林州|仙桃|海安|湘潭|舒兰|宜兰|铜陵|三亚|长沙|克拉玛依|商洛|贺州|普洱|禹州|绍兴|厦门|阜新|鹤岗|鹤壁|白城|怀仁|阿勒泰|嘉兴|宁安|淮安|崇左|朝阳|娄底|哈密|常州|舞钢|抚远|上海|北屯|万宁|广德|寿光|玉溪|定西|十堰|大安|驻马店|海东|廊坊|庆阳|密山|高碑店|梧州|宿迁|六盘水|台北|东台|太仓|庄河|古交|员林|新乡|林芝|阳春|临沧|靖西|濮阳|浏阳|孝感|晋州|潍坊|腾冲|怀化|胡杨河|随州|石狮|无为|敦煌|汝州|东港|滕州|穆棱|杭州|青铜峡|绵阳|大理|山南|盘锦|崇州|香格里拉|衡水|任丘|邢台|敦化|济源|邵东|合肥|通化|诸城|池州|邳州|七台河|洪江|南阳|菏泽|榆树|漳州|遵义|慈溪|岳阳|无锡|襄阳|建瓯|瑞安|邓州|咸宁|兴城|霸州|铜仁|吴忠|扬州|扶余|汕头|来宾|牙克石|凤城|新竹|景洪|东兴|蚌埠|沈阳|枣阳|安宁|赣州|荆门|高邮|莱州|当阳|沅江|朔州|玉树|冷水江|榆林|个旧|永州|禹城|介休|禄丰|耒阳|台州|辉县|三明|茫崖|台东|共青城|水富|安国|胶州|永康|漠河|钟祥|开远|嵊州|佳木斯|四会|宁国|头份|开平|宜昌|阜阳|昆明|新民|台中|义乌|金昌|韩城|深州|兴化|常宁|临湘|黑河|集安|沧州|乌海|桐城|张家港|信阳|灵武|塔城)"
        match = re.search(reg, contents)
        if match:
            city = match.group(1)
        return city

    def get_test_spider_configs(self):
        spider_config_list = [{"pushLocationId":0,"wordPackageRowId":"","mingdaoGrabSearchWordList":[],"title":"汗管瘤 - 0801","action":"定向采集博主","actionList":[],"spiderRoundFlag":"spider2-2025-08-01+14:13:50","source":"小红书","spiderInput":"","spiderRecordNum":100,"isSpiderTopicWord":0,"likesGt":0,"comments":200,"commentsGt":0,"statusStr":"","typeStr":"","spiderSearchWordLevel":3,"isSpiderCommentUser":0,"isAnalyzeAddress":0,"isVideoToText":0,"lightWordSpiderIsLongTail":0,"topicWordSpiderIsLongTail":0,"recordType":"","recordSort":"","recordPosition":"","recordPublishTime":"","recordRange":"","swipeTime":300,"startTime":"","endTime":"","dailyStartTime":"","dailyEndTime":"","xhsGrabTagAccountId":"","input":"**********","mingdaoId":"","grabTaskId":77457,"grabTaskInputId":84343,"grabUser":None,"grabRecordDetail":None,"noticeWecom":None,"token":""}]
        return spider_config_list