# 小红书笔记统计功能说明

## 功能概述

这个功能可以自动统计小红书账号的笔记数据，包括：
- 笔记标题
- 阅读数（小眼睛数据）
- 点赞数
- 收藏数
- 评论数
- 转发数

统计的数据会自动同步到明道云表格中，支持新增和更新操作。

## 配置步骤

### 1. 明道云配置

1. 复制 `mingdao_config.py` 文件到项目根目录
2. 修改配置文件中的以下参数：
   ```python
   MINGDAO_CONFIG = {
       "APP_KEY": "你的APP_KEY",
       "SIGN": "你的SIGN", 
       "BJK_WORKSHEET_ID": "你的工作表ID",
       "OWNER_ID": "你的所有者ID"
   }
   ```

### 2. 明道云工作表字段配置

确保明道云工作表包含以下字段：
- `bjbt`: 笔记标题
- `nicheng`: 昵称
- `xhsh`: 小红书号
- `xiaoyanjing_number`: 小眼睛数量(阅读数)
- `pinglun_number`: 评论数
- `dianzan_number`: 点赞数
- `shoucang_number`: 收藏数
- `zhuanfa_number`: 转发数
- `ownerid`: 所有者ID

### 3. 安装依赖

确保安装了 `schedule` 库：
```bash
pip install schedule
```

## 使用方法

### 自动定时执行

系统会在每天晚上23:30-00:30时间窗口内自动执行笔记统计，与养号监听逻辑共存：
- **统计时间**（23:30-00:30）：执行笔记数据统计
- **其他时间**：执行原有的养号和监听逻辑

### 手动触发

可以通过API手动触发统计：

```bash
# 手动触发笔记统计
curl -X POST http://localhost:8080/api/xhs/notes-stats

# 查看统计状态
curl -X GET http://localhost:8080/api/xhs/notes-stats/status
```

## 工作流程

### 时间判断逻辑
系统会根据当前时间自动选择执行模式：

**统计时间（23:30-00:30）**：
1. **获取账号信息**: 自动获取当前小红书账号的昵称和小红书号
2. **进入笔记页面**: 点击"我"页面，然后点击"笔记"tab
3. **滚动收集数据**: 滚动页面，收集所有可见的笔记
4. **获取详细数据**: 点击每个笔记，进入详情页获取点赞、收藏、评论等数据
5. **同步到明道云**: 将收集的数据同步到明道云表格
6. **等待**: 统计完成后等待1-1.5小时，避免重复执行

**其他时间**：
1. **执行养号**: 按原有逻辑执行养号操作
2. **执行监听**: 30%概率执行监听逻辑
3. **随机等待**: 等待3-5分钟后继续循环

## 注意事项

1. **设备连接**: 确保手机通过ADB正确连接到电脑
2. **小红书登录**: 确保手机上的小红书应用已登录目标账号
3. **网络连接**: 确保网络连接正常，能够访问明道云API
4. **权限设置**: 确保应用有足够的权限进行自动化操作

## 配置文件示例

`mingdao_config.py` 示例：
```python
MINGDAO_CONFIG = {
    "APP_KEY": "abcd1234efgh5678",
    "SIGN": "your_sign_here",
    "BJK_WORKSHEET_ID": "worksheet_id_here", 
    "OWNER_ID": "owner_id_here"
}
```

## 故障排除

### 常见问题

1. **无法找到笔记**: 检查UI元素ID是否发生变化
2. **明道云同步失败**: 检查配置信息是否正确
3. **设备连接失败**: 检查ADB连接状态
4. **应用崩溃**: 检查日志文件获取详细错误信息

### 日志查看

系统会记录详细的执行日志，可以通过日志文件查看执行状态和错误信息。

## 技术架构

- **定时任务**: 使用 `schedule` 库实现定时执行
- **UI自动化**: 使用 Appium 进行移动端自动化操作
- **数据同步**: 通过明道云API进行数据同步
- **并发处理**: 支持多设备并发执行统计任务

## 更新日志

- v1.0: 初始版本，支持基本的笔记数据统计和明道云同步
