from ..utils import glv, CommonUtils, logger
import json
import requests
from .tool_service import ToolService
import re
class CommonService(CommonUtils):
    def __init__(self):
        super().__init__()
        self.nezha_password = None
        self.nezha_username = None
        self.domain = None
        self.global_key = "common_var" 
        self.logger = logger

    def init_app(self,app):
        self.domain = app.config.get('DOMAIN', "")
        self.nezha_username = app.config.get('NEZHA_USERNAME', "")
        self.nezha_password = app.config.get('NEZHA_PASSWORD', "")


    def login(self):
        url = "https://api.auth.hctalent.cn/api/auth/sign"
        payload = json.dumps({
            "username": self.nezha_username,
            "password": self.nezha_password,
        })
        response = requests.request("POST", url, headers=self.headers, data=payload)
        res = json.loads(response.text)
        if res["err_no"] == 0:
            ToolService.build_common_var(self.global_key,"nezha_token",res["results"]["token"])
            ToolService.build_common_var(self.global_key,"nezha_phone",res["results"]["user"]["user"]["phone"])
            ToolService.build_common_var(self.global_key,"nezha_nickname",res["results"]["user"]["user"]["nickName"])
        else:
            self.logger.warning("用户名或密码错误")
            raise ValueError("用户名或密码错误！")

    def get_config(self):
        url = self.domain + "/interview-robot/get-config"
        req = {
            "token":"maotai"
        }
        retry_num = 2
        results = self.comm_post(url, req, retry_num)
        if results is None:
            return False
        ToolService.build_common_var(self.global_key,"config",results)
        return None

    def customer_post(self,url, req, retry_num):
        payload = json.dumps(req)
        retry = 0
        while True:
            if retry > retry_num:
                return None
            headers = {
                'Content-Type': 'application/json',
            }
            retry = retry+1
            try:
                response = requests.request("POST", url, headers=headers, data=payload, timeout=120)
                res = json.loads(response.text)
                return res
                    
            except Exception as e:
                exception_str = "url:"+url+"req:"+payload+str(e)
                self.logger.waring(exception_str)

    def comm_post(self, url, req, retry_num):
        payload = json.dumps(req)
        retry = 0
        while True:
            if retry > retry_num:
                return None
            headers = {
                'Content-Type': 'application/json',
                'Authorization': glv[self.global_key]["nezha_token"],
            }
            retry = retry+1
            try:
                response = requests.request("POST", url, headers=headers, data=payload, timeout=120)
                res = json.loads(response.text)
                if res["err_no"] == 40001 or res["err_no"] == 500:
                    self.login()
                    continue
                elif res["err_no"]>0:
                    return None
                else:
                    self.logger.warning("耗时："+str(res["duration"]) + ",请求url:" + url + ",req:"+payload+"res:"+response.text)
                    return res["results"]
            except Exception as e:
                exception_str = "url:"+url+"req:"+payload+str(e)
                self.logger.warning(exception_str)
        return None

    def set_variable(self, device_session):
        custom_udid = device_session.custom_udid
        page_source = device_session.get_page_source()
        width_match = re.search(r'class="hierarchy".*?width="(\d+)"', page_source)
        ToolService.build_common_var(custom_udid,"screen_width",int(width_match.group(1)))
        height_match = re.search(r'class="hierarchy".*?height="(\d+)"', page_source)
        ToolService.build_common_var(custom_udid,"screen_height",int(height_match.group(1)))

    # 调用账号配置(服务器)
    def set_run_config(self, device_session,from_flag = "super"):
        custom_udid = device_session.custom_udid
        url = self.domain + "/third-channel/get-channel-account-config"
        req = {
            "channel_account_info": glv[custom_udid]["channelAccountInfo"],
            "nick_name": "",
            "token": "maotai",
            "from": from_flag
        }

        res = self.comm_post(url, req, 1)
        if res is None or res["channelInfo"] is None:
            self.logger.warning(glv[custom_udid]["channelAccountInfo"]+"致命问题，账号配置错误，请重新配置后重试")
            return

        ToolService.build_common_var(custom_udid,"runConfig",res)


    def notice_qiyewechat(self, msg):
        url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=09e94d60-ca7e-45c0-863b-c34c95b5ceaa"
        payload = json.dumps({
            "msgtype": "text",
            "text": {
                "content": "自动化约面:" + msg,
                "mentioned_mobile_list":["***********"]
            }
        })
        requests.request("POST", url, headers=self.headers, data=payload)
        return True


    
common_service = CommonService()