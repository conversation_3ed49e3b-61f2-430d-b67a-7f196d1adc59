import time
import requests
from ..utils import logger, glv, CommonMobileUtils
class XhsFollowLogic:
    """小红书关注处理服务"""
    
    def __init__(self, custom_udid, device_session):
        self.custom_udid = custom_udid
        self.device_session = device_session
        self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
        # self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"

    def get_account_number(self):
        """获取小红书账号"""
        try:
            # 点击我的按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/du6' and @content-desc='我']", "click")
            account_numbers = self.device_session.find_all_by_xpath(".//*[(@resource-id='com.xingin.xhs:id/gxr' or @resource-id='com.xingin.xhs:id/hbv') and contains(@text,'小红书号')]")
            
            if len(account_numbers) == 0:
                self.device_session.swipe(170, 1400, 170, 600, 50)
                logger.error("小红书账号id获取错误，请重新启动后重试")
                return None
            
            account_number = account_numbers[0].get_attribute("text").replace("小红书号", "").replace(" ", "").replace("：", "")
            
            # 返回首页
            CommonMobileUtils.click_use_xpath(self.device_session, "//*[@resource-id='com.xingin.xhs:id/du4']", "click")
            return account_number
        except Exception as e:
            logger.error(f"获取账号信息失败: {e}")
            return None

    def navigate_to_followers(self):
        """导航到粉丝页面"""
        try:
            # 点击我的按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/du6' and @content-desc='我']", "click")
            time.sleep(2)
            
            # 点击粉丝数量进入粉丝列表
            fans_elements = self.device_session.find_all_by_xpath("//*[contains(@text,'粉丝')]")
            if fans_elements:
                fans_elements[0].click()
                time.sleep(2)
                return True
            else:
                logger.info("未找到粉丝入口")
                return False
                
        except Exception as e:
            logger.error(f"导航到粉丝页面失败: {e}")
            return False

    def process_new_followers(self, monitor_xhs_number, max_count=5):
        """处理新增关注者"""
        try:
            if not self.navigate_to_followers():
                return 0
            
            # 获取粉丝列表 - 需要根据实际UI调整
            follower_elements = self.device_session.find_all_by_xpath("//*[contains(@resource-id,'user') or contains(@resource-id,'item')]")
            
            processed_count = 0
            for element in follower_elements[:max_count]:  # 最多处理指定数量
                try:
                    # 点击进入用户主页
                    element.click()
                    time.sleep(2)
                    
                    # 获取用户信息
                    user_name = self.extract_user_name()
                    user_xhs_number = self.extract_user_xhs_number()
                    
                    if user_name and user_xhs_number:
                        # 保存到数据库
                        self.save_follow_to_db(monitor_xhs_number, user_xhs_number, user_name)
                        
                        # 发送企业微信通知
                        self.send_follow_notification(user_name, user_xhs_number, monitor_xhs_number)
                        
                        processed_count += 1
                    
                    # 返回粉丝列表
                    self.device_session.back()
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"处理单个关注者失败: {e}")
                    self.device_session.back()
                    continue
            
            # 返回首页
            self.device_session.back()
            CommonMobileUtils.click_use_xpath(self.device_session, "//*[@resource-id='com.xingin.xhs:id/du4']", "click")
            
            logger.info(f"处理了 {processed_count} 个新关注者")
            return processed_count
            
        except Exception as e:
            logger.error(f"处理新关注者失败: {e}")
            return 0

    def extract_user_name(self):
        """提取用户名 - 需要根据实际UI调整"""
        try:
            # 查找用户名元素
            name_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/gxp")
            if name_elements:
                return name_elements[0].get_attribute("text")
            return None
        except Exception as e:
            logger.error(f"提取用户名失败: {e}")
            return None

    def extract_user_xhs_number(self):
        """提取用户小红书号 - 从用户主页获取"""
        try:
            # 在用户主页查找小红书号
            xhs_number_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/gxr")

            if xhs_number_elements:
                xhs_number_text = xhs_number_elements[0].get_attribute("text")
                # 清理文本，提取纯数字/字母部分
                xhs_number = xhs_number_text.replace("小红书号", "").replace("：", "").replace(":", "").strip()
                logger.info(f"提取到小红书号: {xhs_number}")
                return xhs_number

            logger.warning("未找到小红书号元素")
            return None

        except Exception as e:
            logger.error(f"提取用户小红书号失败: {e}")
            return None

    def save_follow_to_db(self, monitor_xhs_number, user_xhs_number, user_name):
        """保存关注记录到数据库"""
        try:
            payload = {
                "monitorXhsNumber": monitor_xhs_number,
                "monitorUsername": "",
                "userXhsNumber": user_xhs_number,
                "userUsername": user_name,
                "actionType": "follow",
                "messageContent": "",
                "status": 0
            }

            response = requests.post("https://api.open.hctalent.cn/channel/xhsUserActions/save", json=payload)
            logger.info(f"关注记录入库结果：{response.status_code} - {response.text}")
            
        except Exception as e:
            logger.error(f"保存关注记录到数据库失败: {e}")

    def send_follow_notification(self, user_name, user_xhs_number, monitor_xhs_number, monitor_nickname=None):
        """发送关注通知到企业微信"""
        try:
            message = (
                f"### 发现新的用户行为\n"
                f"- 类型：**新增关注**\n"
                f"- 监控账号昵称：**{monitor_nickname}**\n"
                # f"- 监控账号：**{monitor_xhs_number}**\n"
                f"- 用户昵称：**{user_name}**\n"
                f"- 用户小红书号：**{user_xhs_number}**\n"
            )

            # 获取企业微信提醒人
            userid = self.fetch_qw_phone(monitor_xhs_number)
            self.send_to_wechat_group(message, userid)

        except Exception as e:
            logger.error(f"发送关注通知失败: {e}")

    def fetch_qw_phone(self, monitor_xhs_number):
        """获取企业微信手机号"""
        try:
            url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
            payload = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "account_config",
                "filters": [
                    {
                        "controlId": "id",
                        "dataType": 2,
                        "spliceType": 1,
                        "filterType": 1,
                        "value": monitor_xhs_number
                    }
                ]
            }

            headers = {"Content-Type": "application/json"}
            response = requests.post(url, headers=headers, json=payload)
            data = response.json()
            if data.get("success") and data.get("data", {}).get("rows"):
                row = data["data"]["rows"][0]
                return row.get("qw_phone", "")
        except Exception as e:
            logger.error(f"获取企业微信手机号失败：{e}")
        return ""

    def send_to_wechat_group(self, message, userid=None):
        """发送消息到企业微信群"""
        try:
            if userid:
                content = f"<@{userid}>\n{message}"
            else:
                content = message

            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }

            response = requests.post(self.wechat_webhook_url, json=payload)
            if response.status_code == 200:
                logger.info("✅ 企业微信通知发送成功")
            else:
                logger.error(f"❌ 企业微信通知发送失败，状态码：{response.status_code}")
        except Exception as e:
            logger.error(f"❌ 发送企业微信通知时出错：{e}")

    def check_follow_notifications(self, monitor_xhs_number, monitor_nickname=None):
        """检查关注通知 - 通过消息页面"""
        try:
            # 确保在消息页面 todo 向上滚动到新增关注出现
            # CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/du7']", "click")
            # time.sleep(2)

            # 检查新增关注按钮是否有未读数量
            follow_button = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/cd9']")
            if not follow_button:
                logger.info("未找到新增关注按钮")
                return 0

            # 检查是否有未读数量标识
            unread_count_element = None
            unread_count_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/ce1']")
            if not unread_count_elements:
                pass  # 没有未读标识
            else:
                unread_count_element = unread_count_elements[0]

            if not unread_count_element:
                logger.info("新增关注没有未读消息")
                return 0

            # 获取未读数量
            unread_count_text = unread_count_element.get_attribute("text")
            try:
                unread_count = int(unread_count_text)
                logger.info(f"发现 {unread_count} 条新增关注未读消息")
            except:
                unread_count = 1
                logger.info("无法解析未读数量，默认处理1条")

            # 点击新增关注按钮
            follow_button.click()
            time.sleep(2)

            # 检查页面是否有不再提醒按钮    
            page_source = self.device_session.get_page_source()
            time.sleep(1)    
            if "com.xingin.xhs:id/g5q" in page_source:
                self.device_session.find_by_id("com.xingin.xhs:id/g5q").click()
                
            # 处理关注列表中的用户
            processed_count = self.process_follow_list(monitor_xhs_number, unread_count, monitor_nickname)

            # 返回消息页面
            self.device_session.back()
            time.sleep(1)

            logger.info(f"处理了 {processed_count} 个新增关注")
            return processed_count

        except Exception as e:
            logger.error(f"检查关注通知失败: {e}")
            return 0

    def process_follow_list(self, monitor_xhs_number, max_count=20, monitor_nickname=None):
        """处理关注列表中的用户 - 支持滑动处理更多用户"""
        try:
            processed_count = 0
            processed_users = set()  # 记录已处理的用户，避免重复
            scroll_attempts = 0
            max_scroll_attempts = 5  # 最多滑动5次

            logger.info(f"开始处理 {monitor_nickname} 的关注列表，最多处理 {max_count} 个用户")

            while processed_count < max_count and scroll_attempts < max_scroll_attempts:
                # 查找当前屏幕的用户名元素
                current_user_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fsg']")
                current_avatar_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fsf']")

                if not current_user_elements:
                    logger.info("未找到用户元素")
                    break

                # 记录处理前的数量
                initial_processed = processed_count

                # 处理当前屏幕的用户
                for i in range(len(current_user_elements)):
                    if processed_count >= max_count:
                        break

                    try:
                        user_name = current_user_elements[i].get_attribute("text")

                        # 避免重复处理同一用户
                        if user_name in processed_users:
                            continue

                        logger.info(f"👤 处理第 {processed_count + 1} 个用户: {user_name}")

                        # 点击头像进入用户主页
                        if i < len(current_avatar_elements):
                            current_avatar_elements[i].click()
                            time.sleep(2)

                            # 获取用户小红书号
                            user_xhs_number = self.extract_user_xhs_number()

                            if user_name and user_xhs_number:
                                # 保存到数据库
                                self.save_follow_to_db(monitor_xhs_number, user_xhs_number, user_name)

                                # 发送企业微信通知
                                self.send_follow_notification(user_name, user_xhs_number, monitor_xhs_number, monitor_nickname)

                                processed_count += 1
                                processed_users.add(user_name)
                                logger.info(f"✅ 处理完成: {user_name} ({user_xhs_number})")

                            # 返回关注列表
                            self.device_session.back()
                            time.sleep(1)

                            # 重新获取元素（页面可能刷新）
                            current_user_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fsg']")
                            current_avatar_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fsf']")

                    except Exception as e:
                        logger.error(f"处理用户失败: {e}")
                        try:
                            self.device_session.back()
                            time.sleep(1)
                        except:
                            pass
                        continue

                # 如果这轮没有处理任何新用户，尝试向上滑动
                if processed_count == initial_processed:
                    if scroll_attempts < max_scroll_attempts:
                        logger.info(f"第 {scroll_attempts + 1} 次向上滑动加载更多用户...")
                        self.device_session.swipe(170, 1100, 170, 500, 50)
                        time.sleep(2)
                        scroll_attempts += 1
                    else:
                        logger.info("已达到最大滑动次数，停止处理")
                        break
                else:
                    # 有新用户被处理，重置滑动计数
                    scroll_attempts = 0

            logger.info(f"{monitor_nickname} 关注列表处理完成，共处理 {processed_count} 个用户")
            return processed_count

        except Exception as e:
            logger.error(f"处理关注列表失败: {e}")
            return 0
