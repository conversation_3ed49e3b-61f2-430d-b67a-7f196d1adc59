# hc_mobile_auto  
0、pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/  
1、安装 Node 和 npm 和 python 和 platform-tools  
2、添加环境变量  
    ANDROID_HOME=C:\auto  
3、环境变量path内添加  
    C:\auto\platform-tools  
4、npm install -g appium  
    npm install -g appium-doctor  
5、appium driver install uiautomator2  
6、安装Appium Inspector  
7、执行测试脚本  
8、荣耀开发者模式内的不监控adb安装  
9、配置环境变量
    ADB_ENV="xhs-test"  
    APP_ENV="xiaohongshu"  
  
pip install pyarmor  
pyarmor gen -O dist main.py  
  
pip install pyinstaller  
pyinstaller --onefile --name airecruit --clean main.py  
pyinstaller --onefile --name auto --clean --windowed --add-data "ui;ui" app_main.py  