import time
import requests
import json
from urllib.parse import quote

from ..utils import logger, glv, CommonMobileUtils
import re

class XhsCommentLogic:
    """小红书评论处理服务"""
    
    def __init__(self, custom_udid, device_session):
        self.custom_udid = custom_udid
        self.device_session = device_session
        self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
        # self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"
        self.base_api = "https://api.open.hctalent.cn/channel/reply-form"
        
    def get_account_number(self):
        """获取小红书账号"""
        try:
            # 点击我的按钮
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/du6' and @content-desc='我']", "click")
            account_numbers = self.device_session.find_all_by_xpath(".//*[(@resource-id='com.xingin.xhs:id/gxr' or @resource-id='com.xingin.xhs:id/hbv') and contains(@text,'小红书号')]")
            
            if len(account_numbers) == 0:
                self.device_session.swipe(170, 1400, 170, 600, 50)
                logger.error("小红书账号id获取错误，请重新启动后重试")
                return None
            
            account_number = account_numbers[0].get_attribute("text").replace("小红书号", "").replace(" ", "").replace("：", "")
            
            # 返回首页
            CommonMobileUtils.click_use_xpath(self.device_session, "//*[@resource-id='com.xingin.xhs:id/du4']", "click")
            return account_number
        except Exception as e:
            logger.error(f"获取账号信息失败: {e}")
            return None

    def process_comment_notifications(self, monitor_xhs_number, monitor_nickname=None):
        """处理评论通知"""
        try:

            # 检查评论和@按钮是否有未读数量
            comment_button = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/b_4']")
            if not comment_button:
                logger.info("未找到评论和@按钮")
                return 0

            # 检查是否有未读数量标识
            unread_count_element = None
            unread_count_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/bam']")
            if not unread_count_elements:
                pass  # 没有未读标识
            else:
                unread_count_element = unread_count_elements[0]

            if not unread_count_element:
                logger.info("评论和@没有未读消息")
                return 0

            # 获取未读数量
            unread_count_text = unread_count_element.get_attribute("text")
            try:
                unread_count = int(unread_count_text)
                logger.info(f"发现 {unread_count} 条评论和@未读消息")
            except:
                unread_count = 1
                logger.info("无法解析未读数量，默认处理1条")

            # 点击评论和@按钮
            comment_button.click()
            time.sleep(2)

            # 检查页面是否有不再提醒按钮    
            page_source = self.device_session.get_page_source()
            time.sleep(1)    
            if "com.xingin.xhs:id/g5q" in page_source:
                self.device_session.find_by_id("com.xingin.xhs:id/g5q").click()

            # 处理评论列表中的评论
            processed_count = self.process_comment_list(monitor_xhs_number, unread_count, monitor_nickname)

            # 返回消息列表
            self.device_session.back()
            time.sleep(1)
            logger.info(f"处理了 {processed_count} 条评论")
            return processed_count

        except Exception as e:
            logger.error(f"处理评论通知失败: {e}")
            return 0

    def process_comment_list(self, monitor_xhs_number, max_count=20, monitor_nickname=None):
        """处理评论列表中的评论 - 支持滑动处理更多评论"""
        try:
            processed_count = 0
            processed_comments = set()  # 记录已处理的评论，避免重复
            scroll_attempts = 0
            max_scroll_attempts = 5  # 最多滑动5次

            logger.info(f"开始处理 {monitor_nickname} 的评论列表，最多处理 {max_count} 条评论")

            while processed_count < max_count and scroll_attempts < max_scroll_attempts:
                # 查找当前屏幕的评论元素
                current_user_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fs1']")
                current_comment_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/frn']")

                if not current_user_elements or not current_comment_elements:
                    logger.info("未找到评论元素")
                    break

                # 记录处理前的数量
                initial_processed = processed_count

                # 处理当前屏幕的评论
                for i in range(min(len(current_user_elements), len(current_comment_elements))):
                    if processed_count >= max_count:
                        break

                    try:
                        user_name = current_user_elements[i].get_attribute("text")
                        comment_content = current_comment_elements[i].get_attribute("text")

                        # 创建唯一标识避免重复处理
                        comment_id = f"{user_name}:{comment_content}"
                        if comment_id in processed_comments:
                            continue

                        logger.info(f"📝 处理第 {processed_count + 1} 条评论: {user_name} - {comment_content[:20]}...")

                        # 首先点击评论项进入笔记页面获取笔记链接
                        comment_containers = self.device_session.find_all_by_xpath(".//android.widget.RelativeLayout[@clickable='true']")
                        note_link = ""

                        if i < len(comment_containers):
                            logger.info(f"🔗 为第 {processed_count + 1} 条评论获取笔记链接...")

                            # 点击评论项进入笔记页面
                            comment_containers[i].click()
                            time.sleep(2)

                            # 获取笔记链接
                            note_link = self.extract_note_link()
                            logger.info(f"🔗 第 {processed_count + 1} 条评论的笔记链接: {note_link[:50]}...")

                            # 返回评论列表
                            self.device_session.back()
                            time.sleep(1)

                            # 重新获取元素（页面可能刷新）
                            current_user_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fs1']")
                            if i >= len(current_user_elements):
                                break

                        # 点击用户名获取用户详情
                        current_user_elements[i].click()
                        time.sleep(2)

                        # 获取用户小红书号
                        user_xhs_number = self.extract_user_xhs_number()

                        if user_name and user_xhs_number and comment_content:
                            # 分析评论内容
                            analysis_result = self.analyze_comment(comment_content)

                            # 保存到数据库
                            self.save_comment_to_db(monitor_xhs_number, user_xhs_number, user_name, comment_content)

                            # 发送企业微信通知
                            self.send_comment_notification(user_name, user_xhs_number, comment_content, analysis_result, note_link, monitor_xhs_number, monitor_nickname)

                            processed_count += 1
                            processed_comments.add(comment_id)
                            logger.info(f"✅ 处理完成: {user_name} - {comment_content}")

                        # 重新获取元素（页面可能刷新）
                        current_user_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/fs1']")
                        current_comment_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/frn']")

                    except Exception as e:
                        logger.error(f"处理评论失败: {e}")
                        try:
                            self.device_session.back()
                            time.sleep(1)
                        except:
                            pass
                        continue

                # 如果这轮没有处理任何新评论，尝试向上滑动
                if processed_count == initial_processed:
                    if scroll_attempts < max_scroll_attempts:
                        logger.info(f"第 {scroll_attempts + 1} 次向上滑动加载更多评论...")
                        self.device_session.swipe(170, 1100, 170, 500, 50)
                        time.sleep(2)
                        scroll_attempts += 1
                    else:
                        logger.info("已达到最大滑动次数，停止处理")
                        break
                else:
                    # 有新评论被处理，重置滑动计数
                    scroll_attempts = 0

            logger.info(f"{monitor_nickname} 评论处理完成，共处理 {processed_count} 条评论")
            return processed_count

        except Exception as e:
            logger.error(f"处理评论列表失败: {e}")
            return 0



    def extract_user_xhs_number(self):
        """提取用户小红书号 - 需要根据实际UI调整"""
        try:
            # 查找小红书号
            xhs_number_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/gxr")
            if xhs_number_elements:
                xhs_number = xhs_number_elements[0].get_attribute("text").replace("小红书号", "").replace("：", "").strip()
                self.device_session.back()
                return xhs_number
            self.device_session.back()
            return None
        except Exception as e:
            logger.error(f"提取用户小红书号失败: {e}")
            return None

    def extract_note_link(self):
        """提取笔记链接 - 通过更多菜单复制链接"""
        try:
            logger.info("开始提取笔记链接...")

            # 0. 先清空剪贴板，避免获取到之前的链接
            self.device_session.set_clipboard("")
            time.sleep(0.5)

            # 1. 点击更多按钮
            more_button = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/moreOperateIV']")
            if not more_button:
                logger.warning("未找到更多按钮")
                return ""

            more_button.click()
            time.sleep(2)

            # 2. 查找操作菜单的RecyclerView
            menu_recycler = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/ge8']")
            if not menu_recycler:
                logger.warning("未找到操作菜单")
                return ""

            # 3. 查找"复制链接"按钮
            copy_link_buttons = self.device_session.find_all_by_xpath("//*[@resource-id='com.xingin.xhs:id/j_8' and @text='复制链接']")

            # 4. 如果没找到，在菜单区域向左滑动
            if not copy_link_buttons:
                logger.info("未找到复制链接按钮，尝试向左滑动...")
                # 在菜单区域向左滑动 (从建群分享区域向左滑)
                self.device_session.swipe(400, 1450, 100, 1450, 50)
                time.sleep(1)

                # 重新查找复制链接按钮
                copy_link_buttons = self.device_session.find_all_by_xpath("//*[@resource-id='com.xingin.xhs:id/j_8' and @text='复制链接']")

            if copy_link_buttons:
                # 5. 点击复制链接按钮
                logger.info("找到复制链接按钮，准备点击...")
                copy_link_buttons[0].click()
                time.sleep(2)  # 等待复制完成和自动返回

                # 6. 获取剪贴板内容并提取链接
                clipboard_content = self.device_session.get_clipboard()
                note_link = self.extract_link_from_clipboard(clipboard_content)

                if note_link:
                    logger.info(f"✅ 获取到笔记链接: {note_link}")
                    return note_link
                else:
                    logger.warning(f"⚠️ 未能从剪贴板内容中提取到有效链接: {clipboard_content[:100]}...")
                    return ""
            else:
                logger.warning("滑动后仍未找到复制链接按钮")
                # 关闭菜单
                self.device_session.back()
                time.sleep(1)
                return ""

        except Exception as e:
            logger.error(f"提取笔记链接失败: {e}")
            # 尝试关闭可能打开的菜单
            try:
                self.device_session.back()
            except:
                pass
            return ""

    def extract_link_from_clipboard(self, clipboard_content):
        """从剪贴板内容中提取小红书链接"""
        try:
            import re

            if not clipboard_content:
                return ""

            # 查找 http://xhslink.com/ 开头的链接
            xhslink_pattern = r'http://xhslink\.com/[a-zA-Z0-9/]+'
            xhslink_match = re.search(xhslink_pattern, clipboard_content)
            if xhslink_match:
                return xhslink_match.group()

            # 查找 https://www.xiaohongshu.com/ 开头的链接
            xiaohongshu_pattern = r'https://www\.xiaohongshu\.com/[a-zA-Z0-9/]+'
            xiaohongshu_match = re.search(xiaohongshu_pattern, clipboard_content)
            if xiaohongshu_match:
                return xiaohongshu_match.group()

            logger.warning(f"未找到有效的小红书链接，剪贴板内容: {clipboard_content[:200]}...")
            return ""

        except Exception as e:
            logger.error(f"提取链接失败: {e}")
            return ""

    def analyze_comment(self, comment_content):
        """调用GPT API分析评论内容"""
        try:
            api_url = "https://gpt.hctalent.cn/api/v1/chat/completions"
            headers = {
                "Authorization": "Bearer fastgpt-yAN1ImxLQNY6sMWXVh8qYWxPgdUevYX4hNDhgBvw5dEyimQtH8IOZNn1cNZamf1qm",
                "Content-Type": "application/json"
            }
            request_body = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": comment_content
                            }
                        ]
                    }
                ]
            }

            response = requests.post(api_url, headers=headers, json=request_body)
            response.raise_for_status()
            gpt_result = response.json()

            gpt_content = gpt_result['choices'][0]['message']['content']
            # 去掉 markdown 格式的代码块符号
            import re
            cleaned_content = re.sub(r"^```json\s*|```$", "", gpt_content.strip(), flags=re.MULTILINE).strip()

            gpt_data = json.loads(cleaned_content)
            return gpt_data

        except Exception as e:
            logger.error(f"调用分析接口失败: {e}")
            return None

    def save_comment_to_db(self, monitor_xhs_number, user_xhs_number, user_name, comment_content):
        """保存评论到数据库"""
        try:
            payload = {
                "monitorXhsNumber": monitor_xhs_number,
                "monitorUsername": "",
                "userXhsNumber": user_xhs_number,
                "userUsername": user_name,
                "actionType": "comment",
                "messageContent": comment_content,
                "status": 0
            }

            response = requests.post("https://api.open.hctalent.cn/channel/xhsUserActions/save", json=payload)
            logger.info(f"评论入库结果：{response.status_code} - {response.text}")
            
        except Exception as e:
            logger.error(f"保存评论到数据库失败: {e}")

    def send_comment_notification(self, user_name, user_xhs_number, comment_content, analysis_result, note_link, monitor_xhs_number, monitor_nickname=None):
        """发送评论通知到企业微信"""
        try:
            # 构建分析结果字符串
            analysis_text = ""
            if analysis_result:
                analysis = analysis_result.get('analysis', {})
                sentiment = analysis.get('sentiment', {})
                sentiment_type = sentiment.get('type', '未知')
                sentiment_confidence = sentiment.get('confidence_score', '未知')

                transaction = analysis.get('transaction_intent', {})
                has_intent = transaction.get('has_intent', '否')
                intent_type = transaction.get('intent_type') or '无'

                analysis_text = (
                    f"\n### 评论分析结果\n"
                    f"- 情感倾向：**{sentiment_type}** (置信度: {sentiment_confidence})\n"
                    f"- 是否包含交易意图：**{has_intent}**\n"
                    f"- 意图类型：{intent_type}\n"
                )

            # 构建回复链接
            reply_url = (
                f"https://api.open.hctalent.cn/reply-form/"
                f"?user={quote(user_xhs_number)}&content={quote(comment_content)}"
            )

            message = (
                f"### 发现新的用户行为\n"
                f"- 类型：**新增评论**\n"
                f"- 监控账号昵称：**{monitor_nickname}**\n"
                f"- 监控账号：**{monitor_xhs_number}**\n"
                f"- 用户昵称：**{user_name}**\n"
                f"- 用户小红书号：**{user_xhs_number}**\n"
                f"> 评论内容：{comment_content}\n\n"
                f"{analysis_text}"
                f"[点击回复这条评论]({reply_url})\n\n"
            )

            if note_link:
                message += f"[点击查看笔记详情]({note_link})"

            # 获取企业微信提醒人
            userid = self.fetch_qw_phone(monitor_xhs_number)
            self.send_to_wechat_group(message, userid)

        except Exception as e:
            logger.error(f"发送评论通知失败: {e}")

    def fetch_qw_phone(self, monitor_xhs_number):
        """获取企业微信手机号"""
        try:
            url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
            payload = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "account_config",
                "filters": [
                    {
                        "controlId": "id",
                        "dataType": 2,
                        "spliceType": 1,
                        "filterType": 1,
                        "value": monitor_xhs_number
                    }
                ]
            }

            headers = {"Content-Type": "application/json"}
            response = requests.post(url, headers=headers, json=payload)
            data = response.json()
            if data.get("success") and data.get("data", {}).get("rows"):
                row = data["data"]["rows"][0]
                return row.get("qw_phone", "")
        except Exception as e:
            logger.error(f"获取企业微信手机号失败：{e}")
        return ""

    def send_to_wechat_group(self, message, userid=None):
        """发送消息到企业微信群"""
        try:
            if userid:
                content = f"<@{userid}>\n{message}"
            else:
                content = message

            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }

            response = requests.post(self.wechat_webhook_url, json=payload)
            if response.status_code == 200:
                logger.info("✅ 企业微信通知发送成功")
            else:
                logger.error(f"❌ 企业微信通知发送失败，状态码：{response.status_code}")
        except Exception as e:
            logger.error(f"❌ 发送企业微信通知时出错：{e}")

    def handle_pending_reply(self, monitor_xhs_number):
        """处理待回复的评论"""
        try:
            # 获取待回复记录
            resp = requests.post(
                f"{self.base_api}/pending-replies",
                json={
                    "monitorXhsNumber": monitor_xhs_number,
                    "actionType": "comment"
                }
            )
            resp.raise_for_status()
            resp_json = resp.json()
            data = resp_json.get("data")
            
            if not data:
                logger.info("无待回复记录")
                return
            
            for reply_item in data:
                try:
                    message_content = reply_item.get("messageContent")
                    user_xhs_number = reply_item.get("userXhsNumber")
                    reply_content = reply_item.get("replyContent")
                    record_id = reply_item.get("id")
                    user_username = reply_item.get("user_username")  # 获取用户名

                    # 执行回复逻辑，传入用户名进行双重匹配
                    replied = self.send_reply_mobile(message_content, reply_content, user_username)
                    
                    if replied:
                        # 标记为已回复
                        requests.post(f"{self.base_api}/mark-replied", json={"id": record_id})
                        logger.info(f"记录 {record_id} 回复成功并已标记")

                        # 发送成功通知
                        message = (
                            f"✅ 自动回复成功\n"
                            f"> 用户号：{user_xhs_number}\n"
                            f"> 原评论：{message_content}\n"
                            f"> 回复内容：{reply_content}"
                        )
                        self.send_to_wechat_group(message)
                    else:
                        # 回复失败，更新状态为3
                        try:
                            update_resp = requests.post(
                                "https://api.open.hctalent.cn/channel/xhsUserActions/update",
                                json={
                                    "id": record_id,
                                    "isReplied": 3
                                }
                            )
                            update_resp.raise_for_status()
                            logger.info(f"记录 {record_id} 回复失败，状态已更新为3")
                            # 返回消息页面
                            self.device_session.back()
                            time.sleep(1)
                        except Exception as update_e:
                            logger.error(f"更新记录 {record_id} 状态失败: {update_e}")

                        logger.warning(f"记录 {record_id} 回复失败")
                    
                except Exception as e:
                    logger.error(f"处理回复记录失败: {e}")
                    
        except Exception as e:
            logger.error(f"处理待回复失败: {e}")

    def clean_message_content(self, content):
        """
        清洗评论内容，去除表情符号、换行、空格等干扰字符
        """
        if not content:
            return ""
        # 去除类似 [偷笑R] 这种小红书表情
        cleaned = re.sub(r'\[.*?R\]', '', content)
        # 去掉换行和前后空格
        cleaned = cleaned.replace('\n', '').replace('\r', '').strip()
        return cleaned

    def send_reply_mobile(self, target_message, reply_content, target_username=None):
        """在手机端发送回复 - 支持用户名+评论内容双重匹配"""
        try:
            logger.info(f"📝 准备回复评论: {target_message[:20]}... -> {reply_content}")
            if target_username:
                logger.info(f"🎯 目标用户: {target_username}")
                self.current_reply_username = target_username
            else:
                self.current_reply_username = None

            # 1. 确保在评论和@页面
            comment_button = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/b_4']")
            if comment_button:
                comment_button.click()
                time.sleep(2)
                logger.info("已进入评论和@页面")

            # 2. 滑动查找目标评论
            scroll_attempts = 0
            max_scroll_attempts = 10  # 最多滑动10次查找目标评论
            found_target = False

            while not found_target and scroll_attempts < max_scroll_attempts:
                # 获取当前屏幕的评论容器
                comment_containers = self.device_session.find_all_by_xpath(".//android.widget.RelativeLayout[@clickable='true']")

                if not comment_containers:
                    logger.warning("未找到评论容器")
                    break

                # 3. 遍历当前屏幕的评论容器，查找匹配的内容
                for i, container in enumerate(comment_containers):
                    try:
                        # 查找评论内容元素
                        comment_element = container.find_element_by_id("com.xingin.xhs:id/frn")
                        comment_text = comment_element.get_attribute("text")

                        # 查找用户名元素
                        username_element = container.find_element_by_id("com.xingin.xhs:id/fs1")
                        username = username_element.get_attribute("text")

                        # 4. 使用双重匹配逻辑：评论内容 + 用户名
                        cleaned_target = self.clean_message_content(target_message)
                        cleaned_actual = self.clean_message_content(comment_text)

                        # 精确匹配策略：评论内容完全相等 AND 用户名匹配
                        content_match = cleaned_target == cleaned_actual
                        username_match = True  # 默认为True，如果有用户名则进行匹配

                        # 如果有用户名信息，则进行用户名匹配
                        if hasattr(self, 'current_reply_username') and self.current_reply_username:
                            username_match = username == self.current_reply_username
                            logger.info(f"用户名匹配检查: 目标用户={self.current_reply_username}, 当前用户={username}, 匹配结果={username_match}")

                        if content_match and username_match:
                            logger.info(f"匹配评论成功：用户={username}, 评论={comment_text}")
                            found_target = True

                            # 5. 点击"回复"按钮
                            try:
                                reply_button = container.find_element_by_id("com.xingin.xhs:id/fqq")
                                reply_button.click()
                                time.sleep(3)  # 等待回复界面加载
                                logger.info("已点击回复按钮")

                                # 6. 直接填写回复内容 input
                                success = self.fill_reply_content_mobile(reply_content)
                                if success:
                                    logger.info("[已发送] 自动回复成功")
                                    return True
                                else:
                                    logger.error("填写回复内容失败")
                                    return False

                            except Exception as e:
                                logger.warning(f"第一次点击回复按钮失败，尝试滑动重试")
                                try:
                                    self.device_session.swipe(170, 1100, 170, 500, 50)
                                    time.sleep(1)
                                    reply_button = container.find_element_by_id("com.xingin.xhs:id/fqq")
                                    reply_button.click()
                                    time.sleep(3)
                                    logger.info("重试点击回复按钮成功")

                                    success = self.fill_reply_content_mobile(reply_content)
                                    if success:
                                        logger.info("[已发送] 自动回复成功（重试）")
                                        return True
                                    else:
                                        logger.error("填写回复内容失败（重试）")
                                        return False

                                except Exception as e2:
                                    logger.error(f"重试点击回复按钮失败: {e2}")
                                    return False

                    except Exception as inner_e:
                        # 跳过无法处理的容器
                        continue

                # 如果当前屏幕没有找到目标评论，向上滑动查找更多
                if not found_target:
                    if scroll_attempts < max_scroll_attempts:
                        logger.info(f"第 {scroll_attempts + 1} 次向上滑动查找目标评论...")
                        self.device_session.swipe(170, 1100, 170, 500, 50)
                        time.sleep(2)
                        scroll_attempts += 1
                    else:
                        logger.warning("已达到最大滑动次数，未找到目标评论")
                        break

            if not found_target:
                logger.warning("未匹配到指定评论内容")
                return False

        except Exception as e:
            logger.error(f"自动回复失败：{e}")
            return False

    def fill_reply_content_mobile(self, reply_content):
        """使用复制粘贴方式填写回复内容 - 移动端优化版本"""
        try:
            # 查找输入框
            input_elements = []

            try:
                input_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/f33")
                if input_elements:
                    logger.info(f"通过resource-id找到 {len(input_elements)} 个输入框")
            except Exception as e:
                logger.debug(f"通过resource-id查找失败: {e}")

            if input_elements:
                # 点击输入框
                input_box = input_elements[0]
                input_box.click()
                time.sleep(2)
                logger.info("已点击输入框")

                # 4. 使用xbot.py的input方法填写内容
                try:
                    input_box.input(reply_content)
                except Exception as e:
                    logger.error(f"填写回复内容出错: {e}")

                # 5. 查找并点击发送按钮
                send_buttons = self.device_session.find_all_by_id("com.xingin.xhs:id/fb0") 

                if send_buttons:
                    send_buttons[0].click()
                    time.sleep(2)
                    logger.info("✅ 回复发送成功")
                    # 返回消息页面
                    self.device_session.back()
                    return True
                else:
                    logger.error("未找到发送按钮")
                    return False
            else:
                logger.error("未找到输入框")
                return False

        except Exception as e:
            logger.error(f"填写回复内容失败: {e}")
            return False
