import time
import random
import re
import requests
from datetime import datetime

from ..utils import logger, glv, CommonMobileUtils
from ..services.tool_service import ToolService
class YanghaoLogic:
    """养号逻辑类 - 适配自影刀养号系统"""
    
    def __init__(self, custom_udid, domain):
        self.domain = domain
        self.custom_udid = custom_udid
        self.device_session = None
        self.clicked_elements = set()  # 用于存储已点击的元素
        self.comment_index = 0  # 评论索引
        ToolService.build_common_var(self.custom_udid, "yanghao_last_run_timestamp", 0)

    def init(self, device_session):
        self.device_session = device_session

    def fetch_account_config(self, account_number):
        """从明道云获取账号配置"""
        url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
        
        headers = {
            "Content-Type": "application/json",
        }

        data = {
            "appKey": "f08bf7f7cfe8c038",
            "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
            "worksheetId": "raiseAccount",
            "filters": [
                {
                    "controlId": "opreateAccount",
                    "filterType": 1,  # 建议用“包含”匹配，避免匹配不到
                    "value": str(account_number)
                }
            ]
        }

        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                res = response.json()
                rows = res.get("data", {}).get("rows", [])
                
                if res.get("success") and rows:
                    row_data = rows[0]
                    return row_data
                else:
                    logger.info(f"未找到账号 {account_number} 的配置，响应内容: {res}")
            else:
                logger.error(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
        except Exception as e:
            logger.error(f"养号配置获取失败,请求时发生错误: {e}")
        
        return None

    def get_account_number(self):
        """获取小红书账号"""
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/du6' and @content-desc='我']", "click")
        account_numbers = self.device_session.find_all_by_xpath(".//*[(@resource-id='com.xingin.xhs:id/gxr' or @resource-id='com.xingin.xhs:id/hbv') and contains(@text,'小红书号')]")
        
        if len(account_numbers) == 0:
            # 网络故障，上拉刷新
            self.device_session.swipe(170, 1400, 170, 600, 50)
            logger.error("小红书账号id获取错误，请重新启动后重试")
            return None
        
        account_number = account_numbers[0].get_attribute("text").replace("小红书号", "").replace(" ", "").replace("：", "")
        return account_number

    def get_account_name(self):
        """获取账号昵称"""
        account_name = self.device_session.find_by_id("com.xingin.xhs:id/gxp").get_attribute("text")
        # 点击首页
        CommonMobileUtils.click_use_xpath(self.device_session, "//*[@resource-id='com.xingin.xhs:id/du5']", "click")
        return account_name

    def is_in_time_slot(self, start_time_str, end_time_str, current_time):
        """检查当前时间是否在指定时间段内"""
        if start_time_str and end_time_str:
            start_time = datetime.strptime(start_time_str, "%H:%M").replace(
                year=current_time.year, month=current_time.month, day=current_time.day, second=0
            )
            end_time = datetime.strptime(end_time_str, "%H:%M").replace(
                year=current_time.year, month=current_time.month, day=current_time.day, second=0
            )
            return start_time <= current_time <= end_time
        return False

    def search_for_content(self, searchContent):
        """搜索内容"""
        try:
            # 搜索输入框的资源ID
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/hmg']", "click")
            search_box = self.device_session.find_by_id("com.xingin.xhs:id/fam")  
            search_box.input(searchContent)
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.xingin.xhs:id/luz']", "click")
            
            try:
                # 查找网络刷新按钮
                time.sleep(20)
                refresh_button = self.device_session.find_by_text("刷新")
                if refresh_button:
                    refresh_button.click()
                    logger.info("刷新按钮点击成功")
            except Exception as e:
                pass

            logger.info("搜索执行成功")
            time.sleep(2)
        except Exception as e:
            logger.error(f"搜索时出错: {str(e)}")

    def ensure_app_ready(self):
        """确保应用已启动并处于正常状态"""
        try:
            # 检查页面是否在小红书
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs" not in page_source:
                self.device_session.activate_app("com.xingin.xhs")
                wait_time = random.randint(10, 15)
                logger.info(f"等待 {wait_time} 秒让 App 启动...")
                time.sleep(wait_time)

            # 检查页面是否有稍后再说按钮
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/ali" in page_source:
                self.device_session.find_by_id("com.xingin.xhs:id/ali").click()
                time.sleep(2)

            # 确保回到首页
            self.ensure_homepage()

        except Exception as e:
            logger.error(f"应用初始化失败: {e}")

    def ensure_homepage(self):
        """确保当前在首页"""
        try:
            # 首先检查当前是否已经在首页
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/du5" in page_source:
                logger.info("当前已在首页，无需返回")
                return

            # 最多返回8次，每次都检查是否有首页元素
            for i in range(8):
                # 返回一次
                logger.info(f"第{i+1}次返回操作")
                self.device_session.back()
                time.sleep(2)  # 等待页面加载

                # 检查是否有首页元素
                page_source = self.device_session.get_page_source()
                time.sleep(2) 
                if "com.xingin.xhs:id/du5" in page_source:
                    logger.info(f"第{i+1}次检查：已找到首页元素")
                    return  # 找到首页元素就退出

            # 8次都没找到首页元素
            logger.warning("返回8次后仍未找到首页元素")

        except Exception as e:
            logger.error(f"回到首页失败: {e}")

    def check_follower_count_range(self, runConfig):
        """检查博主粉丝数是否在指定范围内"""
        try:
            # 获取配置的粉丝数范围，确保是整数类型
            try:
                min_followers = int(runConfig.get("min_fans", 0))  # 最小粉丝数
                max_followers = int(runConfig.get("max_fans", 999999999))  # 最大粉丝数
            except (ValueError, TypeError):
                logger.warning("粉丝数配置格式错误，使用默认值")
                min_followers = 0
                max_followers = 999999999

            # 检查是否需要进行粉丝数筛选
            should_follow = True
            if min_followers != 0 or max_followers != 999999999:
                # 需要检查粉丝数，点击博主头像进入主页
                avatar_element = self.device_session.find_by_id("com.xingin.xhs:id/avatarLayout")
                if avatar_element:
                    avatar_element.click()
                    time.sleep(2)

                    # 查找粉丝数元素 - 根据UI树使用cdd元素（粉丝数数字）
                    follower_element = self.device_session.find_by_id("com.xingin.xhs:id/cdd")
                    if follower_element:
                        follower_text = follower_element.get_attribute("text")
                        follower_count = self.parse_follower_count(follower_text)

                        # 确保follower_count是整数类型
                        try:
                            follower_count = int(follower_count) if isinstance(follower_count, str) else follower_count
                        except (ValueError, TypeError):
                            logger.warning(f"粉丝数格式错误: {follower_count}，默认为0")
                            follower_count = 0

                        # 判断粉丝数是否在范围内
                        if min_followers <= follower_count <= max_followers:
                            logger.info(f"粉丝数 {follower_count} 在范围内 [{min_followers}, {max_followers}]，允许关注")
                            should_follow = True
                        else:
                            logger.info(f"粉丝数 {follower_count} 不在范围内 [{min_followers}, {max_followers}]，跳过关注")
                            should_follow = False
                    else:
                        logger.warning("未找到粉丝数元素，默认允许关注")
                        should_follow = True

                    # 返回笔记页面
                    self.device_session.back()
                    time.sleep(1)
                else:
                    logger.warning("未找到博主头像元素，默认允许关注")
                    should_follow = True
            else:
                logger.info("未设置粉丝数筛选条件，默认允许关注")

            # 如果符合关注条件，执行关注操作
            if should_follow:
                # 查找博主头像元素 - 根据UI树使用avatarLayout
                avatar_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/avatarLayout")
                if avatar_elements:
                    avatar_elements[0].click()
                    time.sleep(2)
                    guanzhu_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/jo8")
                    if guanzhu_elements:
                        guanzhu_elements[0].click()
                        logger.info("关注成功")
                    time.sleep(1)
                    self.device_session.back()
                    return True
                else:
                    logger.warning("未找到博主头像元素，无法执行关注")
                    return False
            else:
                return False

        except Exception as e:
            logger.error(f"粉丝数检查失败: {e}")
            return True  # 失败时默认允许关注

    def parse_follower_count(self, follower_text):
        """解析粉丝数文本，支持万、k等单位"""
        try:
            if not follower_text:
                return 0

            # 移除空格和其他字符，只保留数字和单位
            text = follower_text.strip().lower()

            # 提取数字部分
            import re
            number_match = re.search(r'(\d+\.?\d*)', text)
            if not number_match:
                return 0

            number = float(number_match.group(1))

            # 处理单位
            if '万' in text:
                return int(number * 10000)
            elif 'k' in text:
                return int(number * 1000)
            else:
                return int(number)

        except Exception as e:
            logger.error(f"解析粉丝数失败: {e}, 原始文本: {follower_text}")
            return 0
        
    def navigate_to_nearby(self):
        """导航到附近页面"""
        try:
            # 第一次找附近按钮
            nearby_buttons = self.device_session.find_all_by_text("附近")
            if not nearby_buttons:
                logger.warning("未找到附近按钮")
                return False

            # 点击“附近”按钮
            nearby_buttons[0].click()
            time.sleep(2)

            # 处理权限弹窗：点击“拒绝”
            deny_buttons = self.device_session.find_all_by_text("拒绝")
            if deny_buttons:
                deny_buttons[0].click()
                logger.info("点击了权限弹窗的‘拒绝’按钮")
                time.sleep(1)

            # 点击之后如果“附近”按钮仍然存在，说明跳转成功（就在附近页）
            if self.device_session.find_all_by_text("附近"):
                logger.info("已进入附近页面")
                return True

            # 如果“附近”按钮不在了，说明跳转失败或跳到设置页 → 最多返回3次
            for _ in range(3):
                logger.warning("未检测到‘附近’按钮，可能跳转错误，尝试返回")
                self.device_session.back()
                time.sleep(1)

                # ✅ 如果返回后看到“附近”按钮，就说明现在页面是正确的了，可以认为成功
                if self.device_session.find_all_by_text("附近"):
                    logger.info("返回后找到了‘附近’按钮，视为成功")
                    return True

            logger.warning("连续返回3次后仍未看到‘附近’按钮")
            return False

        except Exception as e:
            logger.error(f"导航到附近页面失败: {e}")
            return False


    def should_comment_on_content(self, runConfig, content_desc):
        """检查是否应该对当前内容进行评论（基于笔记标题或博主标题）"""
        try:
            # 获取配置的评论条件
            comment_title_keywords = runConfig.get("commentTitleKeyword", "")  # 指定笔记标题关键词
            comment_author_keywords = runConfig.get("commentAuthorKeywords", "")  # 指定博主名称关键词

            # 如果没有设置任何条件，默认允许评论
            if not comment_title_keywords and not comment_author_keywords:
                return True

            # 检查笔记标题关键词
            if comment_title_keywords:
                title_keywords = [kw.strip() for kw in comment_title_keywords.split(',') if kw.strip()]
                for keyword in title_keywords:
                    if keyword in content_desc:
                        logger.info(f"笔记标题包含关键词 '{keyword}'，允许评论")
                        return True

            # 检查博主名称关键词
            if comment_author_keywords:
                author_keywords = [kw.strip() for kw in comment_author_keywords.split(',') if kw.strip()]
                # 获取博主名称
                blogger_name = self.get_blogger_name_from_content()
                if blogger_name:
                    for keyword in author_keywords:
                        if keyword in blogger_name:
                            logger.info(f"博主名称 '{blogger_name}' 包含关键词 '{keyword}'，允许评论")
                            return True

            logger.info("内容不符合评论条件")
            return False

        except Exception as e:
            logger.error(f"检查评论条件失败: {e}")
            return True  # 出错时默认允许评论

    def get_blogger_name_from_content(self):
        """从当前页面获取博主名称"""
        try:
            # 根据UI树查找博主名称元素 - nickNameTV
            blogger_element = self.device_session.find_by_id("com.xingin.xhs:id/nickNameTV")
            if blogger_element:
                blogger_name = blogger_element.get_attribute("text")
                logger.info(f"获取到博主名称: {blogger_name}")
                return blogger_name
            else:
                logger.warning("未找到博主名称元素")
                return ""
        except Exception as e:
            logger.error(f"获取博主名称失败: {e}")
            return ""

    def perform_actions(self, page_source, account_name, account_number, runConfig, watchProbability,
                       operateType, comments, actions, content_desc, is_video):
        """执行具体的动作（看、赞、藏、评、关注）"""
        has_comment = False

        # 看操作 - 随机执行
        watch_chance = 0.8  # 80% 概率观看
        if "看" in actions and random.random() < watch_chance:
            logger.info("看操作成功")

            if is_video or "com.xingin.xhs:id/g7z" not in page_source:
                logger.info("是视频或者是点赞元素找不到")
                # time.sleep(random.randint(6, 30))
                # CommonMobileUtils.back(self.device_session, 1)
                # return
            else:
                # 如果不是视频，进行滑动操作
                for _ in range(random.randint(2, 5)):
                    self.device_session.swipe(170, 1400, 170, 600, 50)
                    time.sleep(3)
            time.sleep(random.randint(6, 30))

        # 设置动作概率 - 所有动作都随机执行
        like_chance = 0.3      # 30% 概率点赞
        collect_chance = 0.2   # 20% 概率收藏
        comment_chance = 0.15  # 15% 概率评论
        follow_chance = 0.3  # 30% 概率点击关注

        # 关注操作 - 在笔记页面直接点击关注按钮，根据粉丝数筛选
        if random.random() < follow_chance:
            logger.info("尝试点击关注按钮")
            try:
                # 检查粉丝数是否符合条件并执行关注
                self.check_follower_count_range(runConfig)

                # 查找并点击"不再提醒"按钮（如果出现）
                try:
                    remind_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/g5i")
                    if remind_elements:
                        remind_elements[0].click()
                        logger.info("已点击不再提醒")
                except Exception:
                    pass

            except Exception as e:
                logger.debug(f"关注操作失败: {e}")
        time.sleep(1)

        # 点赞操作 - 兼容视频和图文笔记
        if "赞" in actions and random.random() < like_chance:
            logger.info("进入点赞操作")
            time.sleep(2)

            # 尝试视频笔记的点赞按钮（右侧按钮区域的第一个）
            like_element = None
            if is_video:
                try:
                    # 视频笔记：右侧按钮区域的第一个LinearLayout（点赞）
                    like_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/c91']//android.widget.LinearLayout[@clickable='true']")
                    if like_elements and len(like_elements) > 0:
                        like_element = like_elements[0]  # 第一个是点赞按钮
                        logger.info("找到视频笔记点赞按钮")
                except Exception as e:
                    logger.debug(f"视频点赞按钮查找失败: {e}")

            # 如果视频点赞按钮没找到，尝试图文笔记的点赞按钮
            if not like_element:
                try:
                    like_element = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/g7z' or @resource-id='com.xingin.xhs:id/likeLayout']")
                    logger.info("找到图文笔记点赞按钮")
                except Exception as e:
                    logger.debug(f"图文点赞按钮查找失败: {e}")

            if like_element:
                try:
                    like_element.click()
                    page_source = self.device_session.get_page_source()
                    if "com.xingin.xhs:id/fep" in page_source:
                        self.device_session.back()
                        time.sleep(1)
                    logger.info("点赞成功")
                except Exception as e:
                    logger.error(f"点赞操作失败: {e}")
            else:
                logger.info("未找到点赞按钮")
            time.sleep(2)

        time.sleep(random.randint(3, 5))   
        
        # 收藏操作 - 兼容视频和图文笔记
        if "藏" in actions and random.random() < collect_chance:
            logger.info("进入收藏操作")

            # 尝试视频笔记的收藏按钮（右侧按钮区域的第二个）
            collect_element = None
            if is_video:
                try:
                    # 视频笔记：右侧按钮区域的第二个LinearLayout（收藏）
                    collect_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/c91']//android.widget.LinearLayout[@clickable='true']")
                    if collect_elements and len(collect_elements) > 1:
                        collect_element = collect_elements[1]  # 第二个是收藏按钮
                        logger.info("找到视频笔记收藏按钮")
                except Exception as e:
                    logger.debug(f"视频收藏按钮查找失败: {e}")

            # 如果视频收藏按钮没找到，尝试图文笔记的收藏按钮
            if not collect_element:
                try:
                    collect_element = self.device_session.find_by_xpath(".//*[@resource-id='com.xingin.xhs:id/g69' or @resource-id='com.xingin.xhs:id/collectLayout']")
                    logger.info("找到图文笔记收藏按钮")
                except Exception as e:
                    logger.debug(f"图文收藏按钮查找失败: {e}")

            if collect_element:
                try:
                    collect_element.click()
                    logger.info("收藏成功")
                except Exception as e:
                    logger.error(f"收藏操作失败: {e}")
            else:
                logger.info("未找到收藏按钮")
            time.sleep(2)

        time.sleep(random.randint(1, 3))   
        
        # 评论操作 - 兼容视频和图文笔记，支持指定笔记标题或博主标题
        if "评" in actions and random.random() < comment_chance:
            # 检查是否符合评论条件（笔记标题或博主标题）
            if self.should_comment_on_content(runConfig, content_desc):
                # 预处理评论列表
                comments = [comment.strip() for comment in comments.splitlines() if comment.strip()]
                logger.info("进入评论")
                comment = random.choice(comments)
                logger.info(f"已选择评论内容: {comment}")

                try:
                    input_field = None

                    # 尝试直接找到输入框f33
                    try:
                        input_field = self.device_session.find_by_id("com.xingin.xhs:id/f33")
                        logger.info("直接找到评论输入框f33")
                    except Exception as e:
                        logger.debug(f"直接查找f33失败")

                    # 如果没找到f33，尝试点击dvr激活输入框
                    if not input_field:
                        try:
                            dvr_field = self.device_session.find_by_id("com.xingin.xhs:id/dvr")
                            dvr_field.click()
                            logger.info("点击dvr激活评论输入框")
                            time.sleep(1)
                            input_field = self.device_session.find_by_id("com.xingin.xhs:id/f33")
                        except Exception as e:
                            logger.debug(f"查找或点击dvr失败")

                    if input_field:
                        input_field.click()
                        time.sleep(1)

                        # 输入评论内容
                        input_field.input(comment)
                        time.sleep(1)

                        # 查找发送按钮 - 多种可能
                        send_button = None
                        try:
                            send_button = self.device_session.find_by_xpath(
                                ".//*[@resource-id='com.xingin.xhs:id/lkn' or "
                                "@resource-id='com.xingin.xhs:id/fb0' or "
                                "@resource-id='com.xingin.xhs:id/fbp']"
                            )
                        except Exception as e:
                            logger.debug(f"发送按钮查找失败: {e}")

                        if send_button:
                            send_button.click()
                            logger.info(f"评论成功: {comment}")
                            has_comment = True
                        else:
                            logger.info("未找到发送按钮")
                    else:
                        logger.info("未找到评论输入框")

                except Exception as e:
                    logger.error(f"评论操作失败: {e}")

                time.sleep(1)
            else:
                logger.info("笔记标题或博主不符合评论条件，跳过评论")

        if has_comment:
            CommonMobileUtils.back(self.device_session, 2 if is_video else 1)
        else:
            CommonMobileUtils.back(self.device_session, 1) 

        self.clicked_elements.add(content_desc)
        time.sleep(1)

    def handle_action(self, account_name, runConfig, watchProbability, account_number, operateType,
                     comments, actions, title_keywords, blogger_keyword, searchContent, operateMenu):
        """处理动作逻辑"""
        # 获取竞品关键词
        competitive_keywords = [keyword.strip() for keyword in runConfig.get("competitiveKeywords", "").split(',') if keyword.strip()]

        # 随机选择笔记的概率
        click_probability = 0.2  # 定义点击概率为 20%

        if searchContent:
            # 搜索结果元素
            elements = self.device_session.find_all_by_xpath("//*[@resource-id='com.xingin.xhs:id/g8r']")
        elif operateMenu == "仅附近页":
            # 附近页面的内容卡片是LinearLayout，包含完整的content-desc信息
            elements = self.device_session.find_all_by_xpath("//android.widget.LinearLayout[@content-desc and contains(@content-desc, '来自')]")
        else:
            # 发现页笔记元素 - 直接查找包含content-desc的父级FrameLayout
            elements = self.device_session.find_all_by_xpath("//android.widget.FrameLayout[@content-desc and contains(@content-desc, '来自')]")

        if elements:
            found_any = False
            for element in elements:
                try:
                    is_video = False

                    # 随机选择笔记进行点击
                    random_chance = random.random()
                    if random_chance > click_probability:
                        continue

                    # 搜索页跟发现页的content_desc要区分
                    if searchContent:
                        content_desc = element.get_attribute("text")
                    else:
                        # 发现页：直接获取FrameLayout的content-desc
                        content_desc = element.get_attribute("content-desc")

                    if content_desc in self.clicked_elements:
                        logger.info(f"跳过已点击的内容:{content_desc}")
                        continue

                    # 检查是否包含竞品关键词
                    if any(ck in content_desc for ck in competitive_keywords):
                        logger.info(f"跳过包含竞品关键词的内容: {content_desc}")
                        continue

                    logger.info(f"随机选择笔记: {content_desc[:30]}...")

                    # 检查是否为视频 - 根据content-desc判断
                    if content_desc and content_desc.startswith("视频"):
                        is_video = True
                        logger.info("检测到视频内容")
                    else:
                        is_video = False
                        logger.info("检测到图文内容")

                    # 根据页面类型选择点击方式
                    if searchContent:
                        element.click()
                    elif operateMenu == "仅附近页":
                        # 附近页面的LinearLayout本身就是可点击的
                        element.click()
                    else:
                        # 发现页需要查找内部的可点击FrameLayout并点击
                        clickable_element = self.device_session.find_by_xpath(f"//android.widget.FrameLayout[@content-desc='{content_desc}']//android.widget.FrameLayout[@resource-id='com.xingin.xhs:id/au2' and @clickable='true']")
                        clickable_element.click()

                    search_button_elements = self.device_session.find_all_by_xpath(".//*[@resource-id='com.xingin.xhs:id/luz' and @text='搜索']")
                    if search_button_elements:
                        logger.info("找到搜索按钮，执行返回操作")
                        CommonMobileUtils.back(self.device_session, 2)

                    page_source = self.device_session.get_page_source()
                    if "com.xingin.xhs:id/a2i" not in page_source and "com.xingin.xhs:id/fys" not in page_source:
                        logger.info("当前页面不包含笔记内容标识，跳过")
                        continue
                    time.sleep(1)

                    try:
                        self.perform_actions(page_source, account_name, account_number, runConfig, watchProbability,
                                           operateType, comments, actions, content_desc, is_video)
                    except Exception as e:
                        logger.error(f"执行动作时出错: {e}")

                    self.clicked_elements.add(content_desc)
                    found_any = True

                    # 处理完一个笔记后，检查时间是否到达
                    current_time = datetime.now()
                    if not self.is_in_time_slot(runConfig.get("firstStartTime"), runConfig.get("firstEndTime"), current_time):
                        logger.info("处理笔记后时间检查：时间到达，结束养号")
                        return "time_arrive"

                    break


                except Exception as e:
                    logger.error(f"处理元素时出错: {str(e)}")
                    continue

            if not found_any:
                logger.info("当前页面未找到符合条件的元素，继续滑动...")
                self.device_session.swipe(170, 1400, 170, 600, 50)
                time.sleep(1)
        else:
            logger.info("未找到符合条件的上一级元素，继续滑动...")
            self.device_session.swipe(170, 1400, 170, 600, 50)
            time.sleep(1)

        return "continue"  # 正常继续执行

    def traverse_actions(self, account_name, runConfig, watchProbability, max_rounds,
                        pause_min_time, pause_max_time, account_number, operateType, comments,
                        searchContent, actions, title_keywords, blogger_keywords, operateMenu):
        """遍历执行动作"""
        swipe_count = 0
        round_count = 0

        while round_count < max_rounds:
            # 处理每个动作
            result = self.handle_action(account_name, runConfig, watchProbability, account_number, operateType,
                                      comments, actions, title_keywords, blogger_keywords, searchContent, operateMenu)

            # 如果处理笔记后时间到达，直接返回
            if result == "time_arrive":
                return "time_arrive"

            # 检查滑动次数是否达到 10
            swipe_count += 1
            if swipe_count >= 10:
                runConfig = glv["common_var"][self.custom_udid]["runConfig"]

                # 获取时间段配置
                firstStartTime = runConfig.get("firstStartTime")
                firstEndTime = runConfig.get("firstEndTime")
                secondStartTime = runConfig.get("secondStartTime")
                secondEndTime = runConfig.get("secondEndTime")
                thirdStartTime = runConfig.get("thirdStartTime")
                thirdEndTime = runConfig.get("thirdEndTime")

                # 如果 showTime 为 1，更新时间为全天
                show_time = runConfig.get("showTime")
                if show_time == "1":
                    runConfig["firstStartTime"] = "00:00"
                    runConfig["firstEndTime"] = "23:59"
                    logger.info("时间已更新为全天: 00:00 到 23:59")
                else:
                    logger.info("showTime 为 0，时间保持不变")

                # 删除发布检查逻辑，专注于养号

                # 校验时间是否已经到了
                current_time = datetime.now()
                if not self.is_in_time_slot(firstStartTime, firstEndTime, current_time) and \
                   not self.is_in_time_slot(secondStartTime, secondEndTime, current_time) and \
                   not self.is_in_time_slot(thirdStartTime, thirdEndTime, current_time):
                    logger.info("时间到达，重启小红书")
                    return "time_arrive"
                else:
                    logger.info("时间未到")

                logger.info(f"完成第 {round_count + 1} 轮，准备进入下一轮")
                swipe_count = 0
                round_count += 1

                # 暂停逻辑
                pause_time = random.uniform(pause_min_time, pause_max_time)
                logger.info(f"暂停 {pause_time:.2f} 秒...")
                time.sleep(pause_time)

                # 判断是否达到了最大轮数
                if round_count >= max_rounds:
                    logger.info("已达到最大轮数，停止执行")
                    return True
        return False



    def get_current_account_info(self):
        try:
            print("🔍 获取当前登录账号信息...")
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs" not in page_source:
                self.device_session.activate_app("com.xingin.xhs")
                logger.info("应用启动成功")
                time.sleep(random.randint(8, 15))

            me_tab = self.device_session.find_by_id("com.xingin.xhs:id/du6")
            me_tab.click()
            time.sleep(2)

            xhs_id_el = self.device_session.find_by_id("com.xingin.xhs:id/gxr")
            xhs_id_text = xhs_id_el.get_attribute("text").replace("小红书号：", "").strip()

            nickname = ""
            try:
                nickname_el = self.device_session.find_by_id("com.xingin.xhs:id/gxp")
                nickname = nickname_el.get_attribute("text")
            except:
                pass

            print("🆔 当前登录账号信息：")
            print(f"账号昵称：{nickname}")
            print(f"小红书号：{xhs_id_text}")
            return nickname, xhs_id_text

        except Exception as e:
            print(f"[我] 获取当前账号信息失败：{e}")
            return "", ""
        
    def run_yanghao(self, account_number=None, account_name=None):
        """
        主要的养号运行逻辑
        """

        # 确保应用状态正常
        self.ensure_app_ready()

        # 获取当前账号信息（如果没有传入的话）
        if not account_name or not account_number:
            account_name, account_number = self.get_current_account_info()

        # 如果没有提供账号信息，自动获取
        if not account_number:
            logger.info("没有账号")
            return "error"

        if not account_name:
            logger.info("没有用户名")
            return "error"
        
        # 获取养号配置
        runConfig = self.fetch_account_config(account_number)

        # 获取 runConfig
        if not runConfig:
            logger.info(f"UDID {self.custom_udid} 的配置未找到，跳过此次操作")
            return
        
        # if runConfig :
        #     shouye_tab = self.device_session.find_by_id("com.xingin.xhs:id/du5")
        #     shouye_tab.click()
            
        # 记录上一次搜索内容
        previous_search_content = None
        current_round_count = 0
        last_time_slot = None

        try:
                # 判断是否需要show time
                show_time = runConfig.get("showTime")
                if show_time == "1":
                    runConfig["firstStartTime"] = "00:00"
                    runConfig["firstEndTime"] = "23:59"
                    logger.info("时间已更新为全天: 00:00 到 23:59")

                # 获取配置参数
                operateType = runConfig["taskType"]
                operateMenu = runConfig["operateMenu"]
                title_keywords = runConfig["titleKeywords"]
                blogger_keywords = runConfig["bloggerKeyword"]
                searchContent = runConfig["searchKeywords"]
                comments = runConfig["commentContents"]
                startTime = runConfig["startTime"]
                endTime = runConfig["endTime"]
                max_rounds = runConfig["executeMaxRounds"]
                pause_min_time = runConfig["pauseMinTime"]
                pause_max_time = runConfig["pauseMaxTime"]
                watchProbability = runConfig["watchProbability"]

                # 时间段配置
                firstStartTime = runConfig.get("firstStartTime")
                firstEndTime = runConfig.get("firstEndTime")
                secondStartTime = runConfig.get("secondStartTime")
                secondEndTime = runConfig.get("secondEndTime")
                thirdStartTime = runConfig.get("thirdStartTime")
                thirdEndTime = runConfig.get("thirdEndTime")

                # 检查总时间限制
                start_time_obj = datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S")
                end_time_obj = datetime.strptime(endTime, "%Y-%m-%d %H:%M:%S")
                current_time = datetime.now()

                if current_time < start_time_obj or current_time > end_time_obj:
                    logger.info("当前时间不在总时间限制范围内")
                    time.sleep(3)
                    return

                # 检查时间段
                time_slots = [
                    (firstStartTime, firstEndTime),
                    (secondStartTime, secondEndTime),
                    (thirdStartTime, thirdEndTime),
                ]

                in_valid_time_slot = False
                for index, (start_time, end_time) in enumerate(time_slots):
                    if self.is_in_time_slot(start_time, end_time, current_time):
                        in_valid_time_slot = True
                        if last_time_slot != index:
                            current_round_count = 0
                            last_time_slot = index
                            logger.info(f"进入新的时间段 {index + 1}，重置current_round_count为0")
                        break

                if in_valid_time_slot:
                    logger.info("当前时间在有效时间段内，执行任务")
                    page_source = self.device_session.get_page_source()
                    if "com.xingin.xhs" not in page_source:
                        self.device_session.activate_app("com.xingin.xhs")
                        logger.info("应用启动成功")
                        time.sleep(random.randint(8, 15))
                else:
                    page_source = self.device_session.get_page_source()
                    # if "com.xingin.xhs" in page_source:
                    #     self.device_session.close_app("com.xingin.xhs")
                    #     logger.info("应用关闭成功")
                    logger.info("当前时间不在有效时间段内")
                    time.sleep(3)
                    return

                # 点击首页
                shouye_tab = self.device_session.find_by_id("com.xingin.xhs:id/du5")
                shouye_tab.click()
                
                # 根据操作类型决定搜索内容
                if operateType == "自动养号" and operateMenu == "仅发现页":
                    searchContent = None
                    faxian_buttons = self.device_session.find_all_by_text("发现")
                    if faxian_buttons:
                        faxian_buttons[0].click()
                    time.sleep(2)
                elif operateMenu == "仅搜索页":
                    searchContent = runConfig["findKeywords"]
                elif operateMenu == "仅附近页":
                    searchContent = None
                    # 导航到附近页面
                    if not self.navigate_to_nearby():
                        logger.error("无法进入附近页面，跳过此次操作")
                        return "error"
                elif operateType == "定向推号":
                    searchContent = runConfig["searchKeywords"]

                # 拼接动作状态
                actions = []
                if runConfig["watch"] == '1':
                    actions.append("看")
                if runConfig["praise"] == '1':
                    actions.append("赞")
                if runConfig["collect"] == '1':
                    actions.append("藏")
                if runConfig["comment"] == '1':
                    actions.append("评")
                if runConfig["visitHome"] == '1':
                    actions.append("关注")

                # 处理搜索关键词
                if searchContent and (',' in searchContent or '，' in searchContent):
                    search_keywords_list = [kw.strip() for kw in re.split(r'[，,]', searchContent) if kw.strip()]
                else:
                    search_keywords_list = [searchContent] if searchContent else []

                # 执行养号逻辑
                if search_keywords_list:
                    for keyword in search_keywords_list:
                        if keyword != previous_search_content:
                            if previous_search_content:
                                CommonMobileUtils.back(self.device_session, 2)

                            logger.info(f"正在搜索关键词: {keyword}")
                            self.search_for_content(keyword)
                            previous_search_content = keyword

                        result = self.traverse_actions(
                            account_name, runConfig, watchProbability, int(max_rounds),
                            int(pause_min_time), int(pause_max_time), account_number,
                            operateType, comments, keyword, actions, title_keywords, blogger_keywords, operateMenu
                        )

                        if result == "time_arrive":
                            return "time_arrive"

                    time.sleep(60)
                else:
                    if current_round_count < int(max_rounds):
                        result = self.traverse_actions(
                            account_name, runConfig, watchProbability, int(max_rounds),
                            int(pause_min_time), int(pause_max_time), account_number,
                            operateType, comments, searchContent, actions, title_keywords, blogger_keywords, operateMenu
                        )

                        if result == "time_arrive":
                            return "time_arrive"

                        current_round_count += 1
                    else:
                        logger.info("已达到最大轮数，跳过执行")

        except KeyboardInterrupt:
            logger.info("用户中断养号程序")
            return "interrupted"
        except Exception as e:
            logger.error(f"养号执行异常: {e}")
            return "error"
