#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Appium 脚本：点击指定 ID 元素 + 下拉刷新
"""

import time
import subprocess
from appium import webdriver
from appium.options.android import UiAutomator2Options

class SimpleClickTest:
    def __init__(self):
        self.driver = None
        self.udid = self.get_connected_device()
        if not self.udid:
            raise RuntimeError("未找到可用设备")
        self.setup_driver()

    def get_connected_device(self):
        """返回首个已连接设备的UDID"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行
            for line in lines:
                if '\tdevice' in line:
                    return line.split('\t')[0]
        except Exception as e:
            print(f"获取设备失败: {e}")
        return None

    def setup_driver(self):
        """配置并连接 Appium"""
        capabilities = {
            "platformName": "Android",
            "appium:automationName": "UiAutomator2",
            "appium:udid": self.udid,
            "appium:deviceName": "Phone",
            "appium:noReset": True
        }
        options = UiAutomator2Options().load_capabilities(capabilities)
        self.driver = webdriver.Remote("http://127.0.0.1:4723", options=options)
        print("✅ 成功连接 Appium")

    def activate_app(self, package_name="com.xingin.xhs"):
        """激活小红书"""
        self.driver.activate_app(package_name)
        time.sleep(2)

    def click_by_id(self, resource_id):
        """点击指定 ID 元素"""
        try:
            self.driver.find_element("id", resource_id).click()
            print(f"✅ 点击成功: {resource_id}")
        except Exception as e:
            print(f"❌ 点击失败: {e}")

    def get_page_source(self):
        """获取页面UI树并打印"""
        try:
            page_source = self.driver.page_source
            print("=" * 80)
            print("📱 页面UI树:")
            print("=" * 80)
            print(page_source)
            print("=" * 80)
            return page_source
        except Exception as e:
            print(f"❌ 获取页面源码失败: {e}")
            return None

    def swipe_down(self):
        from appium.webdriver.common.appiumby import AppiumBy
        import random
        import time

        # 直接尝试执行评论操作（测试版本）
        print("准备执行评论操作")

        # 预处理评论列表
        comment = "非常好！"
        print(f"已选择评论内容: {comment}")

        try:
            input_field = None

            # Step 1: 尝试直接找到 f33 输入框
            try:
                input_field = self.driver.find_element(AppiumBy.ID, "com.xingin.xhs:id/f33")
                print("直接找到评论输入框 f33")
            except Exception as e:
                print(f"直接查找 f33 失败: {e}")

            # Step 2: 如果没有找到 f33，则尝试点击 dvr 激活输入框
            if not input_field:
                try:
                    dvr_field = self.driver.find_element(AppiumBy.ID, "com.xingin.xhs:id/dvr")
                    dvr_field.click()
                    print("点击 dvr 激活评论输入框")
                    time.sleep(1)
                    input_field = self.driver.find_element(AppiumBy.ID, "com.xingin.xhs:id/f33")
                except Exception as e:
                    print(f"查找或点击 dvr 失败: {e}")

            # Step 3: 输入评论并点击发送
            if input_field:
                input_field.click()
                time.sleep(1)

                input_field.send_keys(comment)
                time.sleep(1)

                send_button = None
                try:
                    # 多种可能的发送按钮 ID
                    for btn_id in ["com.xingin.xhs:id/lkn", "com.xingin.xhs:id/fb0", "com.xingin.xhs:id/fbp"]:
                        try:
                            send_button = self.driver.find_element(AppiumBy.ID, btn_id)
                            if send_button and send_button.is_enabled():
                                print(f"找到发送按钮: {btn_id}")
                                break
                        except:
                            continue
                except Exception as e:
                    print(f"发送按钮查找失败: {e}")

                if send_button:
                    send_button.click()
                    print(f"评论成功: {comment}")
                else:
                    print("未找到发送按钮")
            else:
                print("未找到评论输入框")

        except Exception as e:
            print(f"评论操作失败: {e}")

        time.sleep(1)


    def cleanup(self):
        if self.driver:
            self.driver.quit()

def main():
    tester = None
    try:
        tester = SimpleClickTest()
        # 下拉刷新测试
        tester.swipe_down()

        # 如果需要点击元素，取消下面的注释并修改ID
        # tester.click_by_id("你要点击的ID")

    except Exception as e:
        print(f"❌ 出错: {e}")
    finally:
        if tester:
            tester.cleanup()

if __name__ == "__main__":
    main()
