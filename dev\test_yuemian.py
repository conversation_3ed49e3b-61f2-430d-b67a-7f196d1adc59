from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
from utils.common import CommonMobileUtils
from utils.xbot import DeviceSession

import re
import datetime


class yuemian:

    def __init__(self, device_session):
        self.device_session = device_session

    def y(self):
        ret = {}
        ret["setInterviewTime"] = "2025-06-13 16:20:00"
        ret["weworkData"] = "abc"

        CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='约面试']")
        time.sleep(2)
        # 解析面试时间
        time_match = re.search(r'\d+-\d+-(\d+) (\d+):(\d+):\d+', ret["setInterviewTime"])

        calendar_objs = self.device_session.find_all_by_xpath(
            ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_calendar_date' and @text='" + str(int(time_match.group(1))) + "']")
        calendar_objs[0].click()
        time.sleep(1)

        move = 50


        bound_hour = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wheel_middle").get_bounding()

        hour_move_count = int(time_match.group(2)) - 8
        today = datetime.datetime.now()
        if today.strftime("%Y-%m-%d") in ret["setInterviewTime"]:
            hour = int(datetime.datetime.now().strftime("%H"))
            hour_move_count = int(time_match.group(2)) - hour

        for i in range(hour_move_count):
            self.device_session.swipe(bound_hour[0] + 20, bound_hour[1] + 20, bound_hour[0] + 20, bound_hour[1] - move)

        bound_min = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wheelview_right").get_bounding()
        min_move_count = int(time_match.group(3)) // 5
        for i in range(min_move_count):
            self.device_session.swipe(bound_min[0] + 20, bound_min[1] + 20, bound_min[0] + 20, bound_min[1] - move)
        # 点击确定按钮
        CommonMobileUtils.click_use_xpath(self.device_session,
                                          ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_confirm' and @text='确定']", "click")

        # 设置备注
        desc = ""
        try:
            desc = "面试老师：(微信手机同号)。可以就近分配，面试经理会联系你跟你沟通。"
        except Exception as e:
            1

        # 点击文本框
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_interview_message").click()

        # 输入内容
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_interview_message").input(desc)

        # 输入法隐藏
        CommonMobileUtils.back(self.device_session, 1)
        # 点击发送面试邀请
        CommonMobileUtils.click_use_xpath(self.device_session,
                                          ".//*[@resource-id='com.hpbr.bosszhipin:id/btn_create' and @text='发送面试邀请']",
                                          "click")

        page_source = CommonMobileUtils.get_page_source(self.device_session)
        if "com.hpbr.bosszhipin:id/tv_positive" in page_source and "接受" in page_source:
            # 不良记录是否接受
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_positive']")
        page_source = CommonMobileUtils.get_page_source(self.device_session)
        if "暂不开启" in page_source:
            # 点击暂不开启
            CommonMobileUtils.click_use_xpath(self.device_session,
                                              ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='暂不开启']",
                                              "click")
        page_source = CommonMobileUtils.get_page_source(self.device_session)
        if "com.hpbr.bosszhipin:id/btn_confirm" in page_source and "继续" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/btn_confirm']")
        page_source = CommonMobileUtils.get_page_source(self.device_session)
        if "com.hpbr.bosszhipin:id/tv_positive" in page_source and "保证不爽约" in page_source:
            # 保证不爽约
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_positive']")
        page_source = CommonMobileUtils.get_page_source(self.device_session)
        if "返回" in page_source:
            # 点击返回
            CommonMobileUtils.click_use_xpath(self.device_session,
                                              ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='返回']",
                                              "click")



APPIUM_SERVER_URL = "http://127.0.0.1:4723"

capabilities = {
    "platformName": "Android",
    "appium:automationName": "UiAutomator2",
    "appium:udid": "A7JC9X3210W02145",
    "appium:platformVersion": "10",
    "appium:deviceName": "test",
    "appium:noReset": True,
    "appium:uiautomator2ServerLaunchTimeout": 120000,
    "appium:uiautomator2ServerInstallTimeout": 120000
}
appium_options = UiAutomator2Options().load_capabilities(capabilities)

print("--- Capabilities 准备就绪，即将连接 Appium Server ---")
print(capabilities)

# 尝试连接 Appium Server 并创建会话
try:
    driver = webdriver.Remote(command_executor=APPIUM_SERVER_URL, options=appium_options)
    print("\n--- Appium 会话创建成功！ ---")
    print("Driver 对象:", driver)
    driver.activate_app("com.hpbr.bosszhipin")
    device_session = DeviceSession(driver)
    yue = yuemian(device_session)
    yue.y()
    # 最后退出
    driver.quit()
    print("\n--- Driver 已退出 ---")

except Exception as e:
    print("\n--- 创建 Driver 时发生错误！---")
    print("错误信息:", e)
    print("\n请检查 Appium Server 日志获取更详细的错误原因。")

