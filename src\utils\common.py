import requests
import json
import time
from .logger import logger

class GlobalVariables:
    def __init__(self):
        self._data = {}

    def __contains__(self, key):
        return key in self._data

    def __getitem__(self, key):
        return self._data.get(key)

    def __setitem__(self, key, value):
        self._data[key] = value
        
    def __getattr__(self, name):
        return self._data.get(name)

    def __setattr__(self, name, value):
        if name == '_data':
            super().__setattr__(name, value)
        else:
            self._data[name] = value

    def to_dict(self):
        return self._data

    def keys(self):
        """返回内部字典的所有键。"""
        return self._data.keys()

# 创建一个全局实例，让其他模块可以导入和使用
glv = GlobalVariables()

class CommonUtils:
    def __init__(self):
        self.logger = logger
        self.global_key = "common_var"
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.commonMobileUtils = CommonMobileUtils()

    def http_post(self, url, data=None, json_data=None, headers=None, timeout=30):
        """发送HTTP POST请求"""
        response = requests.post(
            url,
            data=data,
            json=json_data,
            headers=self.headers,
            timeout=timeout
        )
        response.raise_for_status()
        return response.json()


    def http_get(self, url, params=None, headers=None, timeout=30):
        """发送HTTP GET请求"""
        response = requests.get(
            url,
            params=params,
            headers=self.headers,
            timeout=timeout
        )
        response.raise_for_status()
        return response.json()

    def get_redirected_url(self,url):
        redirect_url = ""
        i = 0
        while True:
            if i>1:
                break
            i = i +1
            response = requests.get(url, allow_redirects=False)
            redirect_url = response.headers['Location']
            if redirect_url!="":
                break
            time.sleep(2)

        return redirect_url


    def push_mingdao_multi_add(self,field_dict_list,sign,app_key,work_sheet_id):
            if len(field_dict_list) == 0:
                return
            controls_multi = []
            for field_dict in field_dict_list:
                controls = []
                for controlId, value in field_dict.items(): 
                    control = {
                            "controlId":controlId,
                            "value":value
                    }
                    controls.append(control)
                controls_multi.append(controls)

            request = self.build_mingdao_request(field_dict,sign,app_key,work_sheet_id,"")
            request["rows"] = controls_multi
            url = "https://api.mingdao.com/v2/open/worksheet/addRows"
            headers = {
                'Content-Type': 'application/json'
            }
            requests.request("POST", url, headers=headers, data=json.dumps(request), timeout=180)

    def build_mingdao_request(field_dict,sign,app_key,work_sheet_id,row_id):
            request = {
                "appKey":app_key,
                "sign":sign,
                "worksheetId":work_sheet_id
            }
            if row_id != "":
                request["rowId"] = row_id
            return request

class CommonMobileUtils:

    #返回上一级
    @staticmethod
    def back(device_session, num=1):
        for i in range(0,num):
            time.sleep(0.2)
            device_session.back()

    @staticmethod
    def click_use_xpath(device_session,xpath_str="",click_str=""):
        try:
            if click_str == "dbclick":
                device_session.find_by_xpath(xpath_str).dbclick()
            else:
                device_session.find_by_xpath(xpath_str).click()
            return True
        except Exception as e:
            logger.warning(f"设备 {device_session.custom_name} 点击 XPath 失败: {xpath_str}, 错误: {e}", exc_info=True)


    #回退到指定的页面
    @staticmethod
    def back_to_appoint_page_use_not_flags(device_session, flags):
        i = 0
        while(True):
            i = i+1
            if i > 10:
                break
            time.sleep(0.8)
            page_source = device_session.get_page_source()

            is_end = False
            for flag_list in flags:
                is_and = True
                for flag in flag_list:
                    if flag not in page_source:
                        is_and = False
                        break
                if is_and:
                    is_end = True
                    break

            if is_end:
                CommonMobileUtils.back(device_session,1)
            else:
                break  

    #回退到指定的页面
    @staticmethod
    def back_to_appoint_page_use_xpath(device_session, xpath_list):
        for xpath in xpath_list:
            while True:
                results = device_session.find_all_by_xpath(xpath)
                if len(results)>0:
                    break
                CommonMobileUtils.back(device_session,1)
                time.sleep(0.3)

    #回退到指定的页面
    @staticmethod
    def back_to_appoint_page_use_xpath_all_have(device_session, xpath_list):
        is_all_have = False
        i = 0
        while True:
            i = i + 1
            if i > 10:
                break

            if is_all_have:
                break
                
            for xpath in xpath_list:
                results = device_session.find_all_by_xpath(xpath)
                if len(results)==0:
                    is_all_have = False
                    CommonMobileUtils.back(device_session,1)
                    break
                is_all_have = True
                
            
    #回退到指定的页面
    @staticmethod
    def back_to_appoint_page(device_session, source_id):
        time.sleep(0.1)
        i = 0
        while True:
            i = i+1
            if i > 10:
                break
            page_source = device_session.get_page_source()
            if source_id in page_source:
                break           
            else:
                CommonMobileUtils.back(device_session,1)
            time.sleep(0.1)

    # 关闭可能存在的关闭按钮
    @staticmethod
    def close(device_session,xpath_str_map):
        for key in xpath_str_map:
            page_source = device_session.get_page_source()
            if key in page_source:
                CommonMobileUtils.click_use_xpath(device_session,xpath_str_map[key],"click")

    




