import subprocess
import time
import os
import atexit
import logging
from datetime import datetime
import socket
import platform
import threading
from typing import Dict, Optional

logger = logging.getLogger('appium_manager')
logger.propagate = False

handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s %(name)s %(levelname)s %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


class AppiumServerManager:
    _instance = None
    _lock = threading.Lock()

    # 端口管理常量
    BASE_PORT = 4723
    BASE_SYSTEM_PORT = 8200
    MAX_PORT = 60000  # 最大端口号限制
    PORT_INCREMENT = 4  # 端口递增步长

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._init_manager()
        return cls._instance

    def _init_manager(self):
        self.servers: Dict[str, dict] = {}  # {udid: {process: subprocess.Popen, port: int, system_port: int}}
        self.port_lock = threading.Lock()
        self.port_map = {}  # {port: udid}
        self.system_port_map = {}  # {system_port: udid}

    @staticmethod
    def _normalize_udid(udid: str) -> str:
        if ":" in udid:
            return udid.split(":")[0]
        else:
            return udid

    def get_available_port(self) -> Optional[int]:
        """获取可用的Appium服务器端口"""
        with self.port_lock:
            port = self.BASE_PORT
            while port < self.MAX_PORT:
                if port not in self.port_map and not self._is_port_in_use(port):
                    return port
                port += self.PORT_INCREMENT
            logger.error("No available ports left!")
            return None

    def get_available_system_port(self) -> Optional[int]:
        """获取可用的系统端口"""
        with self.port_lock:
            system_port = self.BASE_SYSTEM_PORT
            while system_port < self.MAX_PORT:
                if system_port not in self.system_port_map and not self._is_port_in_use(system_port):
                    return system_port
                system_port += self.PORT_INCREMENT
            logger.error("No available system ports left!")
            return None

    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否正在使用"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0

    def start_single_server_for_all_device(self, udid: str) -> Optional[dict]:
        normalized_udid = self._normalize_udid(udid)
        port = 4723
        system_port = 8200
        if self._is_port_in_use(port):
            return None
        return self._start_server(normalized_udid, port, system_port)

    def start_server_for_device(self, udid: str) -> Optional[dict]:
        normalized_udid = self._normalize_udid(udid)
        if normalized_udid in self.servers:
            server_info = self.get_server_info(normalized_udid)
            if self._is_port_in_use(server_info["port"]):
                logger.debug(f"Appium server already running for device {normalized_udid}")
                return self.servers[normalized_udid]
            else:
                port = server_info["port"]
                system_port = server_info["system_port"]
                return self._start_server(normalized_udid, port, system_port)
        """为指定设备启动Appium服务器"""
        port = self.get_available_port()
        system_port = self.get_available_system_port()
        if port is None or system_port is None:
            return None
        return self._start_server(normalized_udid,port,system_port)

    def _start_server(self, normalized_udid, port, system_port):
        logger.info(f"Starting Appium server for device {normalized_udid} on port {port}, system port {system_port}")
        # 构建启动命令
        appium_executable = "appium"
        if platform.system() == "Windows":
            try:
                npm_prefix = subprocess.check_output("npm config get prefix", shell=True).decode().strip()
                appium_executable = os.path.join(npm_prefix, "appium.cmd")
            except subprocess.CalledProcessError as e:
                logger.error(f"Error getting npm prefix: {e}")
                return None

        log_file = self._get_log_file_path(port)

        try:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            log_fp = open(log_file, 'a', encoding='utf-8')

            command = [
                appium_executable,
                "--address", "0.0.0.0",
                "--port", str(port),
                "--base-path", "",
                "--log-level", "info",
                "--use-drivers", "uiautomator2",
            ]

            process = subprocess.Popen(
                command,
                stdout=log_fp,
                stderr=subprocess.STDOUT,
            )

            # 等待服务器启动
            if not self._wait_for_server_start(port, timeout=30):
                logger.error(f"Appium server failed to start on port {port}")
                process.terminate()
                return None

            server_info = {
                'process': process,
                'port': port,
                'system_port': system_port,
                'log_file': log_file,
                'log_fp': log_fp
            }

            self.servers[normalized_udid] = server_info
            self.port_map[port] = normalized_udid
            self.system_port_map[system_port] = normalized_udid

            logger.info(f"Appium server started successfully for device {normalized_udid} (PID: {process.pid})")
            return server_info

        except Exception as e:
            logger.error(f"Failed to start Appium server for device {normalized_udid}: {e}")
            if 'log_fp' in locals():
                log_fp.close()
            return None

    def _wait_for_server_start(self, port: int, timeout: int) -> bool:
        """等待Appium服务器启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._is_port_in_use(port):
                time.sleep(2)  # 额外等待确保完全启动
                return True
            time.sleep(1)
        return False

    def _get_log_file_path(self, port: str) -> str:
        """获取日志文件路径"""
        today_str = datetime.now().strftime("%Y-%m-%d")
        return os.path.join("logs", "appium", f"{port}_{today_str}.log")

    def stop_server_for_device(self, udid: str) -> bool:
        normalized_udid = self._normalize_udid(udid)

        """停止指定设备的Appium服务器"""
        logger.warning(f"开始停止 Appium server for device {normalized_udid}")

        if normalized_udid not in self.servers:
            logger.warning(f"No Appium server found for device {normalized_udid}")
            return False
        server_info = self.servers[normalized_udid]
        process = server_info['process']
        port = server_info['port']
        system_port = server_info['system_port']

        logger.info(f"[Appium Manager] 正在停止设备 {normalized_udid} 的Appium服务器 (PID: {process.pid}, Port: {port})")

        try:
            # 记录进程状态
            retcode = process.poll()

            if retcode is None:
                logger.debug(f"[Appium Manager] 进程仍在运行，准备终止...")
            else:
                logger.warning(f"[Appium Manager] 进程已异常退出 (返回码: {retcode})")

            # 关闭日志文件
            if 'log_fp' in server_info and not server_info['log_fp'].closed:
                server_info['log_fp'].close()
                logger.debug(f"[Appium Manager] 已关闭日志文件: {server_info['log_file']}")

            # 终止进程
            process.terminate()
            logger.debug(f"[Appium Manager] 已发送终止信号(SIGTERM)")

            try:
                process.wait(timeout=10)
                logger.info(f"[Appium Manager] Appium服务器已正常停止")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.warning(f"[Appium Manager] 强制终止进程(SIGKILL)")

            # 清理映射关系
            del self.port_map[port]
            del self.system_port_map[system_port]
            del self.servers[normalized_udid]

            logger.info(f"[Appium Manager] 成功停止设备 {normalized_udid} 的Appium服务器")
            return True

        except Exception as e:
            logger.error(f"[Appium Manager] 停止Appium服务器失败: {e}", exc_info=True)
            return False

    def get_server_info(self, udid: str) -> Optional[dict]:
        normalized_udid = self._normalize_udid(udid)
        """获取设备的Appium服务器信息"""
        return self.servers.get(normalized_udid)

    def cleanup(self):
        """清理所有Appium服务器"""
        with self.port_lock:
            for normalized_udid in list(self.servers.keys()):
                self.stop_server_for_device(normalized_udid)


# 全局单例
appium_manager = AppiumServerManager()
atexit.register(appium_manager.cleanup)