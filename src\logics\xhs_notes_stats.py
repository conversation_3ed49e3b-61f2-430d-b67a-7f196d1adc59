import time
import random
import re
import requests
from datetime import datetime
from xml.etree import ElementTree as ET

from ..utils import logger, glv, CommonMobileUtils
from ..services.tool_service import ToolService


class XhsNotesStatsLogic:
    """小红书笔记数据统计逻辑类"""
    
    def __init__(self, custom_udid, domain=None):
        self.domain = domain
        self.custom_udid = custom_udid
        self.device_session = None
        self.notes_data = []  # 存储笔记数据
        
        # 明道云配置 - 请根据实际情况修改
        self.APP_KEY = "f08bf7f7cfe8c038"  # 请替换为实际的APP_KEY
        self.SIGN = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="  # 请替换为实际的SIGN
        self.BJK_WORKSHEET_ID = "jzbjk"  # 请替换为实际的工作表ID
        self.OWNER_ID = "b6c64060-3fbb-44c5-87ee-52c5f04e50af"  # 请替换为实际的OWNER_ID
        
    def init(self, device_session):
        """初始化设备会话"""
        self.device_session = device_session
        
    def ensure_app_ready(self):
        """确保小红书应用状态正常"""
        try:
            # 激活小红书应用
            self.device_session.activate_app("com.xingin.xhs")
            time.sleep(3)
            
            # 检查是否在首页，如果不在则点击首页
            home_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du5")
            if home_tabs:
                home_tab = home_tabs[0]
                is_selected = home_tab.get_attribute("selected") == "true"
                if not is_selected:
                    home_tab.click()
                    time.sleep(2)
                    
        except Exception as e:
            logger.error(f"确保应用状态失败: {e}")
            
    def get_current_account_info(self):
        """获取当前账号信息"""
        try:
            # 点击"我"页面
            me_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du6")
            if me_tabs:
                me_tab = me_tabs[0]
                me_tab.click()
                time.sleep(3)
                
            # 获取昵称
            nickname_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/gxp")
            nickname = nickname_elements[0].get_attribute("text") if nickname_elements else "未知昵称"
            
            # 获取小红书号
            xhs_number_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/gxr")
            xhs_number = ""
            if xhs_number_elements:
                xhs_text = xhs_number_elements[0].get_attribute("text")
                # 提取小红书号：从"小红书号：***********"中提取数字
                match = re.search(r'小红书号：(\d+)', xhs_text)
                if match:
                    xhs_number = match.group(1)
                    
            logger.info(f"获取到账号信息: 昵称={nickname}, 小红书号={xhs_number}")
            return nickname, xhs_number
            
        except Exception as e:
            logger.error(f"获取账号信息失败: {e}")
            return "未知昵称", "未知号码"
            
    def click_notes_tab(self):
        """点击笔记tab"""
        try:
            # 查找笔记tab并点击
            notes_tabs = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/dig" and @text="笔记"]'
            )
            if notes_tabs:
                notes_tabs[0].click()
                time.sleep(2)
                logger.info("已点击笔记tab")
                return True
            else:
                logger.warning("未找到笔记tab")
                return False
        except Exception as e:
            logger.error(f"点击笔记tab失败: {e}")
            return False
            
    def get_notes_list(self):
        """获取笔记列表"""
        try:
            notes_list = []

            # 查找笔记列表容器
            notes_container = self.device_session.find_by_id("com.xingin.xhs:id/g79")
            if not notes_container:
                logger.warning("未找到笔记列表容器")
                return notes_list

            # 获取当前屏幕的笔记元素
            note_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/au2")

            for note_element in note_elements:
                try:
                    # 从content-desc中提取笔记信息
                    content_desc = note_element.get_attribute("content-desc")
                    if not content_desc or "笔记," not in content_desc:
                        continue

                    # 解析content-desc，格式如："笔记,电解汗管Liu 真的有效果吗,来自mgotta,1赞，476阅读"
                    title = ""
                    view_count = 0

                    try:
                        # 提取标题（在第一个逗号和第二个逗号之间）
                        parts = content_desc.split(',')
                        if len(parts) >= 2:
                            title = parts[1].strip()

                        # 提取阅读数
                        view_match = re.search(r'(\d+)阅读', content_desc)
                        if view_match:
                            view_count = int(view_match.group(1))

                        # 如果从content-desc提取失败，尝试从小眼睛元素获取阅读数
                        if view_count == 0:
                            view_count_elements = note_element.find_elements_by_id("com.xingin.xhs:id/a3q")
                            if view_count_elements:
                                view_text = view_count_elements[0].get_attribute("text")
                                if view_text and view_text.isdigit():
                                    view_count = int(view_text)

                    except Exception as e:
                        logger.warning(f"解析笔记信息失败: {e}")
                        continue

                    if not title:
                        logger.warning(f"未能提取到笔记标题，跳过")
                        continue

                    notes_list.append({
                        'element': note_element,
                        'title': title,
                        'view_count': view_count,
                        'content_desc': content_desc
                    })

                    logger.info(f"找到笔记: {title[:30]}... (阅读数: {view_count})")

                except Exception as e:
                    logger.warning(f"解析笔记元素失败: {e}")
                    continue

            logger.info(f"获取到 {len(notes_list)} 条笔记")
            return notes_list

        except Exception as e:
            logger.error(f"获取笔记列表失败: {e}")
            return []
            
    def get_note_details(self, note_title):
        """获取笔记详情页的数据"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 获取点赞数
            like_count = 0
            try:
                # 从点赞按钮的content-desc获取："点赞 1"
                like_button = self.device_session.find_by_id("com.xingin.xhs:id/g7z")
                if like_button:
                    content_desc = like_button.get_attribute("content-desc")
                    if content_desc:
                        like_match = re.search(r'点赞 (\d+)', content_desc)
                        if like_match:
                            like_count = int(like_match.group(1))

                # 备选方案：从点赞数字元素获取
                if like_count == 0:
                    like_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/g80")
                    if like_elements:
                        like_text = like_elements[0].get_attribute("text")
                        if like_text and like_text.isdigit():
                            like_count = int(like_text)
            except Exception as e:
                logger.warning(f"获取点赞数失败: {e}")

            # 获取收藏数
            collect_count = 0
            try:
                # 从收藏按钮的content-desc获取："收藏 0"
                collect_button = self.device_session.find_by_id("com.xingin.xhs:id/g69")
                if collect_button:
                    content_desc = collect_button.get_attribute("content-desc")
                    if content_desc:
                        collect_match = re.search(r'收藏 (\d+)', content_desc)
                        if collect_match:
                            collect_count = int(collect_match.group(1))
            except Exception as e:
                logger.warning(f"获取收藏数失败: {e}")

            # 获取评论数
            comment_count = 0
            try:
                # 方法1：从评论按钮的content-desc获取："评论 6"
                comment_button = self.device_session.find_by_id("com.xingin.xhs:id/g6e")
                if comment_button:
                    content_desc = comment_button.get_attribute("content-desc")
                    if content_desc:
                        comment_match = re.search(r'评论 (\d+)', content_desc)
                        if comment_match:
                            comment_count = int(comment_match.group(1))

                # 方法2：从评论数字元素获取
                if comment_count == 0:
                    comment_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/g6i")
                    if comment_elements:
                        comment_text = comment_elements[0].get_attribute("text")
                        if comment_text and comment_text.isdigit():
                            comment_count = int(comment_text)

                # 方法3：从评论标签获取："评论 6"
                if comment_count == 0:
                    comment_tab_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/it3")
                    for element in comment_tab_elements:
                        text = element.get_attribute("text")
                        if text and "评论" in text:
                            comment_match = re.search(r'评论 (\d+)', text)
                            if comment_match:
                                comment_count = int(comment_match.group(1))
                                break
            except Exception as e:
                logger.warning(f"获取评论数失败: {e}")

            # 获取分享数（暂时没有找到分享数据）
            share_count = 0

            logger.info(f"笔记详情: {note_title[:20]}... - 点赞:{like_count}, 收藏:{collect_count}, 评论:{comment_count}")

            return {
                'like_count': like_count,
                'collect_count': collect_count,
                'comment_count': comment_count,
                'share_count': share_count
            }

        except Exception as e:
            logger.error(f"获取笔记详情失败: {e}")
            return {
                'like_count': 0,
                'collect_count': 0,
                'comment_count': 0,
                'share_count': 0
            }

    def scroll_and_collect_notes(self, account_name, account_number, max_scrolls=10):
        """滚动收集笔记数据"""
        try:
            all_notes_data = []
            processed_titles = set()  # 避免重复处理
            consecutive_empty_scrolls = 0  # 连续空滚动计数

            for scroll_count in range(max_scrolls):
                logger.info(f"第 {scroll_count + 1} 次滚动收集笔记...")

                # 获取当前屏幕的笔记列表
                notes_list = self.get_notes_list()

                if not notes_list:
                    consecutive_empty_scrolls += 1
                    logger.warning(f"未获取到笔记列表，连续空滚动次数: {consecutive_empty_scrolls}")
                    if consecutive_empty_scrolls >= 3:
                        logger.info("连续3次未获取到笔记，停止滚动")
                        break
                    # 尝试滚动到下一屏
                    self.device_session.swipe(170, 1400, 170, 600, 50)
                    time.sleep(2)
                    continue
                else:
                    consecutive_empty_scrolls = 0  # 重置计数

                # 统计本次新发现的笔记
                new_notes_count = 0

                # 处理每个笔记
                for note_info in notes_list:
                    title = note_info['title']

                    # 跳过已处理的笔记
                    if title in processed_titles:
                        logger.info(f"跳过已处理的笔记: {title[:30]}...")
                        continue

                    processed_titles.add(title)
                    new_notes_count += 1

                    try:
                        # 优先尝试点击小眼睛进入详情页
                        clicked_successfully = False

                        # 方法1：在当前笔记元素内查找小眼睛
                        try:
                            view_count_elements = note_info['element'].find_elements_by_id("com.xingin.xhs:id/a3q")
                            if view_count_elements:
                                view_text = view_count_elements[0].get_attribute("text")
                                logger.info(f"点击小眼睛进入详情页，阅读数: {view_text}")
                                view_count_elements[0].click()
                                clicked_successfully = True
                                time.sleep(3)
                        except Exception as e:
                            logger.warning(f"点击笔记内小眼睛失败: {e}")

                        # 方法2：如果方法1失败，点击笔记本身
                        if not clicked_successfully:
                            try:
                                logger.info(f"尝试点击笔记本身进入详情页...")
                                note_info['element'].click()
                                clicked_successfully = True
                                time.sleep(3)
                            except Exception as e:
                                logger.warning(f"点击笔记本身失败: {e}")

                        # 如果都失败了，跳过这个笔记
                        if not clicked_successfully:
                            logger.error(f"无法点击笔记进入详情页，跳过: {title[:30]}...")
                            continue

                        # 获取详情页数据
                        details = self.get_note_details(title)

                        # 组装笔记数据
                        note_data = {
                            'title': title,
                            'account_name': account_name,
                            'xhs_account': account_number,
                            'view_count': note_info['view_count'],
                            'like_count': details['like_count'],
                            'collect_count': details['collect_count'],
                            'comment_count': details['comment_count'],
                            'share_count': details['share_count'],
                            'collected_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }

                        all_notes_data.append(note_data)
                        logger.info(f"收集笔记数据: {title[:30]}...")

                        # 返回到笔记列表
                        self.device_session.back()
                        time.sleep(2)

                        # 随机等待，避免操作过快
                        time.sleep(random.uniform(1, 2))

                    except Exception as e:
                        logger.error(f"处理笔记失败: {title[:30]}... - {e}")
                        # 尝试返回
                        try:
                            self.device_session.back()
                            time.sleep(1)
                        except:
                            pass
                        continue

                logger.info(f"本次滚动发现 {new_notes_count} 个新笔记，已处理笔记总数: {len(processed_titles)}")

                # 如果本次没有发现新笔记，可能已经滚动到底部
                if new_notes_count == 0:
                    consecutive_empty_scrolls += 1
                    if consecutive_empty_scrolls >= 2:
                        logger.info("连续2次未发现新笔记，可能已到底部，停止滚动")
                        break
                else:
                    consecutive_empty_scrolls = 0

                # 滚动到下一屏
                if scroll_count < max_scrolls - 1:
                    try:
                        # 向上滚动加载更多笔记
                        logger.info("向上滚动加载更多笔记...")
                        self.device_session.swipe(170, 1400, 170, 600, 50) 
                        time.sleep(3)  # 等待更长时间确保加载完成
                    except Exception as e:
                        logger.warning(f"滚动失败: {e}")
                        break

            logger.info(f"总共收集到 {len(all_notes_data)} 条笔记数据")
            return all_notes_data

        except Exception as e:
            logger.error(f"滚动收集笔记失败: {e}")
            return []

    def check_existing_notes_in_mingdao(self, xhs_account):
        """检查明道云中已存在的笔记"""
        logger.info(f"🔍 检查明道云中账号 {xhs_account} 已存在的笔记...")

        existing_notes = {}

        try:
            # 使用过滤器查询当前账号的笔记数据
            query_payload = {
                "appKey": self.APP_KEY,
                "sign": self.SIGN,
                "worksheetId": self.BJK_WORKSHEET_ID,
                "pageSize": 1000,
                "pageIndex": 1,
                "filters": [
                    {
                        "controlId": "xhsh",
                        "filterType": 1,
                        "value": xhs_account
                    }
                ]
            }

            response = requests.post(
                "https://api.mingdao.com/v2/open/worksheet/getFilterRows",
                json=query_payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success") and result.get("data", {}).get("rows"):
                    for row in result["data"]["rows"]:
                        title = row.get("bjbt", "")
                        xhs_account_row = row.get("xhsh", "")
                        row_id = row.get("rowid", "")

                        # 使用标题+账号作为唯一标识
                        key = f"{title}_{xhs_account_row}"
                        existing_notes[key] = row_id

                    logger.info(f"📋 明道云中账号 {xhs_account} 已存在 {len(existing_notes)} 条笔记记录")
                else:
                    logger.info(f"📋 明道云中账号 {xhs_account} 暂无笔记记录")
            else:
                logger.error(f"❌ 查询明道云数据失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")

        except Exception as e:
            logger.error(f"❌ 查询明道云数据失败: {e}")

        return existing_notes

    def sync_notes_to_mingdao(self, notes_data):
        """将笔记数据同步到明道云（支持新增和更新）"""
        logger.info(f"🔄 开始同步 {len(notes_data)} 条数据到明道云...")

        if not notes_data:
            logger.warning("⚠️ 没有数据需要同步")
            return

        # 检查已存在的笔记
        existing_notes = self.check_existing_notes_in_mingdao(notes_data[0]['xhs_account'] if notes_data else "")

        new_notes = []  # 需要新增的笔记
        update_notes = []  # 需要更新的笔记

        for note in notes_data:
            key = f"{note['title']}_{note['xhs_account']}"

            note_data = [
                {"controlId": "bjbt", "value": note["title"]},
                {"controlId": "nicheng", "value": note["account_name"]},
                {"controlId": "xhsh", "value": note["xhs_account"]},
                {"controlId": "xiaoyanjing_number", "value": str(note["view_count"])},
                {"controlId": "pinglun_number", "value": str(note["comment_count"])},
                {"controlId": "dianzan_number", "value": str(note["like_count"])},
                {"controlId": "shoucang_number", "value": str(note["collect_count"])},
                {"controlId": "zhuanfa_number", "value": str(note["share_count"])},
                {"controlId": "ownerid", "value": self.OWNER_ID}
            ]

            if key in existing_notes:
                # 需要更新
                update_notes.append({
                    "rowId": existing_notes[key],
                    "controls": note_data
                })
            else:
                # 需要新增
                new_notes.append(note_data)

        # 新增笔记
        if new_notes:
            logger.info(f"📝 新增 {len(new_notes)} 条笔记...")
            add_payload = {
                "appKey": self.APP_KEY,
                "sign": self.SIGN,
                "worksheetId": self.BJK_WORKSHEET_ID,
                "triggerWorkflow": True,
                "returnRowIds": "true",
                "rows": new_notes
            }

            try:
                response = requests.post(
                    "https://api.mingdao.com/v2/open/worksheet/addRows",
                    json=add_payload,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        logger.info(f"✅ 成功新增 {len(new_notes)} 条笔记")
                    else:
                        logger.error(f"❌ 新增笔记失败: {result}")
                else:
                    logger.error(f"❌ 新增笔记请求失败，状态码: {response.status_code}")

            except Exception as e:
                logger.error(f"❌ 新增笔记失败: {e}")

        # 更新笔记
        if update_notes:
            logger.info(f"🔄 更新 {len(update_notes)} 条笔记...")

            # 批量更新
            for update_note in update_notes:
                update_payload = {
                    "appKey": self.APP_KEY,
                    "sign": self.SIGN,
                    "worksheetId": self.BJK_WORKSHEET_ID,
                    "triggerWorkflow": True,
                    "controls": update_note["controls"],
                    "rowId": update_note["rowId"]
                }

                try:
                    response = requests.post(
                        "https://api.mingdao.com/v2/open/worksheet/editRow",
                        json=update_payload,
                        headers={"Content-Type": "application/json"},
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success"):
                            logger.info(f"✅ 成功更新笔记: {update_note['controls'][0]['value'][:30]}...")
                        else:
                            logger.error(f"❌ 更新笔记失败: {result}")
                    else:
                        logger.error(f"❌ 更新笔记请求失败，状态码: {response.status_code}")

                except Exception as e:
                    logger.error(f"❌ 更新笔记失败: {e}")

        logger.info(f"🎉 数据同步完成！新增: {len(new_notes)} 条，更新: {len(update_notes)} 条")

    def run_stats_collection(self, account_number, account_name, max_scrolls=5):
        """运行笔记数据统计收集"""
        try:
            logger.info("🚀 开始执行小红书笔记数据统计...")

            # 确保应用状态正常
            self.ensure_app_ready()

            # 获取当前账号信息
            # account_name, account_number = self.get_current_account_info()

            # 点击我按钮
            me_tab = self.device_session.find_by_id("com.xingin.xhs:id/du6")
            me_tab.click()
            time.sleep(2)

            if not account_name or not account_number:
                logger.error("❌ 无法获取账号信息，停止统计")
                return False

            # 点击笔记tab
            if not self.click_notes_tab():
                logger.error("❌ 无法点击笔记tab，停止统计")
                return False

            # 滚动收集笔记数据
            notes_data = self.scroll_and_collect_notes(account_name, account_number, max_scrolls)

            if not notes_data:
                logger.warning("⚠️ 未收集到任何笔记数据")
                return False

            # 同步数据到明道云
            self.sync_notes_to_mingdao(notes_data)

            logger.info(f"✅ 笔记数据统计完成！共处理 {len(notes_data)} 条笔记")
            return True

        except Exception as e:
            logger.error(f"❌ 笔记数据统计失败: {e}")
            return False
        finally:
            # 返回首页
            try:
                home_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du5")
                if home_tabs:
                    home_tabs[0].click()
                    time.sleep(2)
            except:
                pass
