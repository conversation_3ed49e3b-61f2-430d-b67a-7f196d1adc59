import subprocess
import sys

import requests
import re
import time
import multiprocessing
import logging
import os
from datetime import datetime  # 导入 datetime 模块
from concurrent.futures import ThreadPoolExecutor


logger = logging.getLogger('adb')
logger.propagate = False

handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s %(name)s %(levelname)s %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

def get_daily_log_file(base_log_dir="logs/adb"):
    today_str = datetime.now().strftime("%Y-%m-%d")
    log_filename = f"adb_{today_str}.log"
    log_file_path = os.path.join(base_log_dir, log_filename)

    if not os.path.exists(base_log_dir):
        os.makedirs(base_log_dir)
        logger.debug(f"Created log directory: {base_log_dir}")

    return log_file_path


# 创建线程池
executor = ThreadPoolExecutor(max_workers=20)


def execute_adb_command(cmd, timeout=10):
    """执行 ADB 命令并返回结果，带超时控制."""
    try:
        logging.debug(f"Executing ADB command: {cmd}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout, encoding= sys.getdefaultencoding())
        stdout = result.stdout.strip()
        stderr = result.stderr.strip()

        if stderr:
            logging.warning(f"ADB command stderr: {stderr}")  # 记录标准错误
        logging.debug(f"ADB command stdout: {stdout}")  # 记录标准输出
        return stdout, stderr
    except subprocess.TimeoutExpired:
        logging.error(f"ADB command timed out: {cmd}")
        return "", "Timeout"
    except FileNotFoundError:
        logging.error(f"ADB command not found.")
        return "", "FileNotFoundError"
    except Exception as e:
        logging.exception(f"Error executing ADB command: {e}")  # 记录异常堆栈
        return "", str(e)


def get_connected_devices():
    """获取已连接的设备列表 (过滤掉 _adb-tls-connect 设备)."""
    stdout, stderr = execute_adb_command(['adb', 'devices'])
    if stderr:
        logging.error(f"Error getting devices: {stderr}")
        return []

    lines = stdout.strip().splitlines()[1:]  # Skip "List of devices attached"
    devices = [line.split()[0] for line in lines
               if 'device' in line and '_adb-tls-connect' not in line]

    logging.debug(f"Connected devices: {devices}")
    return devices


def get_device_model(device):
    """获取指定设备的型号."""
    stdout, stderr = execute_adb_command(['adb', '-s', device, 'shell', 'getprop', 'ro.product.model'])
    if stderr:
        logging.warning(f"Error getting model for {device}: {stderr}")
        return None

    model = stdout.strip()
    logging.info(f"{device} -> Model: {model}")
    return model


def connect(ip, port):
    """连接到指定的 ADB 设备."""
    stdout, stderr = execute_adb_command(['adb', 'connect', f'{ip}:{port}'])
    output = stdout + stderr

    if "connected to" in output.lower() or "already connected" in output.lower():
        logging.info(f"Successfully connected to {ip}:{port}")
        return True
    else:
        logging.warning(f"Failed to connect to {ip}:{port}: {output}")
        return False


def remove_offline_adb_devices():
    """移除 offline 的 ADB 设备 (过滤掉 _adb-tls-connect 设备)."""
    stdout, stderr = execute_adb_command(['adb', 'devices'])
    if stderr:
        logging.error(f"Error getting devices: {stderr}")
        return

    lines = stdout.strip().splitlines()[1:]
    offline_devices = []
    for line in lines:
        if "offline" in line:
            match = re.match(r"(\S+)\s+offline", line)
            if match:
                device = match.group(1)
                # 确保我们要断开的不是 TLS 连接的设备
                if '_adb-tls-connect' not in device:
                    offline_devices.append(device)
                else:
                    logging.info(f"Skipping disconnect for TLS device: {device}")

    for device in offline_devices:
        logging.info(f"Disconnecting offline device: {device}")
        execute_adb_command(['adb', 'disconnect', device])  # 使用线程池执行

    logging.info(f"Removed {len(offline_devices)} offline devices.")


def pair(ip: str, port: int, pairing_code: str, timeout=10):
    """使用配对码进行 ADB 配对."""
    try:
        cmd = ["adb", "pair", f"{ip}:{port}"]

        process = subprocess.Popen(
            cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )

        stdout, _ = process.communicate(input=pairing_code + "\n", timeout=timeout)

        logging.debug(f"ADB pair output: {stdout}")

        if "Successfully paired" in stdout:
            logging.info(f"Successfully paired with {ip}:{port}")
            return True
        else:
            logging.warning(f"Failed to pair with {ip}:{port}: {stdout}")
            return False

    except subprocess.TimeoutExpired:
        logging.error("adb pair 超时")
        return False
    except FileNotFoundError:
        logging.error("未找到 adb 命令，请检查环境变量")
        return False
    except Exception as e:
        logging.exception(f"发生错误: {e}")
        return False


def check(group):
    """检查设备状态并进行相应操作."""
    url = f'https://automatic-android-api.jobeyond.cn/a/device/available?group={group}'
    headers = {
        'x-token': '2W93grF60JDqnFEI',
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Accept': '*/*',
        'Host': 'automatic-android-api.jobeyond.cn',
        'Connection': 'keep-alive'
    }

    try:
        response = requests.post(url, headers=headers)
        response.raise_for_status()  # 检查 HTTP 状态码
        devices = response.json().get('body', [])
    except requests.exceptions.RequestException as e:
        logging.error(f"Request failed: {e}")
        return

    connected_devices = get_connected_devices()

    for device_data in devices:
        adb_ip = device_data.get("adbIp")
        adb_port = device_data.get("adbPort")

        if not adb_ip or not adb_port:
            continue

        adb_addr = f"{adb_ip}:{adb_port}"
        if adb_addr not in connected_devices:
            logging.info(f"Connecting to {adb_addr}...")
            connect(adb_ip, adb_port)
        else:
            logging.info(f"Already connected: {adb_addr}")

    remove_offline_adb_devices()

    connected_devices = get_connected_devices()
    if connected_devices:
        for device in connected_devices:
            executor.submit(get_device_model, device)
    else:
        logging.info("No devices connected.")

def adb_start(group):
    """循环检查设备."""
    # 创建一个 FileHandler，用于将日志写入文件
    file_handler = None
    try:
        while True:
            current_log_file = get_daily_log_file()

            # 移除旧的 FileHandler (如果存在)
            if file_handler:
                logger.removeHandler(file_handler)
                file_handler.close()  # 关闭旧的文件句柄

            # 创建新的 FileHandler，并设置编码
            file_handler = logging.FileHandler(current_log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            try:
                check(group)
            except Exception as e:
                logging.exception(f"Error occurred: {e}")
            finally:
                time.sleep(60)
    except Exception as e:
        logging.exception(f"Unhandled exception in adb_start: {e}")
    finally:
        # 确保在退出前关闭 FileHandler
        if file_handler:
            logger.removeHandler(file_handler)
            file_handler.close()