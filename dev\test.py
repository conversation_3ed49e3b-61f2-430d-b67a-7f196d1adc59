import subprocess
import time
import os
import atexit
import logging
from datetime import datetime
import psutil
import socket
import sys

from appium import webdriver
from appium.options.android import UiAutomator2Options

# 启用 logging（确保你的应用有 logs 目录）
logger = logging.getLogger('appium_launcher')
logger.setLevel(logging.DEBUG)
logger.propagate = False
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s %(name)s %(levelname)s %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


def main():
    # udid = input("请输入设备 UDID: ")  # 获取用户输入的 UDID
    udid = "A7JC9X3105W02806"
    capabilities = {
        "platformName": "Android",
        "appium:automationName": "UiAutomator2",
        "appium:udid": udid,
        # "appium:platformVersion": "10",
        "appium:deviceName": "Phone",
        "appium:noReset": True,
        "appium:uiautomator2ServerLaunchTimeout": 120000,
        "appium:uiautomator2ServerInstallTimeout": 120000,
        'appium:newCommandTimeout': 120
    }

    appium_options = UiAutomator2Options().load_capabilities(capabilities)
    appium_server_url = "http://127.0.0.1:4723"

    # print("--- Capabilities 准备就绪，即将连接 Appium Server ---")
    # print(capabilities)

    # 尝试连接 Appium Server 并创建会话
    try:
        # logger.debug("设备连接中")
        driver = webdriver.Remote(command_executor=appium_server_url, options=appium_options)
        # print("\n--- Appium 会话创建成功！ ---")
        # print("Driver 对象:", driver)
        # # driver.activate_app("com.hpbr.bosszhipin")
        #
        # logger.debug("获取设备页面信息")
        print(driver.page_source)

        # 最后退出
        # driver.quit()
        # print("\n--- Driver 已退出 ---")

    except Exception as e:
        logger.error(f"创建 Driver 时发生错误！{e}")
        print("\n--- 创建 Driver 时发生错误！---")
        print("错误信息:", e)
        print("\n请检查 Appium Server 日志获取更详细的错误原因。")


if __name__ == "__main__":
    main()