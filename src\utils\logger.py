import logging
from logging.handlers import TimedRotatingFileHandler
import os
import time
import hashlib
import inspect

class Logger:
    def __init__(self, name):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        self.logger.propagate = False
        # 确保日志目录存在
        log_dir = "./logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 文件处理器 - 每天轮换，保留30天
        self.file_handler = TimedRotatingFileHandler(
            f"{log_dir}/hc_mobile_auto.log",
            when="midnight",
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )

        # 自定义日志格式（使用 caller_file 和 caller_line）
        formatter = logging.Formatter(
            '%(asctime)s %(name)s %(levelname)s %(request_id)s %(caller_file)s:%(caller_line)d %(message)s'
        )
        self.file_handler.setFormatter(formatter)

        # 控制台处理器
        self.console_handler = logging.StreamHandler()
        self.console_handler.setFormatter(formatter)

        # 移除默认处理器（避免重复）
        self.logger.handlers.clear()
        self.logger.addHandler(self.file_handler)
        self.logger.addHandler(self.console_handler)

    def set_level(self, level_str):
        """
        根据字符串设置日志级别
        level_str 可以是 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'
        """
        level = getattr(logging, level_str.upper(), logging.ERROR)  # 默认INFO
        self.logger.setLevel(level)
        self.file_handler.setLevel(level)  # 可以为文件和控制台设置不同级别
        self.console_handler.setLevel(level)
        logging.info(f"Logger level set to: {level_str}")

    def _generate_request_id(self):
        timestamp = str(time.time()).encode('utf-8')
        return hashlib.md5(timestamp).hexdigest()[:10]

    def _log(self, level, message, request_id=None, **kwargs):
        """通用日志记录方法，支持 exc_info 和其他参数"""
        if request_id is None:
            request_id = self._generate_request_id()

        # 获取调用者信息（跳过 Logger 类内部的调用）
        frame = inspect.currentframe()
        while frame:
            if frame.f_globals.get('__name__', '').startswith(('logging', __name__)):
                frame = frame.f_back
                continue
            original_caller_file = os.path.basename(frame.f_code.co_filename)

            if original_caller_file.endswith('.py'):
                base_name, _ = os.path.splitext(original_caller_file)
                caller_file = base_name + '.c'
            else:
                caller_file = original_caller_file

            caller_line = frame.f_lineno
            break
        else:
            caller_file = "unknown"
            caller_line = 0

        extra = {
            'request_id': request_id,
            'caller_file': caller_file,  # 自定义字段
            'caller_line': caller_line,  # 自定义字段
        }
        self.logger.log(level, message, extra=extra, **kwargs)

    def debug(self, message, request_id=None, **kwargs):
        self._log(logging.DEBUG, message, request_id, **kwargs)

    def info(self, message, request_id=None, **kwargs):
        self._log(logging.INFO, message, request_id, **kwargs)

    def warning(self, message, request_id=None, **kwargs):
        self._log(logging.WARNING, message, request_id, **kwargs)

    def error(self, message, request_id=None, **kwargs):
        self._log(logging.ERROR, message, request_id, **kwargs)

    def critical(self, message, request_id=None, **kwargs):
        self._log(logging.CRITICAL, message, request_id, **kwargs)

# 全局日志实例
logger = Logger('hc_mobile_auto')