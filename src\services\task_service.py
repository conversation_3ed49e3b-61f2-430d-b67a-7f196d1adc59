import threading
from concurrent.futures import Thread<PERSON>oolExecutor
from ..logics.spider_logic import Spider<PERSON>ogic
from ..utils import DeviceSession,logger,glv,CommonUtils,appium_manager
from ..logics import <PERSON><PERSON>og<PERSON>,<PERSON><PERSON>shuLogic
from ..logics.xhs_raise_account import <PERSON><PERSON><PERSON>ogic
import subprocess
import re
import time
import random
from datetime import datetime, timedelta

class TaskService:
    def __init__(self):
        self.domain = None
        self.max_workers = 2  # 默认并发数
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.current_running = 0
        self.lock = threading.Lock()  # 线程锁保证并发安全
        self.run_flags = []
        self.appium_more_thread = False

    def init_app(self, app):
        """初始化服务配置"""
        self.max_workers = app.config.get('MAX_CONCURRENT_TASKS', 5)
        self.executor._max_workers = self.max_workers
        self.domain = app.config.get('DOMAIN', '')
        self.run_flags = app.config.get('RUN_FLAGS', [])
        self.appium_more_thread = app.config.get('APPIUM_MORE_THREAD', False)

    def _task_finished_callback(self, future, udid):
        with self.lock:
            self.current_running -= 1
            if udid in glv["current_running_udids"]:
                del glv["current_running_udids"][udid]
            logger.warning(f"设备udid {udid} 任务已完成（或退出）。当前并发: {self.current_running}/{self.max_workers}")

        if future.exception():
            logger.error(f"设备 {udid} 任务以异常结束: {future.exception()}", exc_info=True)

    def submit_task(self, udid_dict):
        udid, custom_udid = udid_dict["udid"], udid_dict["custom_udid"]

        """
        提交任务到线程池
        """
        with self.lock:
            if self.current_running >= self.max_workers:
                logger.warning(f"设备udid提交失败: 当前并发数已达上限 {self.current_running}/{self.max_workers}")
                return False

            self.current_running += 1
            logger.warning(f"提交设备udid {udid} | 当前并发: {self.current_running}/{self.max_workers}")
            glv["current_running_udids"][udid] = udid_dict

            if self.appium_more_thread:
                server_info = appium_manager.start_server_for_device(udid)
                if not server_info:
                    logger.error(f"Failed to start Appium server for device {udid}")
                    return False
                # 更新appium_server_url
                appium_server_url = f"http://127.0.0.1:{server_info['port']}"
                system_port = server_info['system_port']
            else:
                appium_manager.start_single_server_for_all_device(udid)
                appium_server_url = f"http://127.0.0.1:4723"
                system_port = 8200

            def task_wrapper():
                device_session = DeviceSession(udid, custom_name=custom_udid, custom_udid=custom_udid, appium_server_url=appium_server_url, system_port=system_port)
                if "airecruit" in self.run_flags:
                    boss_logic = BossLogic(custom_udid, self.domain)
                if "xiaohongshu" in self.run_flags:
                    xiaohongshu_logic = XiaohongshuLogic(custom_udid, self.domain)
                    yanghao_logic = YanghaoLogic(custom_udid, self.domain)
                if "spider" in self.run_flags:
                    spider_logic = SpiderLogic(custom_udid)

                error_run_count = 0
                is_need_init = True
                xhs_nickname, xhs_number = None, None
                while True:
                    try:
                        device_session.connect()
                        # ai招聘
                        if "airecruit" in self.run_flags:
                            device_session.activate_app("com.hpbr.bosszhipin")
                            boss_logic.init(device_session)
                            if is_need_init:
                                boss_logic.set_channel_account_info()
                                boss_logic.boss_set_position_project_flag()
                                is_need_init = False
                                device_session.set_custom_name(glv[udid]["channelAccountInfo"])
                            boss_logic.run()

                        if "spider" in self.run_flags:
                            device_session.activate_app("com.xingin.xhs")
                            spider_logic.init(device_session)
                            if is_need_init:
                                spider_logic.set_channel_account_info()
                                is_need_init = False
                                device_session.set_custom_name(glv[udid]["xhs_name"])
                            spider_logic.run()

                        if "xiaohongshu" in self.run_flags:
                            xiaohongshu_logic.init(device_session)
                            yanghao_logic.init(device_session)
                            # 确保应用状态正常
                            yanghao_logic.ensure_app_ready()
                            # 如果账号信息不完整，就获取账号信息并调用API更新
                            if not xhs_nickname or not xhs_number:
                                xhs_nickname, xhs_number = xiaohongshu_logic.get_and_store_xhs_account_info()

                            # 如果仍然获取不到有效账号信息，跳过本次循环
                            if not xhs_nickname or not xhs_number:
                                logger.warning(f"设备 {custom_udid} 无法获取有效账号信息，跳过本次任务")
                                continue

                            # 检查当前时间是否为统计时间（每天晚上11:30-00:30）
                            current_time = datetime.now()
                            current_hour = current_time.hour
                            current_minute = current_time.minute

                            # 统计时间：晚上11:30到次日00:30
                            # is_stats_time = (current_hour == 23 and current_minute >= 30) or \
                            #               (current_hour == 0 and current_minute <= 30)
                            
                            is_stats_time = False
                            if is_stats_time:
                                # 统计时间：执行笔记统计
                                logger.info(f"🕛 当前为统计时间({current_hour:02d}:{current_minute:02d})，执行笔记数据统计...")
                                from ..logics.xhs_notes_stats import XhsNotesStatsLogic
                                stats_logic = XhsNotesStatsLogic(custom_udid, self.domain)
                                stats_logic.init(device_session)

                                # 执行统计
                                success = stats_logic.run_stats_collection(xhs_number, xhs_nickname, max_scrolls=10)
                                if success:
                                    logger.info(f"✅ 笔记统计完成")
                                else:
                                    logger.warning(f"⚠️ 笔记统计失败")

                                # 统计完成后等待较长时间，避免重复执行
                                wait_minutes = random.randint(60, 90)  # 等待1-1.5小时
                                wait_seconds = wait_minutes * 60
                                logger.info(f"统计完成，等待 {wait_minutes} 分钟")
                                time.sleep(wait_seconds)
                            else:
                                # 非统计时间：执行原来的养号和监听逻辑
                                logger.info(f"⏰ 当前为非统计时间({current_hour:02d}:{current_minute:02d})，执行养号和监听逻辑...")
                                logger.info(f"执行养号...")
                                yanghao_result = yanghao_logic.run_yanghao(xhs_number, xhs_nickname)
                                if yanghao_result == "time_arrive":
                                    logger.info(f"养号完成，关闭应用")
                                    device_session.close_app("com.xingin.xhs")
                                    time.sleep(5)
                                logger.info(f"准备执行监听逻辑...")
                                # 停留首页
                                home_tabs = device_session.find_all_by_id("com.xingin.xhs:id/du5")
                                if home_tabs:
                                    home_tab = home_tabs[0]
                                    home_tab.click()
                                # 30%的概率执行监听
                                if random.random() < 0.3:
                                    logger.info(f"随机决定执行监听逻辑 (30%概率)")
                                    xiaohongshu_logic.run(xhs_nickname, xhs_number)
                                else:
                                    logger.info(f"随机决定跳过监听逻辑 (70%概率)")
                                # 随机停留3-5分钟
                                wait_minutes = random.randint(1, 2)
                                wait_seconds = wait_minutes * 60
                                logger.info(f"随机停留 {wait_minutes} 分钟 ({wait_seconds} 秒)")
                                time.sleep(wait_seconds)
                            
                    except Exception as e:
                        logger.error(f"设备 {device_session.custom_name} 业务逻辑执行异常: {str(e)}", exc_info=True)
                        logger.warning(f"设备 {device_session.custom_name} 尝试重新连接 并恢复任务...")

                        try:
                            device_session.connect()
                        except Exception as reconnect_e:
                            error_run_count = error_run_count + 1
                            if error_run_count > 3:
                                logger.error(f"设备 {device_session.custom_name} 重新连接次数超过限制，退出。 {reconnect_e}", exc_info=False)
                                break
                            else:
                                logger.error(f"设备 {device_session.custom_name} 重新连接过程中发生异常: {reconnect_e}", exc_info=False)

                        time.sleep(60)

                device_session.quit()

        future = self.executor.submit(task_wrapper)
        future.add_done_callback(lambda f: self._task_finished_callback(f, udid))
        return True

    def get_running_count(self):
        """获取当前运行任务数"""
        return self.current_running

# 服务单例
task_service = TaskService()

_append_tasks_lock = threading.Lock()

def append_device_tasks(app):
    """
    检查并处理待执行任务
    :param app: Flask应用实例
    """

    if not _append_tasks_lock.acquire(blocking=False): # 非阻塞尝试获取锁
        logger.warning("跳过任务检查：上一个周期仍在进行中。")
        return

    try:
        logger.debug(f"开始检查待处理任务...,当前并发数 {task_service.get_running_count()}/{app.config['MAX_CONCURRENT_TASKS']}")

        # 获取可用任务槽位
        available_slots = app.config['MAX_CONCURRENT_TASKS'] - task_service.get_running_count()
        if available_slots <= 0:
            logger.debug(f"跳过检查: 当前并发数已达上限 {task_service.get_running_count()}/{app.config['MAX_CONCURRENT_TASKS']}")
            return

        # 获取本地化机器有线连接的设备udids
        udids_connected = devices_udids_local()
        if "current_running_udids" not in glv:
            glv["current_running_udids"] = {}

        running_udids_set = glv["current_running_udids"].keys()

        udids_to_add = []
        for udid_dict in udids_connected:
            # 如果是每个设备启动一个server这里做检查
            if app.config.get('APPIUM_MORE_THREAD', False):
                appium_manager.start_server_for_device(udid_dict["udid"])
            if udid_dict["udid"] in running_udids_set:
                continue
            udids_to_add.append(udid_dict)

        if not udids_to_add:
            logger.debug(f"没有待处理的任务,当前并发数 {task_service.get_running_count()}/{app.config['MAX_CONCURRENT_TASKS']}")
            return

        logger.warning(f"获取到 {len(udids_to_add)} 个未加入到运行的设备 | 可用槽位: {available_slots}")

        # 提交任务到线程池
        success_count = 0
        for udid_dict in udids_to_add:
            if task_service.submit_task(udid_dict):
                success_count += 1


        logger.warning(f"成功提交 {success_count}/{len(udids_to_add)} 个任务,当前并发数 {task_service.get_running_count()}/{app.config['MAX_CONCURRENT_TASKS']}")

    except Exception as e:
        logger.error(f"任务检查异常: {str(e)}", exc_info=True)

    finally:
        _append_tasks_lock.release()



def devices_udids_local() -> list[dict[str, str]]:
    udids = []
    try:
        result = subprocess.run(
            ['adb', 'devices'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            check=True
        )
        output = result.stdout

        lines = output.strip().split('\n')
        for line in lines[1:]:
            parts = re.split(r'\s+', line.strip())
            if len(parts) == 2 and parts[1] == 'device' and '_adb-tls-connect' not in line:
                udids.append(parts[0])
            elif len(parts) == 2 and parts[1] == 'unauthorized':
                logger.warning(f"[警告] 设备 {parts[0]} 未授权。请在手机上确认 USB 调试权限。")

    except FileNotFoundError:
        logger.warning("[错误] 找不到 'adb' 命令。请确保 Android SDK Platform-Tools 已安装并添加到系统 PATH 环境变量中。")
    except subprocess.CalledProcessError as e:
        logger.warning(f"[错误] 'adb devices' 命令执行失败: {e}")
        logger.warning(f"错误输出: {e.stderr}")
    except Exception as e:
        logger.warning(f"[未知错误] 获取设备列表时出错: {e}")

    udids_connected = []
    for udid in udids:
        # 获取手机型号
        phone_model = "Unknown"
        try:
            result = subprocess.run(
                ['adb', '-s', udid, 'shell', 'getprop', 'ro.product.model'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=10
            )
            if result.returncode == 0:
                phone_model = result.stdout.strip()
                logger.info(f"设备 {udid} 型号: {phone_model}")
        except Exception as e:
            logger.warning(f"获取设备 {udid} 型号失败: {e}")

        udids_connected.append({
            "udid": udid,
            "custom_udid": udid,
            "phone_model": phone_model
        })

    return udids_connected

def update_run_config(app):
    """
    检查并处理待执行任务
    :param app: Flask应用实例
    """
    if "current_running_udids" not in glv:
        return

    channel_account_list = []
    channel_account_need_update_runconfig = {}

    for udid in glv["current_running_udids"]:
        custom_udid = glv["current_running_udids"][udid]["custom_udid"]
        if custom_udid not in glv:
            continue
        if "channelAccountInfo" in glv[custom_udid]:
            channel_account_list.append({
                "channel_account_info": glv[custom_udid]["channelAccountInfo"]
            })
        if "runConfig" in glv[custom_udid]:
            channel_account_need_update_runconfig[glv[custom_udid]["channelAccountInfo"]] = custom_udid

    if len(channel_account_list)==0:
        return

    url = app.config.get('DOMAIN', '') + "/third-channel/report-channel-accounts-status"

    commonUtils = CommonUtils()
    response = commonUtils.http_post(url, json_data=channel_account_list)
    for channel_account_info in response["results"]:
        if channel_account_info in channel_account_need_update_runconfig:
            glv[channel_account_need_update_runconfig[channel_account_info]]["runConfig"] = response["results"][channel_account_info]
