# 手机型号适配配置

PHONE_MODEL_CONFIG = {
    # 华为手机
    "HUAWEI": {
        "album_package": "com.android.gallery3d",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "com.android.gallery3d:id/gl_root_view",
        "confirm_button": "确定"
    },
    
    # 小米手机
    "MI": {
        "album_package": "com.miui.gallery",
        "album_activity": "com.miui.gallery.activity.HomePageActivity", 
        "image_selector": "com.miui.gallery:id/thumbnail",
        "confirm_button": "确定"
    },
    "Redmi": {
        "album_package": "com.miui.gallery",
        "album_activity": "com.miui.gallery.activity.HomePageActivity",
        "image_selector": "com.miui.gallery:id/thumbnail", 
        "confirm_button": "确定"
    },
    
    # OPPO手机
    "OPPO": {
        "album_package": "com.coloros.gallery3d",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "com.coloros.gallery3d:id/thumbnail",
        "confirm_button": "确定"
    },
    
    # vivo手机
    "vivo": {
        "album_package": "com.android.gallery3d",
        "album_activity": "com.android.gallery3d.app.GalleryActivity", 
        "image_selector": "com.android.gallery3d:id/thumbnail",
        "confirm_button": "确定"
    },
    
    # 三星手机
    "SM-": {  # 三星型号通常以SM-开头
        "album_package": "com.sec.android.gallery3d",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "com.sec.android.gallery3d:id/thumbnail",
        "confirm_button": "确定"
    },
    
    # 一加手机
    "OnePlus": {
        "album_package": "com.oneplus.gallery",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "com.oneplus.gallery:id/thumbnail",
        "confirm_button": "确定"
    },
    
    # 魅族手机
    "meizu": {
        "album_package": "com.meizu.media.gallery",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "com.meizu.media.gallery:id/thumbnail", 
        "confirm_button": "确定"
    },
    
    # 默认配置（通用Android）
    "DEFAULT": {
        "album_package": "com.android.gallery3d",
        "album_activity": "com.android.gallery3d.app.GalleryActivity",
        "image_selector": "android.widget.ImageView",
        "confirm_button": "确定"
    }
}

def get_phone_config(phone_model):
    """根据手机型号获取配置"""
    if not phone_model:
        return PHONE_MODEL_CONFIG["DEFAULT"]
        
    # 遍历配置，找到匹配的型号
    for model_key, config in PHONE_MODEL_CONFIG.items():
        if model_key in phone_model.upper():
            return config
            
    # 如果没有找到匹配的，返回默认配置
    return PHONE_MODEL_CONFIG["DEFAULT"]

# 小红书内置相册的元素ID（推荐使用）
XHS_ALBUM_CONFIG = {
    "image_container": "com.xingin.xhs:id/recycler_view",
    "image_item": "com.xingin.xhs:id/image_view", 
    "confirm_button": "com.xingin.xhs:id/tv_confirm",
    "next_button": "com.xingin.xhs:id/tv_next"
}
