import datetime
import os
import time
import re
import requests
import json
from lxml import etree
import random

from ..utils import logger,glv,CommonMobileUtils
from ..services.tool_service import ToolService
from ..services.common_service import common_service

class BossLogic:

    def __init__(self, custom_udid, domain):
        self.domain = domain
        self.custom_udid = custom_udid
        self.device_session = None
        ToolService.build_common_var(self.custom_udid, "yesterday_is_upload", "")
        ToolService.build_common_var(self.custom_udid, "boss_last_upload_timestamp", int(time.time()))
        # 二次提醒
        ToolService.build_common_var(self.custom_udid, "boss_last_remind_candidate_timestamp", 0)
        # 最后一次上传时间
        ToolService.build_common_var(self.custom_udid, "boss_last_upload_resume_timestamp", 0)
        # 最后一次发布职位时间
        ToolService.build_common_var(self.custom_udid, "boss_last_publish_position_timestamp", 0)
        # 初始化项目对应关系
        ToolService.build_common_var(self.custom_udid, "project_flag_relation", {})
        # 已经上传的简历
        ToolService.build_common_var(self.custom_udid, "already_upload_resume_name_map", {})
        # 打哪个区域的招呼
        ToolService.build_common_var(self.custom_udid, "call_area_list", [])

    def init(self, device_session):
        self.device_session = device_session

    #Boss处理主函数
    def run(self):
        hour = int(datetime.datetime.now().strftime("%H"))
        minute = int(datetime.datetime.now().minute)
        self.boss_call_config()
        actions = []

        for thisAccountActionConfig in glv[self.custom_udid]["boss_call_config"]["thisAccountActionConfig"]:
            if hour == thisAccountActionConfig["startHour"]:
                if minute >= thisAccountActionConfig["startMin"]:
                    actions = thisAccountActionConfig["actions"]
                    break

            if hour == thisAccountActionConfig["endHour"]:
                if minute <= thisAccountActionConfig["endMin"]:
                    actions = thisAccountActionConfig["actions"]
                    break

            if thisAccountActionConfig["startHour"] < hour < thisAccountActionConfig["endHour"]:
                actions = thisAccountActionConfig["actions"]
                break

        if 1 <= hour <= 6:
            time.sleep(60)
            return
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        if 23 >= hour >= 7 and glv[self.custom_udid]["boss_last_upload_timestamp"] + 21600 < int(time.time()):
            # 最后一次上传数据的时间
            ToolService.build_common_var(self.custom_udid,"boss_last_upload_timestamp",int(time.time()))
            # 上传招聘数据
            self.boss_upload_data()
        # boss_new_call(device_session)
        self.boss_red_talk()
        self.boss_send_message()

        logger.warning(f"{self.device_session.custom_name}, 今日是否已经打完招呼标记 {ToolService.get_today_is_complete(self.custom_udid,"boss_call_is_complete")}")
        if not ToolService.get_today_is_complete(self.custom_udid,"boss_call_is_complete"):
            if "打n个招呼说n句话" in actions:
                self.boss_call(0)

            if "收藏" in actions:
                self.boss_call(1)

        # if ToolService.get_today_is_complete(self.custom_udid,"boss_search_call_is_complete")==False:
        #     if "搜索打招呼" in actions:
        #         boss_search_call(device_session)

        if glv[self.custom_udid]["boss_last_upload_resume_timestamp"] + 300 < int(time.time()):
            self.cron_upload_resume()

        if glv[self.custom_udid]["boss_last_publish_position_timestamp"] + 600 < int(time.time()):
            self.boss_position_publish(glv[self.custom_udid]["channelAccountInfo"])

        time.sleep(5)

    def boss_position_publish(self, channelAccountInfo):
        # 最后一次发布职位时间
        ToolService.build_common_var(self.custom_udid,"boss_last_publish_position_timestamp",int(time.time()))
        url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
        req = {
            "appKey": "01cc0711b349a877",
            "sign": "OWQ5YjFmYmZiNDc5ZWU0NTE1MzAzMjM1NzI1OTU0YTkyYmVlYzA3OGMwMWZiMTg0YWU4ZTFkNzJkMGZkZjkwMg==",
            "worksheetId": "position_publish",
            "pageSize": 10,
            "pageIndex": 1,
            "filters": [
                {
                "controlId": "channelAccountInfo",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 2,
                "value": channelAccountInfo
                },
                {
                "controlId": "publishStatus",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 7
                }
            ]
        }
        response = common_service.customer_post(url,req,1)
        if response is None:
            return

        if not response["success"]:
            return

        rows = response["data"]["rows"]
        if len(rows)==0:
            return

        need_close_positions = []
        need_publish_positions = []
        for row in rows:
            if row["action"]=="发布":
                need_publish_positions.append(row)
            elif row["action"]=="关闭":
                need_close_positions.append(row)

        if len(need_close_positions)==0 and len(need_publish_positions)==0:
            return
        
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_4']")
        # 点击管理职位
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_sub_title']")
        # 点击开放中的职位
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.TextView' and @text='开放中']")
        # 处理职位关闭
        for need_close_position in need_close_positions:
            controls = []
            try:
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_job_name' and @text='"+need_close_position["positionName"]+"']")
                # 点击关闭按钮
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/btn_confirm").click()
                # 点击确定按钮
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_positive']")
                controls.append({
                    "controlId": "publishStatus",
                    "value": "成功"
                })
            except Exception as e:
                controls.append({
                    "controlId": "publishStatus",
                    "value": "失败"
                })
                controls.append({
                    "controlId": "reason",
                    "value": str(e)
                })
            # 更新明道状态
            url = "https://api.mingdao.com/v2/open/worksheet/editRow"
            req = {
                "appKey": "01cc0711b349a877",
                "sign": "OWQ5YjFmYmZiNDc5ZWU0NTE1MzAzMjM1NzI1OTU0YTkyYmVlYzA3OGMwMWZiMTg0YWU4ZTFkNzJkMGZkZjkwMg==",
                "worksheetId": "position_publish",
                "rowId": need_close_position["rowid"],
                "controls": controls
            }
            common_service.customer_post(url,req,1)

            # 回退到有发布新职位按钮的页面
            CommonMobileUtils.back_to_appoint_page_use_xpath(self.device_session,[".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_post_job' and @text='发布新职位']"])

        # 处理职位发布
        if len(need_publish_positions)==0:
            return
        agin_get_project_flag = False
        for need_publish_position in need_publish_positions:
            controls = []
            try:
                self.publish_logic(need_publish_position)
                controls.append({
                    "controlId": "publishStatus",
                    "value": "成功"
                })
                agin_get_project_flag = True
            except Exception as e:
                controls.append({
                    "controlId": "publishStatus",
                    "value": "失败"
                })
                controls.append({
                    "controlId": "reason",
                    "value": str(e)
                })
                logger.warning(f"{self.device_session.custom_name},报错{e}")
            # 回退到有发布新职位按钮的页面
            CommonMobileUtils.back_to_appoint_page_use_xpath(self.device_session,[".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_post_job' and @text='发布新职位']"])
            # 更新明道状态
            url = "https://api.mingdao.com/v2/open/worksheet/editRow"
            req = {
                "appKey": "01cc0711b349a877",
                "sign": "OWQ5YjFmYmZiNDc5ZWU0NTE1MzAzMjM1NzI1OTU0YTkyYmVlYzA3OGMwMWZiMTg0YWU4ZTFkNzJkMGZkZjkwMg==",
                "worksheetId": "position_publish",
                "rowId": need_publish_position["rowid"],
                "controls": controls
            }
            common_service.customer_post(url,req,1)


        if agin_get_project_flag:
            self.boss_set_position_project_flag()

        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        return

    def publish_logic(self,position):
        # 点击发布新职位的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_post_job' and @text='发布新职位']")
        time.sleep(2)
        tv_positive_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='重新发布']")
        if len(tv_positive_obj_list)>0:
            tv_positive_obj_list[0].click()
        # 判断是否需要选择发布方式
        page_source = self.device_session.get_page_source()
        if "请选择发布方式" in page_source:
            if position["positionWay"] == "优选职位":
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_opt' and @text='发布优选职位']")
                # 点击客户公司
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='客户公司']")
                # 点击搜索品牌名称
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").click()
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["companyName"])
                time.sleep(2)
                # 判断是否有公司名称
                all_company_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_proxy_com_name")
                if len(all_company_objs) == 0:
                    raise Exception("未搜索到公司")
                all_company_objs[0].click()
                # 导入项目单信息
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_import' and @text='导入项目单信息']")
                # 点击搜索项目单
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/city_search_et").click()
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/city_search_et").input(position["projectMenu"])
                time.sleep(2)
                all_project_menu_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_select")
                if len(all_project_menu_objs) == 0:
                    raise Exception("未搜索到项目单信息")

                all_project_menu_objs[0].click()
                # 点击导入按钮            
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='导入']")
                # 点击下一步
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='下一步']")
                # 点击职位名称
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位名称']")
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["positionName"])
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action' and @text='保存']")
                # 工作地址
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='工作地址']")
                all_address_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_select")
                if len(all_address_objs) == 0:
                    raise Exception("没有工作地址")
                all_address_objs[0].click()
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='保存']")

                # 点击发布
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='发布']")
                return
            else:
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_normal' and @text='发布普通职位']")
        
        company_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='公司']")
        if len(company_obj_list) > 0:
            # 点击公司
            CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='公司']")
            # 点击搜索品牌名称
            self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").click()
            self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["companyName"])
            time.sleep(2)
            # 判断是否有公司名称
            all_company_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_proxy_com_name")
            if len(all_company_objs) == 0:
                raise Exception("未搜索到公司")
            all_company_objs[0].click()

        # 点击职位名称
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位名称']")
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["positionName"])
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action' and @text='保存']")

        # 点击职位描述
        if "您已成功发布过同类职位 是否使用相同内容快速填写 去使用" not in page_source:
            self.device_session.swipe(170,500,170,1100)
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位描述']")
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["positionDesc"])
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action' and @text='保存']")
        negative_submit_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_negative")
        if len(negative_submit_objs)>0:
            negative_submit_objs[0].click()

        # 点击职位类型
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位类型']")
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_job_title' and @text='查看全部职位类型']")
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["positionType"])
        time.sleep(2)
        all_position_type_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_title")
        if len(all_position_type_objs) == 0:
            raise Exception("未搜索到职位类型")
        all_position_type_objs[0].click()

        # 点击下一步
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='下一步']")
        btn_left_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/btn_left")
        if len(btn_left_objs)>0:
            btn_left_objs[0].click()
            
        move = 50
        if int(glv[self.custom_udid]["screen_width"]) == 1080:
            move = 90

        move_b = 80
        if int(glv[self.custom_udid]["screen_width"]) == 1080:
            move_b = 120
        # 点击经验要求
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='经验要求']")
        bound = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wv_wheelview").get_bounding()

        for i in range(3):
            self.device_session.swipe(bound[0]+20,bound[1],bound[0]+20,bound[1]+250)

        experience_list = ["不限","1年以内","1-3年","3-5年","5-10年","10年以上"]
        move_count = 0
        for experience in experience_list:
            if experience in position["experience"]:
                break
            move_count = move_count + 1

        for i in range(move_count):
            self.device_session.swipe(bound[0]+20,bound[1]+20,bound[0]+20,bound[1]-move)

        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_confirm' and @text='确定']")

        # 处理最低学历
        page_source = self.device_session.get_page_source()
        if "请选择最低学历" not in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='最低学历']")


        for i in range(2):
            self.device_session.swipe(bound[0]+20,bound[1],bound[0]+20,bound[1]+250)
            
        # 选择最低学历
        degree_list = ["不限","初中及以下","中专/中技","高中","大专","本科","硕士","博士"]
        move_count = 0
        for degree in degree_list:
            if degree in position["degreeFrom"]:
                break
            move_count = move_count + 1
        for i in range(move_count):
            self.device_session.swipe(bound[0]+20,bound[1]+20,bound[0]+20,bound[1]-move)

        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_confirm' and @text='确定']")

        # 薪资范围
        page_source = self.device_session.get_page_source()
        if "薪资范围" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='薪资范围']")

        page_source = self.device_session.get_page_source()
        if "薪资范围" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='薪资范围']")

        bound = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wv_wheel_first").get_bounding()
        for i in range(4):
            self.device_session.swipe(bound[0]+20,bound[1],bound[0]+20,bound[1]+250)

        for i in range(int(position["salaryFrom"])-1):
            self.device_session.swipe(bound[0]+20,bound[1]+20,bound[0]+20,bound[1]-move)

        bound = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wv_wheel_second").get_bounding()
        for i in range(int(position["salaryTo"])-int(position["salaryFrom"])-1):
            self.device_session.swipe(bound[0]+20,bound[1]+20,bound[0]+20,bound[1]-move)

        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_confirm' and @text='确定']")

        lower_salary_objs = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/title' and @text='底薪']")

        if len(lower_salary_objs)>0:
            lower_salary_objs[0].click()
            bound = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wheelview_two").get_bounding()
            for i in range(int(position["salaryFrom"])*10-int(float(position["lowerSalary"]))*10):
                self.device_session.swipe(bound[0]+20,bound[1]+20,bound[0]+20,bound[1]+move_b)

            self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_confirm").click()

            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action' and @text='保存']")

        # 处理职位关键词
        if len(position["positionKeyWord"]) > 0:
            positionKeyWords = []
            for split_str in position["positionKeyWord"].replace("，",",").split(","):
                if len(split_str.strip())==0:
                    continue
                positionKeyWords.append(split_str)
            if len(positionKeyWords)>0:
                page_source = self.device_session.get_page_source()
                if "职位关键词(选填)" in page_source:
                    CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位关键词(选填)']")
                else:
                    CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='职位关键词']")
                logger.warning(f"{self.device_session.custom_name},positionKeyWords:{positionKeyWords}")

                for i in range(8):
                    for positionKeyWord in positionKeyWords:
                        positionKeyWordObjList = self.device_session.find_all_by_xpath(".//android.view.View[@text='"+positionKeyWord+"']")
                        if len(positionKeyWordObjList)>0:
                            CommonMobileUtils.click_use_xpath(self.device_session,".//android.view.View[@text='"+positionKeyWord+"']")
                            positionKeyWords.remove(positionKeyWord)
                    if len(positionKeyWords) == 0:
                        break
                    self.device_session.swipe(170,1100,170,800)
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@text='确定']")
                button_obj_list = self.device_session.find_all_by_xpath(".//android.widget.Button[@text='确定']")
                if len(button_obj_list) > 0:
                    raise Exception("有职位关键词必填项，但未选择")


        # 工作地址
        try:
            CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/title' and @text='工作地址']")
            search_button_obj_list = self.device_session.find_all_by_xpath(".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_action' and @text='搜索']")
            if len(search_button_obj_list)>0:
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").click()
                self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_input").input(position["positionAddress"])
                # 点击地址搜索
                CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_action' and @text='搜索']")
                time.sleep(2)
                all_address_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_select")
                if len(all_address_objs) == 0:
                    raise Exception("未搜到工作地址")
                all_address_objs[0].click()
            else:
                postion_address_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_content' and contains(@text,'"+position["positionAddress"]+"')]")
                if len(postion_address_obj_list)!=1:
                    raise Exception("工作地址未匹配")
                postion_address_obj_list[0].click()
            
            save_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='保存']")
            if len(save_obj_list)>0:
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='保存']")

        except Exception as e:
            raise Exception(str(e))

        # 点击发布
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_save' and @text='发布']")
        time.sleep(5)
        if "开始招聘" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_hire' and @text='开始招聘']")
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_done' and @text='完成']")
        # 点击完成
        # if "完成" in page_source:
        #     CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_done' and @text='完成']")
        # else:
        #     print("没有发现完成按钮")
        return


    def boss_search_call(self):
        self.boss_call_config()
        if ToolService.get_today_is_complete(self.custom_udid,"boss_search_call_is_complete"):
            return
        if "searchCallPositionMap" not in glv[self.custom_udid]["boss_call_config"]:
            return
        if len(glv[self.custom_udid]["boss_call_config"]["searchCallPositionMap"])==0:
            return
        position_name, search_word = "",""
        for key, value in glv[self.custom_udid]["boss_call_config"]["searchCallPositionMap"].items():
            position_name, search_word = key, value
            break
        if position_name=="" or search_word=="":
            return
        # 点击搜索按钮
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/iv_tab_2").click()
        # 选择打招呼的职位
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_options").click()
        select_positions = self.device_session.find_all_by_xpath(".//android.widget.TextView[contains(@text,'"+position_name+"')]")
        if len(select_positions) == 0:
            return
        select_positions[0].click()
        # 输入关键词
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_input").click()
        self.device_session.find_by_xpath(".//*[@class='android.widget.EditText']").input(search_word)
        # 搜索
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_search_action").click()

        call_num = 0
        already_call_flag = {}
        for i in range(0,30):
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            geek_obj_list = etree_xml.xpath(".//android.view.ViewGroup[@resource-id='com.hpbr.bosszhipin:id/cl_search_card']",namespaces={'x':'urn:h17-org:v3'})
            for geek_obj in geek_obj_list:
                name_list = geek_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_geek_name']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(name_list)==0:
                    continue
                tv_work_edu_desc_list = geek_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_edu_desc']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(tv_work_edu_desc_list)==0:
                    continue
                name = name_list[0]
                tv_work_edu_desc = tv_work_edu_desc_list[0]
                already_flag = name + tv_work_edu_desc

                if already_flag in already_call_flag:
                    continue
                already_call_flag[already_flag] = 1

                xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_edu_desc' and @text='"+tv_work_edu_desc+"']"
                click_tv_work_edu_desc_obj_list = self.device_session.find_all_by_xpath(xpath_str)
                if len(click_tv_work_edu_desc_obj_list)!=1:
                    continue

                geek_source = etree.tostring(geek_obj,encoding="utf-8").decode("utf-8")
                # text_list = re.findall(r'text="(.*?)"', geek_source)
                text_list = re.findall(r'text="([^"]+)"', geek_source)       
                if position_name in glv[self.custom_udid]["project_flag_relation"]:
                    position_portrait = glv[self.custom_udid]["project_flag_relation"][position_name]["positionPortrait"]
                    lb_position_portrait = self.lb_cal_is_comply(position_portrait,"Bearer fastgpt-jMkQXanChWUurvIfaxaEzwrOBOS8tREv7N7rfnvUisdAI9c9mMrpLbw")
                    if len(position_portrait)>0:
                        try:
                            resume_str = "\n".join(text_list)
                            resume_str = "候选人简历：" + resume_str
                            is_comply = self.cal_is_comply(lb_position_portrait,resume_str,"Bearer fastgpt-nEfNihNNk8IzoGH9hzm2XF03sFr41Dfn4TxPDkfPm4eVrnoiypWYU5RqouD")
                            if not is_comply:
                                logger.warning(f"{self.device_session.custom_name},搜索打招呼-列表大模型判断不符合条件，输入position_portrait:{position_portrait},resume_str:{resume_str}")
                                continue
                            logger.warning(f"{self.device_session.custom_name},搜索打招呼-列表大模型判断【符合条件】，输入position_portrait:{position_portrait},resume_str:{resume_str}")
                        except Exception as e:
                            logger.warning(f"{self.device_session.custom_name},搜索打招呼-列表大模型判断报错:{e}")
                            continue
                else:
                    logger.warning(f"{self.device_session.custom_name},搜索打招呼-未获取到职位配置")
                    return
                
                click_tv_work_edu_desc_obj_list[0].click()
                
                if len(self.device_session.find_all_by_xpath(".//android.widget.Button[@text='继续沟通']"))>0:
                    CommonMobileUtils.back(self.device_session,1)
                    continue

                try:
                    resume = self.boss_parse_resume()
                    resume_str =  self.build_resume_str(resume)
                    is_comply = self.cal_is_comply(position_portrait,resume_str,"Bearer fastgpt-h48agQQhdbxQ2Wdm1fe2zW9pvznAuVH2oPsJJarfW58zAYaiA4DoWT")
                    if not is_comply:
                        logger.warning(f"{self.device_session.custom_name},搜索打招呼-详情大模型判断不符合条件，输入:{position_portrait},resume_str:{resume_str}")
                        continue
                    logger.warning(f"{self.device_session.custom_name},搜索打招呼-详情大模型判断【符合条件】，输入:{position_portrait},resume_str:{resume_str}")
                except Exception as e:
                    logger.warning(f"{self.device_session.custom_name},搜索打招呼-详情大模型判断报错:{e}")
                    CommonMobileUtils.back(self.device_session,1)
                    continue

                #点击使用畅聊卡
                self.device_session.find_by_xpath(".//android.widget.Button[contains(@text,'使用畅聊卡')]").click()
                page_source = self.device_session.get_page_source()
                if "商品价格" in page_source:
                    CommonMobileUtils.back(self.device_session,3)
                    ToolService.set_today_is_complete(self.custom_udid,"boss_search_call_is_complete")
                    return
                call_num = call_num + 1
                if call_num >= 3:
                    CommonMobileUtils.back(self.device_session,3)
                    return

                self.send_content_action(glv[self.custom_udid]["boss_call_config"]["callFirstWords"])
                CommonMobileUtils.back(self.device_session,2)

            if ToolService.get_today_is_complete(self.custom_udid,"boss_search_call_is_complete"):
                break

            time.sleep(5)
            self.device_session.swipe(170,1100,170,500)

    def boss_call(self, flag):
        self.boss_move_position_sort()
        self.boss_call_set_filter()
        already_call_name = {}
        position_index = 0
        position_name_objs = self.device_session.find_all_by_xpath(".//*[contains(@resource-id,'com.hpbr.bosszhipin:id/title')]//*[@class='android.widget.TextView']")
        position_name_sub_list = []
        for position_name_obj in position_name_objs:
            p_name = position_name_obj.get_attribute("text")
            if len(p_name) > 6:
                p_name = p_name[0:6]
            position_name_sub_list.append(p_name)

        for tap_str in ["推荐", "最新"]:
            # 点击推荐按钮
            recommed_button_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_filter_text'][@text='" + tap_str + "']")
            if len(recommed_button_list) == 0:
                continue
            recommed_button_list[0].click()

            for c in range(0, 15):
                ToolService.build_common_var(self.custom_udid, "boss_call_name_list", [])
                for i in range(0, 2):
                    page_source = self.device_session.get_page_source()
                    etree_xml = etree.fromstring(page_source.encode('utf-8'))
                    geek_obj_list = etree_xml.xpath(".//android.view.ViewGroup[@resource-id='com.hpbr.bosszhipin:id/cl_geek_card']",namespaces={'x': 'urn:h17-org:v3'})
                    for geek_obj in geek_obj_list:
                        name_list = geek_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_geek_name']/@text",namespaces={'x': 'urn:h17-org:v3'})
                        if len(name_list) == 0:
                            continue
                        name = name_list[0]
                        if name in already_call_name:
                            continue
                        already_call_name[name] = 1

                        geek_source = etree.tostring(geek_obj, encoding="utf-8").decode("utf-8")

                        # 判断是21点前或后
                        hour = int(datetime.datetime.now().strftime("%H"))
                        if hour>=21:
                            if "com.hpbr.bosszhipin:id/fl_tags" not in geek_source and "今日活跃" not in geek_source and "刚刚活跃" not in geek_source:
                                logger.warning(f"{self.device_session.custom_name},{name}本次不打招呼.原因:不在线or不是刚刚活跃or不是今日活跃")
                                continue
                        else:
                            if "com.hpbr.bosszhipin:id/fl_tags" not in geek_source:
                                logger.warning(f"{self.device_session.custom_name},{name}本次不打招呼.原因:不在线")
                                continue

                        base_list = geek_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_edu_other_desc']/@text",namespaces={'x': 'urn:h17-org:v3'})
                        if len(base_list) > 0:
                            # 判断年龄
                            age = 0
                            age_match = re.match(r'(\d+)岁', base_list[0])
                            if age_match:
                                age = ToolService.str_to_int(age_match.group(1))

                            else:
                                work_experience_match = re.match(r'(\d+)年', base_list[0])
                                if work_experience_match:
                                    age = ToolService.str_to_int(work_experience_match.group(1)) + 20
                                    if age >= 30:
                                        age = 45
                            logger.warning(f"{self.device_session.custom_name},{name}的年龄:{age}岁")


                            if age > 0 and (age < glv[self.custom_udid]["boss_call_config"]["startAge"] or age >
                                            glv[self.custom_udid]["boss_call_config"]["endAge"]):
                                logger.warning(f"{self.device_session.custom_name},{name}本次不打招呼.原因:年龄不符合")
                                continue

                            # 判断学历
                            if "不限" not in glv[self.custom_udid]["boss_call_config"]["degrees"] and len(
                                    glv[self.custom_udid]["boss_call_config"]["degrees"]) > 0:
                                is_degree = False
                                for call_degree in glv[self.custom_udid]["boss_call_config"]["degrees"]:
                                    if len(call_degree) > 0 and call_degree in base_list[0]:
                                        is_degree = True
                                        break
                                if not is_degree:
                                    logger.warning(f"{self.device_session.custom_name},{name}本次不打招呼.原因:学历不符合")
                                    continue

                        # tv_content_list = device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_content")
                        # resume_str = "\n".join(geek_obj.xpath(".//@text"))
                        # # 判断是否目标公司
                        # tv_content_no_in = False
                        # if len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"])>0:
                        #     for expectPositionNo in glv[self.custom_udid]["boss_call_config"]["expectPositionNos"]:
                        #         is_continue = True
                        #         if expectPositionNo in resume_str:
                        #                 is_continue = False
                        #                 tv_content_no_in = True
                        #                 print("公司匹配："+name + resume_str)
                        #                 break

                        #         if is_continue == False:
                        #             break
                        #     if tv_content_no_in != True:
                        #         print(name+"本次不打招呼.原因:非目标公司"+resume_str)
                        #         continue

                        # # 判断是否目标职位
                        # tv_content_in = False
                        # if len(glv[self.custom_udid]["boss_call_config"]["expectPositions"])>0:
                        #     for expectPosition in glv[self.custom_udid]["boss_call_config"]["expectPositions"]:
                        #         is_continue = True
                        #         if expectPosition in resume_str:
                        #                 is_continue = False
                        #                 tv_content_in = True
                        #                 print("公司匹配："+name + resume_str)
                        #                 break

                        #         if is_continue == False:
                        #             break
                        #     if tv_content_in != True:
                        #         print(name+"本次不打招呼.原因:非目标职位"+resume_str)
                        #         continue

                        xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_geek_name' and @text='" + name + "']"
                        click_geek_obj_list = self.device_session.find_all_by_xpath(xpath_str)
                        if len(click_geek_obj_list) != 1:
                            continue

                        # 打招呼的动作
                        if flag == 0:
                            position_name = ""
                            position_name_sub = position_name_sub_list[position_index]
                            for key, value in glv[self.custom_udid]["project_flag_relation"].items():
                                cal_position_name = value["positionName"]
                                if len(cal_position_name) > 6:
                                    cal_position_name = cal_position_name[0:6]
                                if position_name_sub == cal_position_name:
                                    position_name = value["positionName"]
                                    break

                            if position_name in glv[self.custom_udid]["project_flag_relation"]:
                                position_portrait = glv[self.custom_udid]["project_flag_relation"][position_name]["positionPortrait"]
                                # lb_position_portrait = glv[self.custom_udid]["project_flag_relation"][position_name]["list_positionPortrait"]
                                lb_position_portrait = self.lb_cal_is_comply(position_portrait,"Bearer fastgpt-jMkQXanChWUurvIfaxaEzwrOBOS8tREv7N7rfnvUisdAI9c9mMrpLbw")
                                if len(position_portrait) > 0:
                                    try:
                                        resume_str = "\n".join(geek_obj.xpath(".//@text"))
                                        resume_str = "候选人简历：" + resume_str
                                        is_comply = self.cal_is_comply(lb_position_portrait, resume_str,"Bearer fastgpt-nEfNihNNk8IzoGH9hzm2XF03sFr41Dfn4TxPDkfPm4eVrnoiypWYU5RqouD")
                                        if not is_comply:
                                            print("列表大模型判断不符合条件，输入:" + lb_position_portrait + resume_str)
                                            continue
                                        print("列表大模型判断【符合条件】，输入:" + lb_position_portrait + resume_str)
                                    except Exception as e:
                                        print("列表大模型判断报错:" + str(e))
                                        continue

                            click_geek_obj_list[0].click()

                            xpath_str = ".//android.widget.Button[@resource-id='com.hpbr.bosszhipin:id/btn_cancel' and @text='放弃沟通']"
                            btn_cancel_list = self.device_session.find_all_by_xpath(xpath_str)
                            if len(btn_cancel_list) > 0:
                                btn_cancel_list[0].click()
                                CommonMobileUtils.back(self.device_session, 1)
                                break

                            page_source = self.device_session.get_page_source()
                            if "聊天加油包" in page_source or "今日沟通数已达上限" in page_source or "查看不足" in page_source:
                                logger.warning(f"{self.device_session.custom_name},聊天加油包或今日沟通数已达上限或查看不足")
                                ToolService.set_today_is_complete(self.custom_udid, "boss_call_is_complete")
                                CommonMobileUtils.back(self.device_session, 1)
                                break

                            self.boss_call_in_detail(position_name)
                        else:
                            click_geek_obj_list[0].click()

                            page_source = self.device_session.get_page_source()
                            if "聊天加油包" in page_source or "今日沟通数已达上限" in page_source or "查看不足" in page_source:
                                logger.warning(f"{self.device_session.custom_name},聊天加油包或今日沟通数已达上限或查看不足")
                                ToolService.set_today_is_complete(self.custom_udid, "boss_call_is_complete")
                                CommonMobileUtils.back(self.device_session, 1)
                                break

                            self.boss_collect_in_detail()
                        time.sleep(5)
                        CommonMobileUtils.back(self.device_session, 1)

                    if ToolService.get_today_is_complete(self.custom_udid, "boss_call_is_complete"):
                        break

                    self.device_session.swipe(170, 1100, 170, 650)

                # 如果打了招呼，就给这些候选人轮询的发送三条消息
                if len(glv[self.custom_udid]["boss_call_name_list"]) > 0:
                    # 点击消息按钮
                    CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
                    # 点击仅沟通按钮
                    CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='仅沟通']")
                    
                    for i in range(0,4):
                        self.device_session.swipe(170,500,170,1100,duration=50)

                    for callAfterRetalkContent in glv[self.custom_udid]["boss_call_config"]["callAfterRetalkContents"]:
                        time.sleep(5)
                        name_set = set(glv[self.custom_udid]["boss_call_name_list"])
                        for name in name_set:
                            # 判断这个候选人是否是红点，如果是红点就不处理
                            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='" + name + "']/../*[@resource-id='com.hpbr.bosszhipin:id/badge_view']/android.widget.TextView"
                            red_view_list = self.device_session.find_all_by_xpath(xpath_str)
                            if len(red_view_list) > 0:
                                glv[self.custom_udid]["boss_call_name_list"].remove(name)
                                continue
                            # 判断当前列表有几个相同名字的候选人
                            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='" + name + "']"
                            need_deal_candidate_obj_list = self.device_session.find_all_by_xpath(xpath_str)
                            if len(need_deal_candidate_obj_list) != 1:
                                glv[self.custom_udid]["boss_call_name_list"].remove(name)
                                continue
                            need_deal_candidate_obj_list[0].click()
                            # 发送内容
                            self.send_content_action([callAfterRetalkContent])
                            # 返回到列表
                            CommonMobileUtils.back(self.device_session, 1)

                    # 点击牛人按钮
                    self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_tab_1").click()

                if ToolService.get_today_is_complete(self.custom_udid, "boss_call_is_complete"):
                    break

                red_count = self.get_red_count()
                if red_count > 0:
                    break
                time.sleep(5)

    # 点击收藏
    def boss_collect_in_detail(self):
        is_in = self.is_call_in_detail()
        if not is_in:
            return None
        iv_icon_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/title_actions']//*[@resource-id='com.hpbr.bosszhipin:id/iv_icon']")
        if len(iv_icon_obj_list) == 0:
            return False

        iv_icon_obj_list[0].click()
        return None

        # iv_icon_obj_list[0].click()
        # iv_icon_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_icon")
        # if len(iv_icon_obj_list)<3:
        #     iv_icon_obj_list[0].click()
        # elif len(iv_icon_obj_list)==3: #来自店长直聘没有收藏按钮
        #     pass
        # else:
        #     iv_icon_obj_list[1].click() #同事沟通过的第二按钮是收藏

    def boss_call_in_detail(self,position_name):
        if ToolService.get_today_is_complete(self.custom_udid,"boss_call_is_complete"):
            return

        if len(self.device_session.find_all_by_xpath(".//android.widget.Button[@text='继续沟通']"))>0:
            return
        
        is_in = self.is_call_in_detail()
        if not is_in:
            return

        if position_name in glv[self.custom_udid]["project_flag_relation"]:
            position_portrait = glv[self.custom_udid]["project_flag_relation"][position_name]["positionPortrait"]
            if len(position_portrait)>0:
                try:
                    resume = self.boss_parse_resume()
                    resume_str = self.build_resume_str(resume)
                    is_comply = self.cal_is_comply(position_portrait,resume_str,"Bearer fastgpt-h48agQQhdbxQ2Wdm1fe2zW9pvznAuVH2oPsJJarfW58zAYaiA4DoWT")
                    if not is_comply:
                        return

                except Exception as e:
                    logger.warning(f"{self.device_session.custom_name},详情大模型判断报错:{e}")

        # 点击立即沟通
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.Button[@text='立即沟通']")
        # 判断是否已经打完招呼
        block_short_desc_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_block_short_desc' and @text='开聊不足']")
        call_block_short_desc_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_block_short_desc' and @text='沟通权益不足']")
        sel_buy_desc_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_block_short_desc' and @text='选择购买']")

        if len(block_short_desc_obj_list)>0 or len(call_block_short_desc_obj_list)>0 or len(sel_buy_desc_obj_list)>0:
            ToolService.set_today_is_complete(self.custom_udid,"boss_call_is_complete")
            CommonMobileUtils.back(self.device_session,1)
            return


        self.send_content_action(glv[self.custom_udid]["boss_call_config"]["callFirstWords"])
        name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_title").get_attribute("text")
        glv[self.custom_udid]["boss_call_name_list"].append(name)
        CommonMobileUtils.back(self.device_session,1)

    def boss_call_is_in(self):
        # 判断是否在线
        page_source = self.device_session.get_page_source()
        if "com.hpbr.bosszhipin:id/iv_online_state" not in page_source:
            return False
        # 判断年龄
        age_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_geek_age")
        if len(age_list)>0:
            age_match = re.match(r'(\d+)岁', age_list[0].get_attribute("text"))
            if age_match:
                age = ToolService.str_to_int(age_match.group(1))
                if age > 0 and (age < glv[self.custom_udid]["boss_call_config"]["startAge"] or age > glv[self.custom_udid]["boss_call_config"]["endAge"]):
                    return False
        
        tv_job_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_and_city")

        # 判断期望职位包含
        expect_in = False
        if len(glv[self.custom_udid]["boss_call_config"]["expectPositions"])>0:
            for tv_job_obj in tv_job_obj_list:
                is_continue = True
                for expectPosition in glv[self.custom_udid]["boss_call_config"]["expectPositions"]:
                    if expectPosition in tv_job_obj.get_attribute("text"):
                        is_continue = False
                        expect_in = True
                        break
                if not is_continue:
                    break

        # 判断期望职位不包含
        expect_no_in = False
        if len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"])>0:
            if not expect_in:
                for tv_job_obj in tv_job_obj_list:
                    is_continue = True
                    for expectPositionNo in glv[self.custom_udid]["boss_call_config"]["expectPositionNos"]:
                        if expectPositionNo in tv_job_obj.get_attribute("text"):
                            is_continue = False
                            expect_no_in = True
                            break
                    if not is_continue:
                        break

        if len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"])>0 and len(glv[self.custom_udid]["boss_call_config"]["expectPositions"])>0:    
            if expect_in==False and expect_no_in==True:
                return False
        elif len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"])==0 and len(glv[self.custom_udid]["boss_call_config"]["expectPositions"])>0:
            if not expect_in:
                return False
        elif len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"])>0 and len(glv[self.custom_udid]["boss_call_config"]["expectPositions"])==0:
            if expect_no_in:
                return False
        return True

    def boss_move_position_sort(self):
        # 点击牛人按钮
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_tab_1").click()

        iv_menu_icon_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_menu_icon")
        if len(iv_menu_icon_list) == 0:
            return
        iv_menu_icon_list[0].click()

        iv_sort_icon_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/iv_sort_icon")
        if len(iv_sort_icon_list) == 0:
            CommonMobileUtils.back(self.device_session,1)
            return
        bound = iv_sort_icon_list[-1].get_bounding()
        self.device_session.swipe(bound[0] + 5, bound[1], bound[0] + 5, 200)

        # 获取第一个职位的名字
        tv_job_name_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_name")
        if len(tv_job_name_obj_list) > 0:
            first_position_name = tv_job_name_obj_list[0].get_attribute("text")
            if first_position_name in glv[self.custom_udid]["project_flag_relation"]:
                callArea = glv[self.custom_udid]["project_flag_relation"][first_position_name]["callArea"]
                call_area_list = callArea.replace("，", ",").split(",")
                ToolService.build_common_var(self.custom_udid, "call_area_list", call_area_list)
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_btn_action").click()

        # 判断是否在首页的推荐职位上
        recommend_object_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@class='android.widget.TextView' and @text='推荐牛人']")
        if len(recommend_object_list) == 1:
            self.device_session.find_by_xpath(".//android.widget.TextView[@class='android.widget.TextView' and @text='推荐牛人']/../following-sibling::*[1]/*[1]").click()

    # 设置boss的筛选
    def boss_call_set_filter(self):
        # 点击牛人按钮
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_tab_1").click()
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_filter_text' and contains(@text,'筛选')]")

        xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/btn_reset' and @text='清除']"
        CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)

        if glv[self.custom_udid]["boss_call_config"]["gender"] in ["男","女"]:
            filter_text = glv[self.custom_udid]["boss_call_config"]["gender"]
            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
            if len(self.device_session.find_all_by_xpath(xpath_str))>0:
                CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
        if glv[self.custom_udid]["boss_call_config"]["active"]!="不限":
            filter_text = glv[self.custom_udid]["boss_call_config"]["active"]
            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
            if len(self.device_session.find_all_by_xpath(xpath_str))>0:
                CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
        if glv[self.custom_udid]["boss_call_config"]["recentNoLook"]!="不限":
            filter_text = glv[self.custom_udid]["boss_call_config"]["recentNoLook"]
            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
            if len(self.device_session.find_all_by_xpath(xpath_str))>0:
                CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
        if glv[self.custom_udid]["boss_call_config"]["isChangeResume"]!="不限":
            filter_text = glv[self.custom_udid]["boss_call_config"]["isChangeResume"]
            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
            if len(self.device_session.find_all_by_xpath(xpath_str))>0:
                CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
        
        isClickJobIntention = True
        isClickDegree = True
        isClickExperience = True
        i = 0
        while True:
            i = i+1
            if i > 10:
                break
            self.device_session.swipe(170,1100,170,500)
            if isClickJobIntention:
                for jobIntention in glv[self.custom_udid]["boss_call_config"]["jobIntentions"]:
                    filter_text = jobIntention
                    if filter_text!="不限":
                        xpath_str = ".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_label' and @text='求职意向']/../../..//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
                        count = len(self.device_session.find_all_by_xpath(xpath_str))
                        if count==1:
                            CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
                            isClickJobIntention = False
                        else:
                            logger.warning(f"{self.device_session.custom_name},{filter_text}:匹配到{count}个")
            if isClickDegree:
                for degree in glv[self.custom_udid]["boss_call_config"]["degrees"]:
                    filter_text = degree
                    if filter_text!="不限":
                        xpath_str = ".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_label' and @text='学历要求']/../../..//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
                        count = len(self.device_session.find_all_by_xpath(xpath_str))
                        if count==1:
                            CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
                            isClickDegree = False
                        else:
                            logger.warning(f"{self.device_session.custom_name},{filter_text}:匹配到{count}个")
            if isClickExperience:
                for experience in glv[self.custom_udid]["boss_call_config"]["experiences"]:
                    filter_text = experience
                    if filter_text!="不限":
                        xpath_str = ".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_label' and @text='经验要求']/../../..//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='"+filter_text+"']"
                        count = len(self.device_session.find_all_by_xpath(xpath_str))
                        if count==1:
                            CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)
                            isClickExperience = False
                        else:
                            logger.warning(f"{self.device_session.custom_name},{filter_text}:匹配到{count}个")
            page_source = self.device_session.get_page_source()
            if "筛选功能仅供您分析招聘数据" in page_source:
                break
        
        xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/btn_confirm' and @text='确定']"
        CommonMobileUtils.click_use_xpath(self.device_session,xpath_str)

        # 点击选择区
        xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/rv_filter_right']//*[@resource-id='com.hpbr.bosszhipin:id/tv_filter_text']"
        tv_filter_text_list = self.device_session.find_all_by_xpath(xpath_str)
        if len(tv_filter_text_list) == 0:
            return
        tv_filter_text_list[0].click()

        # 点击区
        area_list = glv[self.custom_udid]["call_area_list"]
        random.shuffle(area_list)
        for area in area_list:
            xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/keywords_view_text' and @text='" + area + "']"
            area_in_boss_list = self.device_session.find_all_by_xpath(xpath_str)
            if len(area_in_boss_list) > 0:
                area_in_boss_list[0].click()
                break

        xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/btn_confirm' and @text='确定']"
        CommonMobileUtils.click_use_xpath(self.device_session, xpath_str)

    def boss_set_position_project_flag(self):
        positions_open = self.boss_get_positions()
        req = {
            "source":1,
            "channelAccountInfo" : glv[self.custom_udid]["channelAccountInfo"],
            "positionInfo" : positions_open
        }
        url = self.domain + "/third-channel/indentify-project-flag"
        retry_num = 1
        results = common_service.comm_post(url, req, retry_num)
        ToolService.build_common_var(self.custom_udid,"project_flag_relation",results)


    def boss_red_talk(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        page_source = self.device_session.get_page_source()
        if "仅沟通" not in page_source:
            # 点击消息按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        
        # 点击通知红点
        tv_notice_count_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_notice_count")
        if len(tv_notice_count_obj_list)>0:
            tv_notice_count_obj_list[0].click()
            CommonMobileUtils.back(self.device_session,1)

        tap_str_list = ["新招呼","仅沟通","有交换","有面试"]
        for tap_str in tap_str_list:
            self.boss_talk(tap_str)

    def get_red_count(self):
        page_source = self.device_session.get_page_source()
        if "仅沟通" not in page_source:
            # 点击消息按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        red_count = 0
        tv_count_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_count")
        for tv_count_obj in tv_count_obj_list:
            count = ToolService.str_to_int(tv_count_obj.get_attribute("text"))
            if count>0:
                red_count = 100
                break
        # 点击牛人按钮
        try:
            self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_tab_1").click()
        except:
            logger.warning(f"{self.device_session.custom_name},未找到牛人按钮，正在重试...")
            time.sleep(2)  # 等待2秒
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/cl_tab_1").click()

        return red_count


    def boss_talk(self,tap_str):
        is_complete = False
        index = 0
        while(True):
            index = index + 1
            if index > 5:
                break
            self.boss_close()
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            tap_group_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='"+tap_str+"']/../..",namespaces={'x':'urn:h17-org:v3'})
            if len(tap_group_obj_list) == 0:
                break
            if "新招呼" not in tap_str:
                tap_group_obj = tap_group_obj_list[0]
                tv_count_list = tap_group_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_count']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(tv_count_list) == 0:
                    break
            else:
                page_source = self.device_session.get_page_source()
                if "暂时还没有消息呢" in page_source:
                    is_complete = True
                    break
            #点击tap按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='"+tap_str+"']")
            if "新招呼" not in tap_str:
                #双击仅沟通定位到红点
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']","dbclick")
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            candidate_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/..",namespaces={'x':'urn:h17-org:v3'})
            if len(candidate_obj_list) == 0:
                is_complete = True
                break
            need_deal_candidate_number_map = {}
            for candidate_obj in candidate_obj_list:
                self.boss_close()
                if "新招呼" not in tap_str:
                    #判断此候选人是否是红点
                    red_count_list = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/badge_view']/android.widget.TextView",namespaces={'x':'urn:h17-org:v3'})
                    if len(red_count_list)==0:
                        continue
                else:
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='新招呼']")
                    new_page_source = self.device_session.get_page_source()
                    new_etree_xml = etree.fromstring(new_page_source.encode('utf-8'))
                    new_candidate_obj_list = new_etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/..",namespaces={'x':'urn:h17-org:v3'})
                    if len(new_candidate_obj_list)==0:
                        is_complete = True
                        break

                candidate_name = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/@text",namespaces={'x':'urn:h17-org:v3'})[0]
                logger.warning(f"{self.device_session.custom_name},{candidate_name}需要处理")

                #点击候选人进行沟通
                # CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='"+candidate_name+"']")
                xpath_str = ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='"+candidate_name+"']"
                need_deal_candidate_obj_list = self.device_session.find_all_by_xpath(xpath_str)
                need_deal_candidate_obj_list[0].click()
                logger.warning(f"{self.device_session.custom_name},{candidate_name}开始解析姓名和职位名称")

                page_source = self.device_session.get_page_source()
                if "com.hpbr.bosszhipin:id/tv_sub_title" not in page_source:
                    continue
                candidate_name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_title").get_attribute("text")
                position_name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_sub_title").get_attribute("text")
                logger.warning(f"{self.device_session.custom_name},{candidate_name}解析姓名和职位名称结束")

                #红点处理
                logger.warning(f"{self.device_session.custom_name},{candidate_name}开始解析对话记录")

                records = self.boss_parse_dialogue_record()

                if "新招呼" in tap_str:
                    record_merge = ""
                    for record in records:
                        record_merge = record_merge + record["con"]

                    records = [{"ower":candidate_name,"con":record_merge,"is_friend":1}]
                    talk_objs = []
                    # 获取年龄和学历
                    age = 0
                    degree = ""
                    group_remark = ""
                    if "女士" in candidate_name or "先生" in candidate_name:
                        age,degree,group_remark = self.get_m_geek_info(True)
                    else:
                        age,degree,_ = self.get_m_geek_info(True)
                else:
                    #判断是否需要把系统请求的交互添加到对话记录内
                    talk_objs = []
                    for record in records:
                        if record["is_friend"]==0:
                            talk_objs = []
                        elif record["is_friend"]==2:
                            talk_objs.append({"spoken":record["con"],"receivedName":record["ower"]})
                    logger.warning(f"{self.device_session.custom_name},{candidate_name}解析对话记录结束")

                    if len(talk_objs) > 0:
                        logger.warning(f"{self.device_session.custom_name},{candidate_name}开始添加沟通记录")

                        # 添加沟通记录
                        url = self.domain + "/interview-robot/write-interview-record"
                        req = {
                                "talkList": talk_objs,
                                "groupName": candidate_name,
                                "groupRemark": group_remark,
                                "token": "maotai",
                                "channelAccountInfo":glv[self.custom_udid]["channelAccountInfo"]
                        }
                        common_service.comm_post(url, req, 2)
                        logger.warning(f"{self.device_session.custom_name},{candidate_name}添加沟通记录结束")


                    logger.warning(f"{self.device_session.custom_name},{candidate_name}开始获取所有的同意按钮并点击")

                    # 先查找一次同意按钮的对象列表
                    agree_button_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_dialog_btn_right' and @text='同意']")
                    max_attempts = len(agree_button_obj_list)
                    for attempt in range(max_attempts):
                        # 重新查找所有同意按钮的对象列表
                        agree_button_obj_list = self.device_session.find_all_by_xpath(".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_dialog_btn_right' and @text='同意']")
                        if not agree_button_obj_list:
                            break
                        for agree_button_obj in agree_button_obj_list:
                            try:
                                # 点击同意
                                agree_button_obj.click()
                                page_source = self.device_session.get_page_source()
                                if "取消" in page_source:
                                    # 点击取消
                                    CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='取消']")
                            except Exception as e:
                                logger.warning(f"{self.device_session.custom_name},点击同意按钮时出错{e}")

                    logger.warning(f"{self.device_session.custom_name},{candidate_name}完成获取所有的同意按钮并点击")

                    logger.warning(f"{self.device_session.custom_name},{candidate_name}开始获取年龄和学历以及remark")

                    # 获取年龄和学历
                    age = 0
                    degree = ""
                    group_remark = ""
                    if "女士" in candidate_name or "先生" in candidate_name:
                        age,degree,group_remark = self.get_m_geek_info(True)
                        if age==0 and degree=="" and group_remark=="":
                            CommonMobileUtils.back(self.device_session,1)
                            continue
                    else:
                        # age,degree,group_remark = get_m_geek_info(self.device_session,False)
                        age,degree,_ = self.get_m_geek_info(True)

                    logger.warning(f"{self.device_session.custom_name},{candidate_name}获取年龄和学历以及remark结束")

                

                resume = {}
                gpt_is_comply = 0
                # if "新招呼" in tap_str:
                #     try:
                #         position_portrait = ""
                #         for key,value in glv[self.custom_udid]["project_flag_relation"].items():
                #             if position_name == value["positionName"]:
                #                 position_portrait = value["positionPortrait"]
                #                 break
                #         if len(position_portrait)>0:
                #             is_comply,resume = resume_is_talk_use_gpt(self.device_session,position_portrait)
                #             if is_comply:
                #                 gpt_is_comply = 1
                #             else:
                #                 gpt_is_comply = 2
                #     except Exception as e:
                #         print(e)


                #候选人说的话
                spokens = []
                for record in records:
                    if record["is_friend"]==0:
                        spokens = []
                    else:
                        spokens.append(record["con"])

                # agree_button_obj_list = device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_dialog_btn_right' and @text='同意']")
                # for agree_button_obj in agree_button_obj_list:
                #     # 点击同意
                #     try:
                #         # agree_button_obj.click()
                #         device_session.find_by_id("com.hpbr.bosszhipin:id/tv_dialog_btn_right").click()
                #         page_source = self.device_session.get_page_source()
                #         if "取消" in page_source:
                #             #点击取消
                #             CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='取消']")
                #     except Exception as e:
                #         print(e)


                # 解析手机号和微信号
                logger.warning(f"{self.device_session.custom_name},{candidate_name}开始解析手机号和微信号")

                phone,wechat = self.boss_parse_phone_wechat()
                is_upload_resume = False

                if phone == "" and wechat == "":
                    for record in records:
                        text = record
                        if phone == "" and "的手机号：" in text:
                            match = re.search(r'的手机号：(.*)', text)
                            if match:
                                phone = match.group(1)
                        if wechat == "" and "的微信号：" in text:
                            match = re.search(r'的微信号：(.*)', text)
                            if match:
                                wechat = match.group(1)

                if phone!="" or wechat!="":
                    is_upload_resume = True
                logger.warning(f"{self.device_session.custom_name},{candidate_name}解析手机号和微信号结束")


                if len(spokens) == 0:
                    logger.warning(f"{self.device_session.custom_name},{candidate_name}不是候选人的内容结尾，不需要处理")
                    if is_upload_resume or tap_str in ["有面试"]:
                        logger.warning(f"{self.device_session.custom_name},{candidate_name}开始上传简历")
                        self.boss_upload_resume(resume)
                        logger.warning(f"{self.device_session.custom_name},{candidate_name}上传简历结束")
                    CommonMobileUtils.back(self.device_session,1)
                    continue
                
                project_flag = ""
                if position_name in glv[self.custom_udid]["project_flag_relation"]:
                    project_flag = glv[self.custom_udid]["project_flag_relation"][position_name]["projectFlag"]

                logger.warning(f"{self.device_session.custom_name},{candidate_name}开始请求对话接口")

                is_start = False
                if "新招呼" in tap_str:
                    is_start = True
                url = self.domain + "/interview-robot/boss-call"
                req = {
                    "isStart":is_start,
                    "spokens": spokens,
                    "receivedName": candidate_name,
                    "groupName": candidate_name,
                    "candidateWechatId":"",
                    "candidateWechatName":"",
                    "groupRemark": group_remark,
                    "roomType": 1,
                    "atMe": False,
                    "textType": 1,
                    "robotFlag":"",
                    "token":"maotai",
                    "channelAccountInfo":glv[self.custom_udid]["channelAccountInfo"],
                    "positionName":position_name,
                    "projectFlag":project_flag,
                    "age":age,
                    "degree":degree,
                    "gptIsComply":gpt_is_comply
                }
                logger.warning(f"{self.device_session.custom_name},{candidate_name},{json.dumps(req)}")

                actions = []
                #boss自动化对话接口
                ret = common_service.comm_post(url, req, 1)
                logger.warning(f"{self.device_session.custom_name},{candidate_name},请求对话接口结束")

                if "新招呼" in tap_str:
                    if len(ret["data"]) == 0:
                        ret["data"] = [{
                            "text":"[抱拳]"
                        }]
                if len(ret["data"]) > 0:
                    # 发送的内容
                    contents = []
                    # 处理返回的内容
                    for row in ret["data"]:
                        if "segmentationflag" in row["text"]:#匹配有没有action动作
                            content_list = row["text"].split("segmentationflag")
                            for content in content_list:
                                action_flag = re.match(r'\[action=(.*?)\]', content)
                                if action_flag:
                                    actions.append(action_flag.group(1))
                                elif len(content) > 0:
                                    contents.append(content)
                        elif len(row["text"]) > 0:
                            contents.append(row["text"])

                    # 发送文本内容
                    if len(contents)>0:
                        self.send_content_action(contents)

                interview_button_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='约面试']")
                if len(interview_button_obj_list) == 0:
                    interview_button_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and @text='面试TA']")

                is_send_int = os.environ.get('IS_SEND_INT', '')
                if is_send_int == "" and len(interview_button_obj_list) > 0 and ret["isSetInterviewTime"]:
                    # 交换电话
                    self.change_phone(resume)
                    # 点击面试ta
                    interview_button_obj_list[0].click()
                    time.sleep(2)

                    # 选面试类型
                    # CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_offline_interview']")

                    # 选面试地址
                    # interview_location_text = device_session.find_by_id("com.hpbr.bosszhipin:id/tv_interview_location").get_attribute("text")
                    # if interview_location_text == "":
                    #     # 选择面试地址
                    #     CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_interview_location']")
                    #     address_obj_list = device_session.find_all_by_id("com.hpbr.bosszhipin:id/mTitleView")
                    #     if len(address_obj_list)<1:
                    #         address_obj_list[0].click()

                    #解析面试时间
                    time_match = re.search(r'\d+-\d+-(\d+) (\d+):(\d+):\d+', ret["setInterviewTime"])

                    calendar_objs = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_calendar_date' and @text='"+str(int(time_match.group(1)))+"']")
                    calendar_objs[0].click()
                    time.sleep(1)

                    move = 50
                    if int(glv[self.custom_udid]["screen_width"]) == 1080:
                        move = 90

                    bound_hour = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wheel_middle").get_bounding()

                    hour_move_count = int(time_match.group(2)) - 8
                    today = datetime.datetime.now()
                    if today.strftime("%Y-%m-%d") in ret["setInterviewTime"]:
                        hour = int(datetime.datetime.now().strftime("%H"))
                        hour_move_count = int(time_match.group(2)) - hour

                    for i in range(hour_move_count):
                        self.device_session.swipe(bound_hour[0]+20,bound_hour[1]+20,bound_hour[0]+20,bound_hour[1]-move)

                    bound_min = self.device_session.find_by_id("com.hpbr.bosszhipin:id/wheelview_right").get_bounding()
                    min_move_count = int(time_match.group(3))//5
                    for i in range(min_move_count):
                        self.device_session.swipe(bound_min[0]+20,bound_min[1]+20,bound_min[0]+20,bound_min[1]-move)
                    #点击确定按钮
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_confirm' and @text='确定']","click")

                    # 设置备注
                    self.set_interview_desc(ret["weworkData"])

                    #点击发送面试邀请
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_create' and @text='发送面试邀请']","click")

                    page_source = self.device_session.get_page_source()
                    if "com.hpbr.bosszhipin:id/tv_positive" in page_source and "接受" in page_source:
                        # 不良记录是否接受
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_positive']")
                    page_source = self.device_session.get_page_source()
                    if "暂不开启" in page_source:
                        #点击暂不开启
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='暂不开启']","click")
                    page_source = self.device_session.get_page_source()
                    if "com.hpbr.bosszhipin:id/btn_confirm" in page_source and "继续" in page_source:
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/btn_confirm']")
                    page_source = self.device_session.get_page_source()
                    if "com.hpbr.bosszhipin:id/tv_positive" in page_source and "保证不爽约" in page_source:
                        # 保证不爽约
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_positive']")
                    page_source = self.device_session.get_page_source()
                    if "返回" in page_source:
                        #点击返回
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='返回']","click")
                    # 添加沟通记录
                    url = self.domain + "/interview-robot/write-interview-record"
                    req = {
                            "talkList": [
                                {"spoken":"发起了面试邀请","receivedName":glv[self.custom_udid]["channelAccountInfo"]}
                            ],
                            "groupName": candidate_name,
                            "token": "maotai",
                            "channelAccountInfo":glv[self.custom_udid]["channelAccountInfo"]
                    }
                    common_service.comm_post(url, req, 2)
                        
                if tap_str in ["有面试"]:
                    is_upload_resume = True
                # elif tap_str in ["有交换"] and "time_check" in ret["skill_flag"]:
                #     is_upload_resume = True

                if is_upload_resume:
                    self.boss_upload_resume(resume)
                logger.warning(f"{self.device_session.custom_name},{candidate_name},动作list:{json.dumps(actions)}")
                # 处理动作
                if "delete" in actions:
                    self.delete()
                else:
                    if "change_contact" in actions:
                        self.change_phone({})
                    if "change_wechat" in actions:
                        self.change_WeChat({})
                    if "agree_button_wechat" in actions:
                        self.agree_button_wechat()
                    if "get_attached_resume" in actions:
                        self.get_attached_resume()
                    if "send_position" in actions:
                        self.send_position()
                    if "ask_resume" in actions:    
                        self.ask_resume()
                    #返回至消息列表
                    CommonMobileUtils.back(self.device_session,1)
                self.boss_close()
                
            if is_complete:
                break


    # 设置备注信息
    def set_interview_desc(self,wework):
        desc = ""
        try:
            index = random.randint(0, len(wework["followUpPhoneList"])-1)
            desc = "面试老师：" + wework["realName"] + wework["followUpPhoneList"][index] + "(微信手机同号)。你加一下这个微信具体沟通。"
        except Exception as e:
            1

        # 点击文本框
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_interview_message").click()

        # 输入内容
        self.device_session.find_by_id("com.hpbr.bosszhipin:id/et_interview_message").input(desc)

        # 输入法隐藏
        CommonMobileUtils.back(self.device_session,1)


    # 文本框发送内容
    def send_content_action(self,contents):
        #点击文本输入框
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.EditText']")
        i = 0
        for con in contents:
            i = i + 1
            self.device_session.find_by_xpath(".//*[@class='android.widget.EditText']").input(con)
            time.sleep(1)
            # 点击发送按钮
            self.device_session.find_by_xpath(".//android.widget.EditText[@class='android.widget.EditText']/../android.widget.ImageView[@class='android.widget.ImageView' and @index='3']").click()
            # 获取发送按钮的位置
            # bound = device_session.find_by_xpath(".//*[@class='android.widget.EditText']").get_bounding()
            # if int(glv[self.custom_udid]["screen_width"]) == 1080:
            #     device_session.click(1000,bound[1]+int(bound[3]/2))
            # else:
            #     device_session.click(660,bound[1]+int(bound[3]/2))
        #回退一次，隐藏输入框
        CommonMobileUtils.back(self.device_session,1)

    # 发送定位
    def send_position(self):
        #点击更多
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mMoreIcon']")
        time.sleep(1)
        page_source = self.device_session.get_page_source()
        if "发送位置" in page_source:
            #点击发送位置
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='发送位置']")
            time.sleep(1)
            #点击发送
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action']")

        # 关闭更多的框
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mMoreIcon']")

    # 求简历
    def ask_resume(self):
        page_source = self.device_session.get_page_source()
        if "求简历" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='求简历']")
            time.sleep(1)
            page_source = self.device_session.get_page_source()
            if "确认" in page_source:
                #点击确认
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
            elif "取消" in page_source:
                #点击取消
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='取消']")

    # 标记不合适
    def delete(self):
        #点击标记
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='不合适']")
        #移入不合适
        # CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.TextView' and @text='移入不合适']")
        #选择已找到工作
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_content' and @text='已找到工作']")
        
        page_source = self.device_session.get_page_source()
        if "不开启" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='不开启']")

    # 交换电话
    def change_phone(self,resume):
        change_phone_version_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and contains(@text,'换电话/微信')]")
        is_have_phone = False
        if len(change_phone_version_obj_list) > 0:
            # 点击交换电话
            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and contains(@text,'换电话/微信')]")
            page_source = self.device_session.get_page_source()
            if "请求交换电话" in page_source:
                # 点击请求交换电话
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangePhone']")
                # 点击确认
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
            else:
                CommonMobileUtils.back(self.device_session, 1)
        else:
            change_phone_version_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and contains(@text,'换电话')]")
            if len(change_phone_version_obj_list) > 0:
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and contains(@text,'换电话')]")
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
        page_source = self.device_session.get_page_source()
        if "查看电话" in page_source or "查看微信" in page_source:
            is_have_phone = True

        if is_have_phone:
            self.boss_upload_resume(resume)

    # 定义同意微信函数，接收设备会话对象
    def agree_button_wechat(self):
        i = 0
        while(True):
            i = i + 1
            if i > 5:
                break
            page_source = self.device_session.get_page_source()
            # print(page_source)
            if "BOSS好" in page_source or "可以聊聊啊" in page_source:
                break
            time.sleep(1)
            self.device_session.swipe(170,500,170,duration=50)
            agree_button_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_dialog_btn_right' and @text='同意']")
            if len(agree_button_obj_list)>1:
                for agree_button_obj in agree_button_obj_list:
                    try:
                        # 点击同意
                        agree_button_obj.click()
                        # 等待 1秒，可根据实际情况调整
                        time.sleep(1)
                        page_source = self.device_session.get_page_source()
                        if "取消" in page_source:
                            # 点击取消
                            CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='取消']")
                    except Exception as e:
                        logger.warning(f"{self.device_session.custom_name},点击同意按钮时出错:{e}")

        #点击交换电话
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and contains(@text,'换电话/微信')]")

        time.sleep(1)
        page_source = self.device_session.get_page_source()

        is_have_phone = False
        if "查看电话" in page_source or "查看微信" in page_source:
            is_have_phone = True

        if "请求交换电话" in page_source:
            #点击请求交换电话
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangePhone']")
            time.sleep(1)
            #点击确认
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
        else:
            CommonMobileUtils.back(self.device_session,1)
        
        if is_have_phone:
            phone,wechat = self.boss_parse_phone_wechat()
            connect_position_name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_sub_title").get_attribute("text")
            # 点击右上角的三个点
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/iv_action_2']")
            time.sleep(1)
            # 点击候选人的名字
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_user_name' or @resource-id='com.hpbr.bosszhipin:id/tv_geek_name']")
            time.sleep(2)
            #解析简历
            resume = self.boss_parse_resume()
            CommonMobileUtils.back(self.device_session,2)
            resume["contact"]["phone"]= phone
            resume["contact"]["we_chat"]= wechat
            resume["basic"]["connect_position_name"] = connect_position_name
            resume["basic"]["channel_account_info"] = glv[self.custom_udid]["channelAccountInfo"]

            url = self.domain + "/resume/add"
            req = {"resume":resume,"talk_message":{"resume_id":"","message":[],"source":1}}
            res = common_service.comm_post(url,req,2)
        return    

        

    # 交换微信
    def change_WeChat(self,resume):
        change_phone_version_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and contains(@text,'换电话/微信')]")
        is_have_WeChat = False
        if len(change_phone_version_obj_list)>0:
            #点击交换联系方式
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and contains(@text,'换电话/微信')]")
            page_source = self.device_session.get_page_source()
            if "同意" in page_source:
                self.agree_button_wechat()
            if "请求交换微信" in page_source:
                #点击请求交微信
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangeWeiChat']")
                #点击确认
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
            else:
                CommonMobileUtils.back(self.device_session,1)
        else:
            change_phone_version_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and contains(@text,'换微信')]")
            if len(change_phone_version_obj_list) > 0:
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and contains(@text,'换微信')]")
                # 点击确认
                CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")

        page_source = self.device_session.get_page_source()
        if "查看微信" in page_source or"查看电话" in page_source:
            is_have_WeChat = True

        if is_have_WeChat:
            self.boss_upload_resume(resume)
            
    def boss_get_contact(self):
        phone = ""
        wechat = ""
        # 解析手机号
        phone0,wechat0 = self.boss_parse_phone_wechat()
        if phone0!="":
            phone = phone0
        if wechat0!="":
            wechat = wechat0
        is_agin_parse = False
        page_source = self.device_session.get_page_source()
        if phone0 == "" and "换电话" in page_source:
            #点击交换电话
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[contains(@text,'换电话/微信')]")
            time.sleep(2)
            page_source = self.device_session.get_page_source()
            if "电话请求中" in page_source:
                CommonMobileUtils.back(self.device_session,1)
            elif "请求交换电话" in page_source:
                #点击请求交换电话
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangePhone']")
                time.sleep(1)
                #点击确认
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
            elif "查看电话" in page_source:
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangePhone']")
                is_agin_parse = True
        phone1,wechat1 = self.boss_parse_phone_wechat()
        if phone1!="":
            phone = phone1
        if wechat1!="":
            wechat = wechat1
        # if wechat0 == "":
        #     #点击交换电话/微信
        #     CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and @text='换电话/微信']")
        #     sleep(2)
        #     page_source = self.device_session.get_page_source()
        #     if "请求交换微信" in page_source:
        #         #点击请求交换微信
        #         CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangeWeiChat']")
        #         sleep(1)
        #         #点击确认
        #         CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mSureView' and @text='确认']")
        #     elif "查看微信" in page_source:
        #         CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mExchangeWeiChat']")
        #         is_agin_parse = True
        # phone2,wechat2 = boss_parse_phone_wechat(self.device_session)
        # if phone2!="":
        #     phone = phone2
        # if wechat2!="":
        #     wechat = wechat2
        return phone,wechat

    def boss_upload_resume(self,resume):
        phone,wechat = self.boss_get_contact()
        if phone=="" and wechat=="":
            return
        connect_position_name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_sub_title").get_attribute("text")
        if len(resume)==0:
            # 点击右上角的三个点
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/iv_action_2']")
            time.sleep(1)
            # 点击候选人的名字
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_user_name' or @resource-id='com.hpbr.bosszhipin:id/tv_geek_name']")
            time.sleep(2)
            #解析简历
            resume = self.boss_parse_resume()
            CommonMobileUtils.back(self.device_session,2)

        resume["contact"]["phone"]= phone
        resume["contact"]["we_chat"]= wechat
        resume["basic"]["connect_position_name"] = connect_position_name
        resume["basic"]["channel_account_info"] = glv[self.custom_udid]["channelAccountInfo"]

        url = self.domain + "/resume/add"
        req = {"resume":resume,"talk_message":{"resume_id":"","message":[],"source":1}}
        common_service.comm_post(url,req,2)
        return                 

    def resume_is_talk_use_gpt(self,position_portrait):
        is_comply = True
        resume = {}
        try:
            # 点击右上角的三个点
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/iv_action_2']")
            # 点击候选人的名字
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_user_name' or @resource-id='com.hpbr.bosszhipin:id/tv_geek_name']")
            #解析简历
            resume = self.boss_parse_resume()
            resume_str = self.build_resume_str(resume)
            is_comply = self.cal_is_comply(position_portrait,resume_str,"Bearer fastgpt-h48agQQhdbxQ2Wdm1fe2zW9pvznAuVH2oPsJJarfW58zAYaiA4DoWT")
            if not is_comply:
                logger.warning(f"{self.device_session.custom_name},大模型判断不符合条件，输入:{position_portrait},{resume_str}")

        except Exception as e:
            logger.warning(f"{self.device_session.custom_name},报错{e}")

        # 回退到对话页面
        xpath_list = [".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_sub_title']"]
        CommonMobileUtils.back_to_appoint_page_use_xpath(self.device_session,xpath_list)
        return is_comply,resume
        
    def boss_parse_resume(self):
        page_source = self.device_session.get_page_source()
        contact = {"name":"","phone":"","we_chat":"","email":""}
        if "com.hpbr.bosszhipin:id/tv_geek_name" in page_source:
            contact["name"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_geek_name").get_attribute("text")

        basic = {"age":0,"talk_channel":2,"current_status":"","work_status":"","degree_name":"","expect_place":"","expect_position_name":"","expect_salary_from":"","expect_salary_to":"","gender":"U","h5_content":"","source":1,"source_type":0,"work_year":"","connect_position_name":"","channel_account_info":""}
        basic["source"] = 1
        basic["source_type"] = 1
        basic["talk_channel"] = 2
        basic["gender"] = self.get_gender()

        if "com.hpbr.bosszhipin:id/tv_geek_age" in page_source:
            basic["age"] = int(self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_geek_age").get_attribute("text").replace("岁",""))
            basic["birth"] = ""
        if "com.hpbr.bosszhipin:id/tv_geek_degree" in page_source:
            basic["degree_name"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_geek_degree").get_attribute("text")
        if "com.hpbr.bosszhipin:id/tv_active_status" in page_source:
            basic["current_status"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_active_status").get_attribute("text")
        if "com.hpbr.bosszhipin:id/tv_geek_work_status" in page_source:
            basic["work_status"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_geek_work_status").get_attribute("text")
        if "com.hpbr.bosszhipin:id/tv_job_and_city" in page_source:
            tv_job_and_city = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_job_and_city").get_attribute("text")
            splits = tv_job_and_city.split("，")
            if len(splits) > 1:
                basic["expect_place"] = splits[1]
            if len(splits) > 0:    
                basic["expect_position_name"] = splits[0]
        if "com.hpbr.bosszhipin:id/tv_salary" in page_source:
            tv_salary = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_salary").get_attribute("text")
            match = re.search(r'(\d+)-(\d+)', tv_salary)
            if match:
                basic["expect_salary_from"] = match.group(1)
                basic["expect_salary_to"] = match.group(2)

        work_map = {}
        edu_map = {}

        i = 0
        while(True):
            i = i + 1
            page_source = self.device_session.get_page_source()

            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            work_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_company']/../..",namespaces={'x':'urn:h17-org:v3'})
            for work_obj in work_obj_list:
                work_item = {"corporation_name":"","position_name":"","start_time":"","end_time":"","so_far":0,"description":""}
                corporation_name_list = work_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_company']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(corporation_name_list) == 0:
                    continue
                work_item["corporation_name"] = corporation_name_list[0]
                if work_item["corporation_name"] == "":
                    continue
                tv_time_range_list = work_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_time_range']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(tv_time_range_list) > 0:
                    tv_time_range = tv_time_range_list[0]
                    splits = tv_time_range.split("-")
                    if len(splits) > 1:
                        work_item["end_time"] = splits[1]
                    if len(splits) > 0:   
                        work_item["start_time"] = splits[0]
                    if "至今" in work_item["end_time"]:
                        work_item["so_far"] = 1
                position_name_list = work_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_job_name']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(position_name_list) > 0:
                    work_item["position_name"] = position_name_list[0]
                description_list = work_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_work_desc']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(description_list) > 0:
                    work_item["description"] = description_list[0]
                work_map[work_item["corporation_name"]] = work_item
            edu_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/cl_school']/..",namespaces={'x':'urn:h17-org:v3'})
            for edu_obj in edu_obj_list:
                edu_item = {"degree_name":"","school_name":"","start_time":"","end_time":"","so_far":0}
                school_name_list = edu_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_school']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(school_name_list) == 0:
                    continue
                edu_item["school_name"] = school_name_list[0]
                if edu_item["school_name"] == "":
                    continue
                tv_time_range_list = edu_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_time_range']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(tv_time_range_list) > 0:
                    tv_time_range = tv_time_range_list[0]
                    splits = tv_time_range.split("-")
                    if len(splits) > 1:
                        edu_item["end_time"] = splits[1]
                    if len(splits) > 0:   
                        edu_item["start_time"] = splits[0]
                    if "至今" in edu_item["end_time"]:
                        edu_item["so_far"] = 1
                tv_degree_and_major_list = edu_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_degree_and_major']/@text",namespaces={'x':'urn:h17-org:v3'})
                if len(tv_degree_and_major_list)>0:
                    tv_degree_and_major = tv_degree_and_major_list[0]
                    degree_match = re.search(r'(初中|高中|中专|中技|大专|本科|硕士|博士)', tv_degree_and_major)
                    if degree_match:
                        edu_item["degree_name"] = degree_match.group(1)
                edu_map[edu_item["school_name"]] = edu_item
            if "牛人分析器" in page_source or i > 10:
                break
            self.device_session.swipe(170,1100,170,500,duration=50)

        work = []
        edu = []
        for key in work_map:
            work.append(work_map[key])
        education = []
        for key in edu_map:
            edu.append(edu_map[key])
        resume = {"contact":contact,"basic":basic,"work":work,"education":edu}
        # print(resume)
        return resume

    def build_resume_str(self,resume):
        resume_contact = []
        resume_contact.append("姓名："+resume["contact"]["name"])
        resume_contact.append("学历："+resume["basic"]["degree_name"])
        resume_contact.append("年龄："+str(resume["basic"]["age"]))
        # resume_contact.append("期望职位："+resume["basic"]["expect_position_name"])
        # resume_contact.append("期望薪资："+resume["basic"]["expect_salary_from"]+"-"+resume["basic"]["expect_salary_to"])
        resume_contact.append("\n")
        resume_contact.append("教育经历")
        for education in resume["education"]:
            resume_contact.append("学校："+education["school_name"])
            resume_contact.append("学历："+education["degree_name"])
            resume_contact.append("开始时间："+education["start_time"])
            resume_contact.append("结束时间："+education["end_time"])
            resume_contact.append("\n")

        resume_contact.append("\n")
        resume_contact.append("工作经历")
        for work in resume["work"]:
            resume_contact.append("公司："+work["corporation_name"])
            resume_contact.append("开始时间："+work["start_time"])
            resume_contact.append("结束时间："+work["end_time"])
            resume_contact.append("职位："+work["position_name"])
            resume_contact.append("工作内容："+work["description"])
            resume_contact.append("\n")

        resume_str =  "\n".join(resume_contact)
        return "候选人简历：" +  resume_str

    def boss_parse_phone_wechat(self):
        phone = ""
        wechat = ""
        tv_text_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_text")
        for tv_text_obj in tv_text_objs:
            text = tv_text_obj.get_attribute("text")
            if phone == "" and "的手机号：" in text:
                match = re.search(r'的手机号：(.*)', text)
                if match:
                    phone = match.group(1)
            if wechat == "" and "的微信号：" in text:
                match = re.search(r'的微信号：(.*)', text)
                if match:
                    wechat = match.group(1)
        return phone,wechat

    def boss_send_message(self):
        url = self.domain + "/interview-robot/boss-get-send-messages"
        req = {
            "channelAccountInfo":glv[self.custom_udid]["channelAccountInfo"],
            "token": "maotai"
        }
        retry_num = 2
        messages_map = common_service.comm_post(url, req, retry_num)
        if len(messages_map)==0:
            return
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击消息按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        
        for tap_str in ["有交换","仅沟通","有面试"]:
            page_source = self.device_session.get_page_source()
            if tap_str not in page_source:
                continue
            
            #点击tap按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='"+tap_str+"']")
            
            already_talk_candidate_name_map = {}
            page = 0
            while True:
                page = page + 1
                already_talk_candidate_size_start = len(already_talk_candidate_name_map)

                page_source = self.device_session.get_page_source()
                etree_xml = etree.fromstring(page_source.encode('utf-8'))
                candidate_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/..",namespaces={'x':'urn:h17-org:v3'})
                is_in_inner_time = True
                for candidate_obj in candidate_obj_list:
                    # 判断是否是这两天的时间
                    tv_time = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_time']/@text",namespaces={'x':'urn:h17-org:v3'})
                    if len(tv_time) > 0:
                        tv_time_match = re.search(r'(\d+:\d+)', tv_time[0])

                        if tv_time_match is None:
                            is_in_inner_time = False            
                            break

                    candidate_name = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/@text",namespaces={'x':'urn:h17-org:v3'})[0]

                    if candidate_name=="" or candidate_name in already_talk_candidate_name_map:
                        continue
                    already_talk_candidate_name_map[candidate_name] = candidate_name

                    if candidate_name in messages_map:
                        messages = messages_map.pop(candidate_name)
                        #点击候选人进行沟通
                        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='"+candidate_name+"']")
                        time.sleep(1)
                        
                        #判断是否需要发送表情图片
                        # send_emotion_pic_index = 0
                        # del_y = 0
                        # for message in messages:
                        #     match = re.search(r'发送名片(.*)', message["text"])
                        #     if match != None:
                        #         del messages[del_y]
                        #         card_name = match.group(1).strip()
                        #         if card_name in glv[self.custom_udid]["emotion_pic_map"]:                   
                        #             send_emotion_pic_index = glv[self.custom_udid]["emotion_pic_map"][card_name]
                        #         break
                        #     del_y = del_y + 1

                        # if send_emotion_pic_index > 0:
                        #     boss_send_emotion_pic(self.device_session,send_emotion_pic_index)

                        actions = []
                        if len(messages) > 0:
                            # 发送的内容
                            contents = []
                            # 处理返回的内容
                            for message in messages:
                                # if "交换电话" == message["text"]:
                                if"交换电话" == message["text"] or "交换手机" == message["text"]:
                                    if "change_contact" not in actions:
                                        actions.append("change_contact")
                                elif"交换微信" == message["text"]:
                                    if "change_wechat" not in actions:
                                        actions.append("change_wechat")  
                                elif"同意微信" == message["text"] or "同意手机" == message["text"] or "同意简历" == message["text"]:
                                    if "agree_button_wechat" not in actions:
                                        actions.append("agree_button_wechat") 
                                elif"交换简历" == message["text"]:
                                    if "ask_resume" not in actions:
                                        actions.append("ask_resume") 
                                elif"获取附件简历" == message["text"] or"附件简历" == message["text"]:
                                    if "get_attached_resume" not in actions:
                                        actions.append("get_attached_resume")                                           
                                elif "segmentationflag" in message["text"]:#匹配有没有action动作
                                    content_list = message["text"].split("segmentationflag")
                                    for content in content_list:
                                        action_flag = re.match(r'\[action=(.*?)\]', content)
                                        if action_flag:
                                            actions.append(action_flag.group(1))
                                        elif len(content) > 0:
                                            contents.append(content)
                                elif len(message["text"]) > 0:
                                    contents.append(message["text"])

                            # 发送文本内容
                            if len(contents)>0:
                                self.send_content_action(contents)

                        # 处理动作
                        if "delete" in actions:
                            self.delete()
                        else:
                            if "change_contact" in actions:
                                self.change_phone({})
                            if "change_wechat" in actions:
                                self.change_WeChat({})
                            if "agree_button_wechat" in actions:
                                self.agree_button_wechat()
                            if "get_attached_resume" in actions:
                                self.get_attached_resume()
                            if "send_position" in actions:
                                self.send_position()
                            if "ask_resume" in actions:    
                                self.ask_resume()
                            #回退到列表
                            CommonMobileUtils.back(self.device_session,1)

                already_talk_candidate_size_end = len(already_talk_candidate_name_map)
                if already_talk_candidate_size_start == already_talk_candidate_size_end:
                    break
                if len(messages_map)==0:
                    break
                if not is_in_inner_time:
                    break
                #向下滑动一页
                self.device_session.swipe(170,1100,170,500,duration=50)
            
            time.sleep(1)
            for i in range(page+1):
                #向上滑动一页
                self.device_session.swipe(170,500,170,1100,duration=50)

            if len(messages_map)==0:
                return


    def get_m_geek_info(self,need_group_remark):
        if need_group_remark:
            # 滑动到最上面
            i = 0
            while(True):
                i = i + 1
                page_source = self.device_session.get_page_source()
                if i > 5 or "com.hpbr.bosszhipin:id/ll_head" in page_source:
                    break
                # page_source = self.device_session.get_page_source()
                # if "com.hpbr.bosszhipin:id/ll_head" in page_source:
                #     break
                time.sleep(1)
                self.device_session.swipe(170,500,170,1100,duration=50)

        age = 0
        degree = ""
        group_remark = ""
        page_source = self.device_session.get_page_source()
        if "ll_head" in page_source:
            if "com.hpbr.bosszhipin:id/mGeekInfo" in page_source:
                mGeekInfo = self.device_session.find_by_id("com.hpbr.bosszhipin:id/mGeekInfo").get_attribute("text").replace(" ","").strip()
                degree_match = re.search(r'(初中|高中|中专|中技|大专|本科|硕士|博士)', mGeekInfo)
                if degree_match is not None:
                    degree = degree_match.group(1).strip()

                age_match = re.search(r'\|(\d+)\|', mGeekInfo)
                if age_match is not None:
                    age = int(age_match.group(1).strip())         
                year_start = datetime.datetime.now().year%100 + 1
                year_end = datetime.datetime.now().year%100 + 10
                for year in range(year_start,year_end):
                    if (str(year) + "年应届生") in mGeekInfo:
                        age = 16
                        break
                
            elif "tv_age" in page_source:
                degree_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_degree").get_attribute("text").replace(" ","").strip()
                degree_match = re.search(r'(初中|高中|中专|中技|大专|本科|硕士|博士)', degree_info)
                if degree_match is not None:
                    degree = degree_match.group(1).strip()
                age_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_age").get_attribute("text").replace(" ","").strip()
                age_match = re.search(r'(\d+)', age_info)
                if age_match is not None:
                    age = int(age_match.group(1).strip())
                exp_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_position_exp").get_attribute("text").replace(" ","").strip()
                year_start = datetime.datetime.now().year%100 + 1
                year_end = datetime.datetime.now().year%100 + 10
                for year in range(year_start,year_end):
                    if (str(year) + "年应届生") in exp_info:
                        age = 16
                        break
                
            if need_group_remark:
                if "com.hpbr.bosszhipin:id/tv_company_name" in page_source:
                    m_content_view_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_company_name")
                    for m_content_view in m_content_view_list:
                        group_remark = group_remark + m_content_view.get_attribute("text").replace(" ","").strip()
                elif "com.hpbr.bosszhipin:id/mContentView" in page_source:
                    m_content_view_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/mContentView")
                    for m_content_view in m_content_view_list:
                        group_remark = group_remark + m_content_view.get_attribute("text").replace(" ","").strip()

        logger.warning(f"{self.device_session.custom_name},age:{age},degree:{degree},group_remark:{group_remark}")

        return age,degree,group_remark

    def boss_new_call(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        page_source = self.device_session.get_page_source()
        if "仅沟通" not in page_source:
            # 点击消息按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        page_source = self.device_session.get_page_source()
        if "新招呼" not in page_source:
            return
        #点击新招呼按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='新招呼']")
        #循环处理候选人
        candidate_name_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_name")
        for candidate_name_obj in candidate_name_objs:
            candidate_name_obj.click()
            time.sleep(1)
            is_meet_condition = True
            page_source = self.device_session.get_page_source()
            if "mGeekInfo" in page_source:
                m_geek_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/mGeekInfo").get_attribute("text").replace(" ","").strip()
                
                year_start = datetime.datetime.now().year%100 + 1
                year_end = datetime.datetime.now().year%100 + 10
                for year in range(year_start,year_end):
                    if (str(year) + "年应届生") in m_geek_info:
                        is_meet_condition = False
                        break

            if is_meet_condition:
                req = {
                    "age":0,
                    "degree":"",
                    "channelAccountInfo":glv[self.custom_udid]["channelAccountInfo"],
                    "positionName":"",
                    "token": "maotai"
                }
                if "mGeekInfo" in page_source:
                    m_geek_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/mGeekInfo").get_attribute("text").replace(" ","").strip()
                    degree_match = re.search(r'(初中|高中|中专|中技|大专|本科|硕士|博士)', m_geek_info)
                    if degree_match is not None:
                        req["degree"] = degree_match.group(1).strip()
                    age_match = re.search(r'\|(\d+)\|', m_geek_info)
                    if age_match is not None:
                        req["age"] = age_match.group(1).strip()
                elif "tv_degree" in page_source:
                    degree_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_degree").get_attribute("text").replace(" ","").strip()
                    degree_match = re.search(r'(初中|高中|中专|中技|大专|本科|硕士|博士)', degree_info)
                    if degree_match is not None:
                        req["degree"] = degree_match.group(1).strip()

                    age_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_age").get_attribute("text").replace(" ","").strip()
                    age_match = re.search(r'(\d+)', age_info)
                    if age_match is not None:
                        req["age"] = int(age_match.group(1).strip())
                    
                    exp_info = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_position_exp").get_attribute("text").replace(" ","").strip()
                    year_start = datetime.datetime.now().year%100 + 1
                    year_end = datetime.datetime.now().year%100 + 10
                    for year in range(year_start,year_end):
                        if (str(year) + "年应届生") in exp_info:
                            req["age"] = 16
                            break


                req["positionName"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_sub_title").get_attribute("text").strip()
                url = self.domain + "/interview-robot/boss-is-meet-condition"
                retry_num = 2
                is_meet_condition = common_service.comm_post(url, req, retry_num)
            if not is_meet_condition:
                #点击标记
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextContent' and @text='标记']")
                #移入不合适
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.TextView' and @text='移入不合适']")
                #选择已找到工作
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_content' and @text='已找到工作']")
                
                page_source = self.device_session.get_page_source()
                if "不开启" in page_source:
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_negative' and @text='不开启']")

                continue
            #点击文本输入框
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.EditText']")
            remind_content = "你好啊，可以聊聊~"
            self.device_session.find_by_xpath(".//*[@class='android.widget.EditText']").input(remind_content)
            time.sleep(1)
            # 点击发送按钮
            self.device_session.find_by_xpath(".//android.widget.EditText[@class='android.widget.EditText']/../android.widget.ImageView[@class='android.widget.ImageView' and @index='3']").click()
            # 获取发送按钮的位置
            # bound = device_session.find_by_xpath(".//*[@class='android.widget.EditText']").get_bounding()
            # if int(glv[self.custom_udid]["screen_width"]) == 1080:
            #     device_session.click(1000,bound[1]+int(bound[3]/2))
            # else:
            #     device_session.click(660,bound[1]+int(bound[3]/2))
            time.sleep(1)
            self.device_session.find_by_xpath(".//*[@class='android.widget.EditText']").click()
            time.sleep(1)
            #点击更多
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mMoreIcon']")
            time.sleep(1)
            page_source = self.device_session.get_page_source()
            if "发送位置" in page_source:
                #点击发送位置
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/mTextView' and @text='发送位置']")
                time.sleep(1)
                #点击发送
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_btn_action']")
            CommonMobileUtils.back(self.device_session,1)
            time.sleep(1)



    def boss_get_positions(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_4']")
        # 点击管理职位
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_sub_title']")

        # 点击开放中的职位
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.TextView' and @text='开放中']")
        time.sleep(2)
        # 职位列表
        position_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_name")

        positions_open = []    
        status = 1

        position_count = len(position_objs)
        count = 0
        while True:
            if (count+1) > position_count:
                break
            # 点击开放中的职位
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.TextView' and @text='开放中']")
            time.sleep(2)
            # 职位列表
            position_objs = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_name")
            i = 0
            for position_obj in position_objs:
                if i == count:
                    #点击职位
                    position_obj.click()
                    time.sleep(0.5)
                    position = self.boss_parse_position(status)
                    positions_open.append(position)
                    self.device_session.back()
                i = i + 1
            
            count = count + 1
        logger.warning(f"{self.device_session.custom_udid},positions_open:{json.dumps(positions_open)}")
        return positions_open

    def boss_upload_data(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_4']")
        
        positions = self.boss_get_positions()
        positions = self.boss_get_statistics_data(positions)
        logger.warning(f"{self.device_session.custom_name},positions:{positions}")

        if len(positions) > 0:
            req = {
                "source":1,
                "channelAccountInfo" : glv[self.custom_udid]["channelAccountInfo"],
                "positionInfo" : positions,
                "isSyncStatistics": True
            }
            url = self.domain + "/third-channel/upload-position"
            retry_num = 1
            results = common_service.comm_post(url, req, retry_num)

    #解析职位
    def boss_parse_position(self,status):
        position = {
            "positionAddress": "",
            "positionStatus": status,
            "positionName": self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_job_name").get_attribute(
                        "text"),
            "positionArea": self.device_session.find_by_id(
                "com.hpbr.bosszhipin:id/tv_required_location").get_attribute("text")
        }
        if status == 1:
            bound = self.device_session.find_by_id("com.hpbr.bosszhipin:id/btn_reset").get_bounding()
            for i in range(2):
                page_source = self.device_session.get_page_source()
                if "com.hpbr.bosszhipin:id/iv_map" not in page_source:
                    self.device_session.swipe(bound[0],bound[1]-100,bound[0],bound[1]-1300)
                else:
                    break
            # 点击工作地址
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/iv_map']")
            time.sleep(1)
            position["positionAddress"] = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_target").get_attribute("text")
        self.device_session.back()
        return position


    def boss_get_statistics_data(self,positions_open):
        # positions_open = [{'positionAddress': '上海闵行区沐海养生SPA会所歪马送酒（莘庄店）', 'positionStatus': 1, 'positionName': '新店开业/酒水专送/月入8k+', 'positionArea': '上海 莘庄'}, {'positionAddress': '上海嘉定区旭辉U天地上海市嘉定区宝翔路158弄7号101室', 'positionStatus': 1, 'positionName': '嘉定宝山可提供住宿/就近分配/酒水配送', 'positionArea': '上海'}]
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_4']")
        # 点击招聘数据
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='招聘数据']")
        for i in range(0, 3):
            self.device_session.swipe(170, 320, 170, 1100)
        time.sleep(3)
        positions_res = []
        for i in range(2):
            recordDate = datetime.datetime.now().strftime("%Y-%m-%d")
            if i == 1:
                today = datetime.datetime.now()
                yesterday = today - datetime.timedelta(days=1)
                recordDate = yesterday.strftime("%Y-%m-%d")
                if glv[self.custom_udid]["yesterday_is_upload"] == recordDate:
                    break
                ToolService.build_common_var(self.custom_udid,"yesterday_is_upload",recordDate)

                # 点击今日数据
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[(@class='android.view.View' or @class='android.widget.TextView') and @text='今日数据']")

                # 点击回查昨天数据
                is_have_yesterday_data = False
                yesterday_data_str = "回查" + recordDate
                page_source = self.device_session.get_page_source()
                if yesterday_data_str in page_source:
                    is_have_yesterday_data = True
                    yesterday_data_button_obj = self.device_session.find_by_xpath(".//*[(@class='android.view.View' or @class='android.widget.TextView') and @text='"+yesterday_data_str+"']")
                    bound = yesterday_data_button_obj.get_bounding()
                    self.device_session.swipe(bound[0]+50,bound[1],bound[0]+50,bound[1]-80)
                    # 点击确定按钮
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[(@class='android.view.View' or @class='android.widget.TextView') and @text='确定']")
                    page_source = self.device_session.get_page_source()
                    if yesterday.strftime("%Y.%m.%d")+"数据" not in page_source:
                        is_have_yesterday_data = False
                if not is_have_yesterday_data:
                    break
            
            # 解析全部职位的数据
            all_position = {}
            all_position["positionAddress"] = ""
            all_position["positionStatus"] = 1
            all_position["positionName"] = "全部职位"
            all_position["positionArea"] = "上海"
            all_position["recordDate"] = recordDate
            datas = []
            data_obj_list = self.device_session.find_all_by_xpath(".//*[(@class='android.view.View' or @class='android.widget.TextView') and ends-with(@text, '人')]")
            for data_obj in data_obj_list:
                data_text = data_obj.get_attribute("text")
                data = re.match(r'(\d+)', data_text)
                if data!=None:
                    datas.append(data.group(1))
            all_position["meLook"] = datas[0]
            all_position["lookMe"] = datas[1]
            all_position["meCall"] = datas[2]
            all_position["callMe"] = datas[3]
            all_position["meTalk"] = datas[4]
            all_position["haveResume"] = datas[5]
            all_position["changePhone"] = datas[6]
            all_position["agreeInter"] = datas[7]
                
            # 当前职位数据加入到返回的职位列表内    
            positions_res.append(all_position.copy())

            # 点击全部职位
            time.sleep(2)
            
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[(@class='android.widget.TextView' or @class='android.view.View') and @text='全部职位']")
            index = 0
            for position in positions_open:
                index = index + 1
                position["recordDate"] = recordDate
                # 点击发布中的职位
                click_status = CommonMobileUtils.click_use_xpath(self.device_session,"(.//android.widget.ListView[@class='android.widget.ListView']//android.view.View[contains(@text,'" +position["positionName"] + "')])")
                if not click_status:
                    continue
                time.sleep(2)
                datas = []
                
                data_obj_list = self.device_session.find_all_by_xpath(".//*[(@class='android.view.View' or @class='android.widget.TextView') and ends-with(@text, '人')]")
                for data_obj in data_obj_list:
                    data_text = data_obj.get_attribute("text")
                    data = re.match(r'(\d+)', data_text)
                    if data is not None:
                        datas.append(data.group(1))
                position["meLook"] = datas[0]
                position["lookMe"] = datas[1]
                position["meCall"] = datas[2]
                position["callMe"] = datas[3]
                position["meTalk"] = datas[4]
                position["haveResume"] = datas[5]
                position["changePhone"] = datas[6]
                position["agreeInter"] = datas[7]
                    
                # 当前职位数据加入到返回的职位列表内    
                positions_res.append(position.copy())
                
                # 点击当前职位
                sel_position_name = position["positionName"]
                if len(position["positionName"])>=8:
                    sel_position_name = position["positionName"][:8] + "..."
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[(@class='android.view.View' or @class='android.widget.TextView') and @text='"+sel_position_name+"']")

                if index == len(positions_open) and i==0:
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[(@class='android.widget.TextView' or @class='android.view.View') and @text='全部职位']")

            if i==1:    
                # 回退一次
                CommonMobileUtils.back(self.device_session,1)
                
        return positions_res


    def boss_sencond_remind(self):
        # remind_list = [{"id":0,"name":"朱晓亮"},{"id":0,"name":"安德森发顺丰"},{"id":0,"name":"李天"}]
        url = self.domain + "/third-channel/get-remind-candidate"
        req = {
            "channel_account_info":glv[self.custom_udid]["channelAccountInfo"]
        }
        retry_num = 2
        remind_list = common_service.comm_post(url, req, retry_num)
        if remind_list is None or len(remind_list) == 0:
            return
        ids = []
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击消息按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        # 点击搜索输入框
        self.boss_click_input_in_message_page()
        
        et_input_xpath = ".//*[@resource-id='com.hpbr.bosszhipin:id/et_input']"
        for remind in remind_list:
            try:
                ids.append(remind["id"])
                # 输入搜索词
                self.device_session.find_by_xpath(et_input_xpath).input(remind["name"])
                time.sleep(1)
                page_source = self.device_session.get_page_source()
                if "暂无相关联系人" in page_source:
                    continue
                # 判断点击哪个联系人
                tv_user_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_user_info' and @text='"+remind["name"]+"']")
                if len(tv_user_obj_list) == 0:
                    continue
                tv_user_obj_list[0].click()
                time.sleep(1)
                #点击文本输入框
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@class='android.widget.EditText']")
                # 输入二次提醒内容
                remind_content = "加我微信了吗？微信上没找到你[捂脸]，没加的话辛苦加一下哈[玫瑰]"
                self.device_session.find_by_xpath(".//*[@class='android.widget.EditText']").input(remind_content)
                # 点击发送按钮
                self.device_session.find_by_xpath(".//android.widget.EditText[@class='android.widget.EditText']/../android.widget.ImageView[@class='android.widget.ImageView' and @index='3']").click()
                # # 获取发送按钮的位置
                # bound = device_session.find_by_xpath(".//*[@class='android.widget.EditText']").get_bounding()
                # if int(glv[self.custom_udid]["screen_width"]) == 1080:
                #     device_session.click(1000,bound[1]+int(bound[3]/2))
                # else:
                #     device_session.click(660,bound[1]+int(bound[3]/2))

                CommonMobileUtils.back(self.device_session,1)
            except Exception as e:
                logger.warning(f"{self.device_session.custom_name},e:{e}")

                # 回退到主页面
                CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
                # 点击消息按钮
                CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
                # 点击搜索输入框
                self.boss_click_input_in_message_page()
                
        url = self.domain + "/third-channel/update-remind-num"
        req = {
            "ids":ids
        }
        retry_num = 1
        common_service.comm_post(url, req, retry_num)

    def boss_parse_dialogue_record(self):
        page_source = self.device_session.get_page_source()
        candidate_name = self.device_session.find_by_id("com.hpbr.bosszhipin:id/tv_title").get_attribute("text")
        #使用etree格式化xml
        etree_xml = etree.fromstring(page_source.encode('utf-8'))
        record_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_content_text' or @resource-id='com.hpbr.bosszhipin:id/tv_text']/..",namespaces={'x':'urn:h17-org:v3'})

        #解析对话记录
        records = []
        for record_obj in record_obj_list:
            ower = glv[self.custom_udid]["channelAccountInfo"]
            is_friend = 0
            if "com.hpbr.bosszhipin:id/mAvatarContainer" in etree.tostring(record_obj).decode("utf-8"):
                is_friend = 1
                ower = candidate_name

            cons = record_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_content_text' or @resource-id='com.hpbr.bosszhipin:id/tv_text']/@text",namespaces={'x':'urn:h17-org:v3'})
            if len(cons)==0:
                continue
            con = cons[0]
            if "com.hpbr.bosszhipin:id/tv_text" in etree.tostring(record_obj).decode("utf-8"):
                time_match = re.search(r'(\d+:\d+)', con)
                if time_match:
                    continue
                if "发送了面试邀请" in con:
                    continue
                elif "接受了面试邀请" in con:
                    is_friend = 1
                    ower = candidate_name
                elif "的手机号" in con:
                    is_friend = 1
                    ower = candidate_name
                elif "的微信号" in con:
                    is_friend = 1
                    ower = candidate_name
                elif "请求交换" in con:
                    is_friend = 0
                    ower = glv[self.custom_udid]["channelAccountInfo"]
                elif "和您交换" in con:
                    is_friend = 1
                    ower = candidate_name
                else:
                    is_friend = 1
                    ower = candidate_name

            records.append({"ower":ower,"con":cons[0],"is_friend":is_friend})
        logger.warning(f"{self.device_session.custom_name},records:{json.dumps(records)}")

        return records


    def boss_click_input_in_message_page(self):
        # 点击搜索输入框
        et_input_xpath = ".//*[@resource-id='com.hpbr.bosszhipin:id/et_input']"
        i = 0
        while True:
                i = i+1
                if i > 30:
                    return False
                try:
                    self.device_session.find_by_xpath(et_input_xpath).click()
                    return True
                except Exception as e:
                    bound = self.device_session.find_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']").get_bounding()
                    self.device_session.swipe(bound[0],bound[1]-900,bound[0],bound[1]-100,duration=50)
                    time.sleep(0.1)


    def boss_send_emotion_pic(self,index):
        if index == 0 :
            return
        while(True):
            page_source = self.device_session.get_page_source()
            if 'com.hpbr.bosszhipin:id/mEmotionRecycleView' in page_source or "全部表情" in page_source:
                break
            CommonMobileUtils.click_use_xpath(self.device_session,"(.//*[@resource-id='com.hpbr.bosszhipin:id/mEmotionView'])[1]")
        time.sleep(1)
            
        if int(glv[self.custom_udid]["screen_width"]) == 1080:
            self.device_session.click(207,2311)
        else:
            self.device_session.click(135,1555)
        time.sleep(1)

        send_emotion_view_xpath = ".//*[@resource-id='com.hpbr.bosszhipin:id/mEmotionRecycleView']/android.widget.LinearLayout[@index='"+str(index)+"']"
        emotion_views = self.device_session.find_all_by_xpath(send_emotion_view_xpath)
        if len(emotion_views) == 1:
            CommonMobileUtils.click_use_xpath(self.device_session,send_emotion_view_xpath)


    def cron_upload_resume(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        page_source = self.device_session.get_page_source()
        if "仅沟通" not in page_source:
            # 点击消息按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_3']")
        tap_str_list = ["有面试"]
        for tap_str in tap_str_list:
            page_source = self.device_session.get_page_source()
            if tap_str not in page_source:
                continue
            
            #点击tap按钮
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_title' and @text='"+tap_str+"']")
            
            # 滚动到最上面
            is_roll_top = False
            tv_name_top= ""
            while(True):
                tv_name_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_name")
                if len(tv_name_obj_list)==0:
                    break
                tv_name = tv_name_obj_list[0].get_attribute("text")
                if tv_name == tv_name_top:
                    break
                tv_name_top = tv_name
                self.device_session.swipe(170,320,170,1100,duration=50)


            is_complete = False
            
            while(True):
                already_talk_candidate_size_start = len(glv[self.custom_udid]["already_upload_resume_name_map"])
                page_source = self.device_session.get_page_source()
                etree_xml = etree.fromstring(page_source.encode('utf-8'))
                candidate_obj_list = etree_xml.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/..",namespaces={'x':'urn:h17-org:v3'})
                for candidate_obj in candidate_obj_list:
                    # 判断是否是这两天的时间
                    tv_time = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_time']/@text",namespaces={'x':'urn:h17-org:v3'})
                    if len(tv_time) == 0:
                        is_complete = True
                        break

                    date=datetime.date.today() 
                    if "昨天" in tv_time:
                        today=datetime.date.today() 
                        oneday=datetime.timedelta(days=1) 
                        date=today-oneday  

                    tv_time_match = re.search(r'(\d+:\d+)', tv_time[0])
                    if tv_time_match is None:
                        is_complete = True     
                        break

                    time_str = str(date) + " " + tv_time_match.group(1) + ":00"
                    time_format = '%Y-%m-%d %H:%M:%S'
                    parsed_time_struct = time.strptime(time_str, time_format)
                    parsed_time = int(time.mktime(parsed_time_struct))

                    candidate_name_list = candidate_obj.xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name']/@text",namespaces={'x':'urn:h17-org:v3'})
                    if len(candidate_name_list)>1:
                        continue
                    candidate_name = candidate_name_list[0]
                    already_upload_resume_name_map = glv[self.custom_udid]["already_upload_resume_name_map"]
                    if candidate_name=="" or candidate_name in already_upload_resume_name_map:
                        continue
                
                    already_upload_resume_name_map[candidate_name] = candidate_name

                    ToolService.build_common_var(self.custom_udid,"already_upload_resume_name_map",already_upload_resume_name_map)

                    # 点击候选人进行简历上传
                    CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name' and @text='"+candidate_name+"']")
                    # 上传简历
                    self.boss_upload_resume({})
                    # 返回至列表页
                    CommonMobileUtils.back(self.device_session,1)

                if already_talk_candidate_size_start == len(glv[self.custom_udid]["already_upload_resume_name_map"]):
                    break

                self.device_session.swipe(170,1100,170,500)
                if is_complete:
                    break

        # 最后一次上传时间
        ToolService.build_common_var(self.custom_udid,"boss_last_upload_resume_timestamp",int(time.time()))
        return

    def boss_call_config(self):
        if ToolService.get_today_is_complete(self.custom_udid,"boss_call_config_update"):
            return
        try:
            # 更新配置
            url = self.domain + "/third-channel/report-channel-account-status"
            payload = json.dumps({"channel_account_info":glv[self.custom_udid]["channelAccountInfo"]})
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            res = json.loads(response.text)
            if res["err_no"] is not None and int(res["err_no"])==0:
                ToolService.build_common_var(self.custom_udid,"boss_call_config",res["results"])
            ToolService.set_today_is_complete(self.custom_udid,"boss_call_config_update")

        except Exception as e:
            logger.warning(f"{self.device_session.custom_name},e:{e}")

        # logger.warning(f"{self.device_session.custom_name},boss_call_config:{glv[self.custom_udid]["boss_call_config"]}")



    def boss_close(self):
        page_source = self.device_session.get_page_source()
        if "com.hpbr.bosszhipin:id/iv_close" in page_source:
            CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/iv_close']")

    def set_channel_account_info(self):
        # 回退到主页面
        CommonMobileUtils.back_to_appoint_page(self.device_session,"com.hpbr.bosszhipin:id/cl_tab_4")
        # 点击我的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/cl_tab_4']","click")
        # 滚动到最上面
        i = 0
        while True:
            i = i + 1
            if i>3:
                break
            self.device_session.swipe(170,500,170,1100)

        # 点击名字
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/ctl_f3_profile']","click")
        # 点击姓名
        CommonMobileUtils.click_use_xpath(self.device_session,".//*[@resource-id='com.hpbr.bosszhipin:id/tv_name_label']","click")

        name = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_name")[0].get_attribute("text")
        CommonMobileUtils.back(self.device_session,1)

        company_name = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_brand")[0].get_attribute("text")
        if len(company_name)>6:
            company_name = company_name[:6]
        nick_name = company_name +"-"+ name
        
        CommonMobileUtils.back(self.device_session,1)

        url = self.domain + "/third-channel/get-channel-account-config"

        req = {
            "channel_account_info": "",
            "nick_name": nick_name,
            "token": "maotai"
        }

        res = common_service.comm_post(url, req, 1)
        if res["channelInfo"] is None:
            logger.warning("致命问题，账号配置错误，请重新配置后重试")
            raise Exception("致命问题，账号配置错误，请重新配置后重试")
        
        ToolService.build_common_var(self.custom_udid,"channelAccountInfo",res["channelInfo"])
        common_service.set_run_config(self.device_session)
        common_service.set_variable(self.device_session)

    def get_gender(self):
        # xpath_list = [".//android.widget.TextView[@resource-id='com.hpbr.bosszhipin:id/tv_geek_name']"]
        # try:
        #     img = package.image_selector("male_flag")
        #     self.device_session.click_image([img],"MiddleCenter",0,0)
        #     CommonMobileUtils.back(self.device_session,1)
        #     return "M"
        # except Exception as e:
        #     1
        #
        # try:
        #     img = package.image_selector("female_flag")
        #     device_session.click_image([img],"MiddleCenter",0,0)
        #     CommonMobileUtils.back(self.device_session,1)
        #     return "F"
        # except Exception as e:
        #     1
        return "U"

    def calculate_resume_score(self, expect_con, history_con):
        resume_score_map_list = []
        keyword_map4 = {"name": "保险销售", "level": "B", "sort": "4", "reg": "(保险顾问|车险专员|续保专员)"}
        resume_score_map_list.append(keyword_map4)

        keyword_map1 = {"name": "销售", "level": "S", "sort": "1","reg": "(bd|BD|催收|地产中介|电销|会籍顾问|健康顾问|客户代表|客户经理|课程顾问|理财顾问|留学顾问|旅游顾问|面销|商务|信贷专员|医药代表|营业员|招生顾问|证券经纪人|置业顾问|促销员|信用卡|销售)"}
        resume_score_map_list.append(keyword_map1)

        keyword_map2 = {"name": "客服", "level": "C", "sort": "2","reg": "(咨询热线|售前客服|电话客服|网络客服|售后客服|电商运营|客户成功|客服)"}
        resume_score_map_list.append(keyword_map2)

        keyword_map3 = {"name": "销售相关", "level": "R", "sort": "3","reg": "(主播|导购|店员|店长|督导|导购|酒店经理|酒店前台|前厅经理|店长|市场推广|市场营销|外贸业务员|网络推广|医美咨询|银行大堂经理|美容顾问)"}
        resume_score_map_list.append(keyword_map3)

        expect_map = {"name": "其他", "level": "Q", "sort": "5", "reg": ""}
        for resume_score_map in resume_score_map_list:
            match_obj = re.search(resume_score_map["reg"], expect_con)
            if match_obj is not None:
                expect_map = resume_score_map
                break

        history_map = {"name": "其他", "level": "Q", "sort": "5", "reg": ""}
        for resume_score_map in resume_score_map_list:
            match_obj = re.search(resume_score_map["reg"], history_con)
            if match_obj is not None:
                history_map = resume_score_map
                break
        return expect_map["level"] + history_map["sort"]

    def is_call_in_detail(self):
        # 判断我的同事是否已经沟通过
        cl_parent_obj_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.hpbr.bosszhipin:id/title_actions']/*[@resource-id='com.hpbr.bosszhipin:id/cl_parent']")
        if len(cl_parent_obj_list) == 4:
            logger.warning(f"{self.device_session.custom_name},我的同事已经沟通过")
            return False

        # # 判断是否在线,判断是21点前或后
        # page_source = self.device_session.get_page_source()
        # hour = int(datetime.datetime.now().strftime("%H"))
        # if hour>=21:
        #     if "com.hpbr.bosszhipin:id/iv_online_state" not in page_source and "今日活跃" not in page_source and "刚刚活跃" not in page_source:
        #         logger.warning(f"本次不打招呼.原因:不在线or不是刚刚活跃or不是今日活跃")
        #         return False
        # else:
        #     if "com.hpbr.bosszhipin:id/iv_online_state" not in page_source:
        #         logger.warning(f"本次不打招呼.原因:不在线")
        #         return False

        # 判断年龄
        age_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_geek_age")
        if len(age_list) > 0:
            age_match = re.match(r'(\d+)岁', age_list[0].get_attribute("text"))
            if age_match:
                age = ToolService.str_to_int(age_match.group(1))
                if age > 0 and (age < glv[self.custom_udid]["boss_call_config"]["startAge"] or age > glv[self.custom_udid]["boss_call_config"]["endAge"]):
                    logger.warning(f"{self.device_session.custom_name},年龄{age},不符合条件")
                    return False

        tv_job_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_and_city")

        # 判断期望职位包含
        expect_in = False
        if len(glv[self.custom_udid]["boss_call_config"]["expectPositions"]) > 0:
            for tv_job_obj in tv_job_obj_list:
                is_continue = True
                for expectPosition in glv[self.custom_udid]["boss_call_config"]["expectPositions"]:
                    if expectPosition in tv_job_obj.get_attribute("text"):
                        is_continue = False
                        expect_in = True
                        break
                if not is_continue:
                    break
            if not expect_in:
                logger.warning(f"{self.device_session.custom_name},期望职位包含,不符合条件")
                return False

        # 判断期望职位不包含
        expect_no_in = False
        if len(glv[self.custom_udid]["boss_call_config"]["expectPositionNos"]) > 0:
            for tv_job_obj in tv_job_obj_list:
                is_continue = True
                for expectPositionNo in glv[self.custom_udid]["boss_call_config"]["expectPositionNos"]:
                    if expectPositionNo in tv_job_obj.get_attribute("text"):
                        is_continue = False
                        expect_no_in = True
                        break
                if not is_continue:
                    break
            if expect_no_in:
                logger.warning(f"{self.device_session.custom_name},期望职位不包含,不符合条件")
                return False

        # 判断离职随时到岗
        if "不限" not in glv[self.custom_udid]["boss_call_config"]["jobIntentions"] and len(
                glv[self.custom_udid]["boss_call_config"]["jobIntentions"]) > 0:
            tv_geek_work_status_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_geek_work_status")
            if len(tv_geek_work_status_list) == 0:
                return False

            is_job_intention = False
            for call_job_intention in glv[self.custom_udid]["boss_call_config"]["jobIntentions"]:
                if len(call_job_intention) > 0 and call_job_intention in tv_geek_work_status_list[0].get_attribute(
                        "text"):
                    is_job_intention = True
                    break
            if is_job_intention == False:
                return False

        # 判断薪资
        salary_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_salary")
        if len(salary_list) == 0:
            logger.warning(f"{self.device_session.custom_name},没匹配到薪资")
            return False

        match_obj = re.search(r'(.*?)K', salary_list[0].get_attribute("text"))
        if match_obj is not None:
            salay = match_obj.group(1).split("-")
            if len(salay) != 2:
                logger.warning(f"{self.device_session.custom_name},没匹配到薪资")
                return False
            if glv[self.custom_udid]["boss_call_config"]["callSalayHigh"] is not None and glv[self.custom_udid]["boss_call_config"]["callSalayHigh"] > 0:
                if glv[self.custom_udid]["boss_call_config"]["callSalayHigh"] < int(salay[1]) * 1000:
                    logger.warning(f"{self.device_session.custom_name},{salay},不符合薪资的条件")
                    return False
            if glv[self.custom_udid]["boss_call_config"]["callSalayLow"] is not None and glv[self.custom_udid]["boss_call_config"]["callSalayLow"] > 0:
                if glv[self.custom_udid]["boss_call_config"]["callSalayLow"] < int(salay[0]) * 1000:
                    logger.warning(f"{self.device_session.custom_name},{salay},不符合薪资的条件")
                    return False
        else:
            logger.warning(f"{self.device_session.custom_name},没匹配到薪资")
            return False

        if len(glv[self.custom_udid]["boss_call_config"]["resumeScores"]) > 0:
            if len(tv_job_obj_list) == 0:
                logger.warning(f"{self.device_session.custom_name},未匹配到期望职位")
                return False

            history_position_from_boss = ""

            tv_desc_obj_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_desc")
            for tv_desc_obj in tv_desc_obj_list:
                history_position_from_boss = history_position_from_boss + tv_desc_obj.get_attribute("text")

            # 滚动展示共工作经历
            self.device_session.swipe(170, 600, 170, 500,duration=50)

            tv_job_name_history_list = self.device_session.find_all_by_id("com.hpbr.bosszhipin:id/tv_job_name")
            for tv_job_name_history in tv_job_name_history_list:
                history_position_from_boss = history_position_from_boss + tv_job_name_history.get_attribute("text")

            expect_position_from_boss = tv_job_obj_list[0].get_attribute("text")

            resume_score = self.calculate_resume_score(expect_position_from_boss, history_position_from_boss)

            if resume_score not in glv[self.custom_udid]["boss_call_config"]["resumeScores"]:
                logger.warning(f"{self.device_session.custom_name},{resume_score}不符合得分条件")
                return False
            logger.warning(f"{self.device_session.custom_name},{resume_score}符合得分条件")
        return True

    def cal_is_comply(self,position_portrait,resume_str,token):
        url = "https://gpt.hctalent.cn/api/v1/chat/completions"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': token
        }
        messages = [{"content":resume_str,"role":"user"}] 
        req={
            "stream":False,
            "detail":False,
            "variables":{"Job_Portrait":position_portrait},
            "messages":messages
        }
        payload = json.dumps(req)
        response = requests.request("POST", url, headers=headers, data=payload, timeout=300)
        res = json.loads(response.text)
        content = res["choices"][0]["message"]["content"]
        # print("大模型判断输入:"+position_portrait+resume_str+"，输出："+content)
        if content[0:1]=="是":
            return True
        else:
            return False

    def lb_cal_is_comply(self,position_portrait,token):
        url = "https://gpt.hctalent.cn/api/v1/chat/completions"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': token
        }
        messages = [{"content":position_portrait,"role":"user"}] 
        req={
            "stream":False,
            "detail":False,
            "variables":{},
            "messages":messages
        }
        payload = json.dumps(req)
        response = requests.request("POST", url, headers=headers, data=payload, timeout=300)
        res = json.loads(response.text)
        content = res["choices"][0]["message"]["content"]
        return content

    # 获取附件简历 
    def get_attached_resume(self):
        # todo
        return True