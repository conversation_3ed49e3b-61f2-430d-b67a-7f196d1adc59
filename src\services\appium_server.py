import subprocess
import time
import os
import atexit
import logging
from datetime import datetime
import socket
import platform

logger = logging.getLogger('appium_launcher')
logger.propagate = False

handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s %(name)s %(levelname)s %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# 全局变量，用于持有 Appium 子进程对象
_appium_process = None

def get_daily_log_file(base_log_dir="logs/appium",port=""):
    today_str = datetime.now().strftime("%Y-%m-%d")
    log_filename = f"{port}_{today_str}.log"
    log_file_path = os.path.join(base_log_dir, log_filename)

    if not os.path.exists(base_log_dir):
        os.makedirs(base_log_dir)
        logger.debug(f"Created log directory: {base_log_dir}")

    return log_file_path

def is_port_in_use(port):
    """检查端口是否正在使用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def start_server(port=4723, log_level=""):
    """
    启动 Appium Server 服务。
    此函数保证只启动一个实例。
    """
    level = getattr(logging, log_level.upper(), logging.ERROR)
    logger.setLevel(level)

    global _appium_process
    logger.debug(f"start_server in : {port}")

    # 1. 检查 端口是否正在被使用
    if is_port_in_use(port):
        logger.debug(f"Appium server is already running on port {port}")
        return True

    current_log_file = get_daily_log_file(port=str(port))
    logger.debug(f"Attempting to start Appium server on port {port}. Logging to: {current_log_file}")

    appium_executable = "appium"
    # 只在 Windows 系统上执行此操作
    if platform.system() == "Windows":
        try:
            npm_prefix = subprocess.check_output("npm config get prefix", shell=True).decode().strip()
            appium_executable = os.path.join(npm_prefix, "appium.cmd")
        except subprocess.CalledProcessError as e:
            logger.error(f"Error getting npm prefix: {e}")
            return False

    # 2. 构建 Appium 启动命令
    # 确保 'appium' 命令在你的系统 PATH 中
    appium_command = [
        appium_executable,
        "--address", "0.0.0.0",
        "--port", str(port),
        "--base-path", "",
        "--log-level", "info",
        "--use-drivers", "uiautomator2"
    ]

    log_fp = None

    try:
        log_fp = open(current_log_file, "a", encoding='utf-8')
        _appium_process = subprocess.Popen(
            appium_command,
            stdout=log_fp,
            stderr=subprocess.STDOUT,
        )
        logger.debug(f"{appium_command}")

        time.sleep(2)

        if _appium_process.poll() is None: # 简单的进程健康检查
            logger.debug(f"Appium server started successfully with PID: {_appium_process.pid}. Logs are being written to {current_log_file}")
            atexit.register(stop_server)
            return True #添加成功
        else:
            logger.error(f"Failed to start Appium server. Check the log file '{current_log_file}' for details.")
            _appium_process = None
            return False #启动失败
    except FileNotFoundError:
        logger.error("Error: 'appium' command not found.")
        logger.error("Please ensure Appium is installed globally via npm (`npm install -g appium`) and that its location is in your system's PATH.")
        if log_fp:
            log_fp.close()
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while starting Appium server: {e}")
        if log_fp:
            log_fp.close()
        return False

def stop_server():
    global _appium_process
    if _appium_process and _appium_process.poll() is None:
        logger.debug(f"Stopping Appium server with PID: {_appium_process.pid}...")
        _appium_process.terminate()  # 发送终止信号 (SIGTERM)
        try:
            _appium_process.wait(timeout=10) # 等待最多10秒
            logger.debug("Appium server stopped gracefully.")
        except subprocess.TimeoutExpired:
            logger.warning("Appium server did not respond to terminate signal, forcing kill.")
            _appium_process.kill() # 强制杀死 (SIGKILL)
            logger.debug("Appium server killed.")
    _appium_process = None

import threading
def monitor_appium(port):
    while True:
        time.sleep(5)  # 每隔 5 秒检查一次
        if not is_port_in_use(port):
            logger.warning("Appium server is not running, attempting to start...")
            start_server(log_level="DEBUG",port=port)