import time
import random
import os
import subprocess
import json
import requests
import re
from datetime import datetime
from urllib.parse import urlparse

from ..utils import logger, glv, CommonMobileUtils
from ..config.phone_model_config import get_phone_config, XHS_ALBUM_CONFIG


class XhsPublishLogic:
    """小红书笔记发布逻辑类"""
    
    def __init__(self, custom_udid, phone_model, domain=None):
        self.domain = domain
        self.custom_udid = custom_udid
        self.phone_model = phone_model
        self.device_session = None
        self.phone_config = get_phone_config(phone_model)
        logger.info(f"设备 {custom_udid} 型号: {phone_model}, 使用配置: {self.phone_config}")
        
    def init(self, device_session):
        """初始化设备会话"""
        self.device_session = device_session
        
    def ensure_app_ready(self):
        """确保小红书应用状态正常"""
        try:
            # 激活小红书应用
            self.device_session.activate_app("com.xingin.xhs")
            time.sleep(3)

            # 检查是否在首页，如果不在则点击首页
            home_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du5")
            if home_tabs:
                home_tab = home_tabs[0]
                is_selected = home_tab.get_attribute("selected") == "true"
                if not is_selected:
                    home_tab.click()
                    time.sleep(2)

        except Exception as e:
            logger.error(f"确保应用状态失败: {e}")

    def clean_device_media(self):
        """清理设备相册中的所有图片和视频"""
        try:
            logger.info("开始清理设备相册...")

            # 清理常见的相册目录
            media_dirs = [
                "/sdcard/DCIM/Camera/",
                "/sdcard/Pictures/",
                "/sdcard/Download/",
                "/sdcard/Movies/",
                "/sdcard/"
            ]

            for media_dir in media_dirs:
                try:
                    # 删除图片文件
                    subprocess.run([
                        'adb', '-s', self.custom_udid, 'shell',
                        f'find {media_dir} -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.bmp" | xargs rm -f'
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)

                    # 删除视频文件
                    subprocess.run([
                        'adb', '-s', self.custom_udid, 'shell',
                        f'find {media_dir} -name "*.mp4" -o -name "*.avi" -o -name "*.mov" -o -name "*.mkv" -o -name "*.flv" -o -name "*.wmv" | xargs rm -f'
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)

                except Exception as e:
                    logger.warning(f"清理目录 {media_dir} 失败: {e}")

            # 刷新媒体库
            try:
                subprocess.run([
                    'adb', '-s', self.custom_udid, 'shell',
                    'am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard'
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)
            except Exception as e:
                logger.warning(f"刷新媒体库失败: {e}")

            logger.info("设备相册清理完成")

        except Exception as e:
            logger.error(f"清理设备相册失败: {e}")

    def check_fabu_config(self, account_number):
        """检查是否有符合条件的发布内容"""
        try:
            logger.info(f"检查账号 {account_number} 的待发布内容...")
            time.sleep(random.randint(5, 10))

            url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
            headers = {"Content-Type": "application/json"}
            data = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "jzzhnrff",
                "pageSize": 1000,
                "pageIndex": 1,
                "controls": [],
                "filters": [
                    {"controlId": "release_status", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "未发布"},
                    {"controlId": "channel_type", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "小红书"}
                ]
            }

            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                res = response.json()
                if res.get("success") and "rows" in res.get("data", {}):
                    rows = res["data"]["rows"]
                    for row in rows:
                        account_data_str = row.get("account", "")
                        release_time = row.get("release_time")

                        if account_data_str:
                            account_data = json.loads(account_data_str)
                            if isinstance(account_data, list):
                                for account_item in account_data:
                                    source_value = json.loads(account_item.get("sourcevalue", "{}"))
                                    account_in_data = source_value.get("66d7fffe98435d4ec600ca08", "")
                                    if account_in_data.strip().lower() == account_number.strip().lower():
                                        release_time_obj = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
                                        if datetime.now() >= release_time_obj:
                                            logger.info(f"找到待发布内容: {row.get('title', '无标题')}")
                                            return row  # 直接返回符合条件的发布记录

            logger.info("没有找到符合条件的待发布内容")
            return None

        except Exception as e:
            logger.error(f"检查发布配置失败: {e}")
            return None

    def clean_filename(self, filename):
        """清理文件名中的非法字符"""
        return re.sub(r'[\\/*?:"<>|]', "_", filename)

    def get_filename_from_url(self, url):
        """从URL中提取文件名"""
        parsed_url = urlparse(url)
        file_name = os.path.basename(parsed_url.path)
        return file_name.split('?')[0]

    def get_media_type(self, file_name):
        """根据文件扩展名判断文件类型"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']

        _, ext = os.path.splitext(file_name.lower())

        if ext in image_extensions:
            return 'image'
        elif ext in video_extensions:
            return 'video'
        else:
            return 'unknown'

    def download_media(self, item):
        """下载媒体文件并推送到设备"""
        media_types = []

        try:
            tp_sp = item.get("tp_sp", [])
            if isinstance(tp_sp, str):
                tp_sp = json.loads(tp_sp)

            if not isinstance(tp_sp, list) or not tp_sp:
                logger.warning("未找到有效的tp_sp数据")
                return None

            for media_info in tp_sp:
                download_url = media_info.get("DownloadUrl")
                if not download_url:
                    logger.warning("未找到有效的文件下载URL")
                    continue

                logger.info(f"下载文件: {download_url}")

                try:
                    response = requests.get(download_url, stream=True, timeout=30)
                    if response.status_code != 200:
                        logger.error(f"文件下载失败，状态码: {response.status_code}")
                        continue

                    # 获取清理后的文件名
                    file_name = self.get_filename_from_url(download_url)
                    file_name = self.clean_filename(file_name)

                    # 推送文件到设备
                    remote_file_path = f"/sdcard/{file_name}"

                    # 创建临时文件
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                temp_file.write(chunk)
                        temp_file_path = temp_file.name

                    # 推送到设备
                    result = subprocess.run([
                        'adb', '-s', self.custom_udid, 'push', temp_file_path, remote_file_path
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=60)

                    # 删除临时文件
                    os.unlink(temp_file_path)

                    if result.returncode == 0:
                        logger.info(f"文件已推送到设备: {remote_file_path}")

                        # 刷新媒体库
                        subprocess.run([
                            'adb', '-s', self.custom_udid, 'shell',
                            f'am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{remote_file_path}'
                        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)

                        media_type = self.get_media_type(file_name)
                        media_types.append(media_type)
                    else:
                        logger.error(f"推送文件失败: {result.stderr}")

                except Exception as e:
                    logger.error(f"处理下载URL {download_url} 时发生错误: {e}")
                    continue

            # 返回文件类型
            if 'video' in media_types:
                return 'video'
            elif 'image' in media_types:
                return 'image'
            else:
                return 'unknown'

        except Exception as e:
            logger.error(f"下载媒体文件失败: {e}")
            return None

    def click_publish_button(self):
        """点击发布按钮"""
        try:
            self.device_session.back()
            publish_button = self.device_session.find_by_id("com.xingin.xhs:id/alx")
            publish_button.click()
            logger.info("发布按钮已点击")
            return True
        except Exception as e:
            logger.error(f"点击发布按钮失败: {e}")
            return False

    def click_fabu_follow(self, media_type):
        """点击发布后续流程"""
        try:
            # 获取所有符合条件的元素
            elements = self.device_session.find_all_by_id("com.xingin.xhs:id/dr9")

            if media_type == "video":
                video_button = self.device_session.find_all_by_id("com.xingin.xhs:id/ig4")[0]
                video_button.click()
                shipin_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/dr9")
                shipin_elements[0].click()
            else:
                # 循环点击每一个元素
                for element in elements:
                    element.click()

            # 点击第一个图片/视频
            first_media = self.device_session.find_all_by_id("com.xingin.xhs:id/ffv")[0]
            first_media.click()

            # 点击下一步
            next_button = self.device_session.find_by_id("com.xingin.xhs:id/a_v")
            next_button.click()

            time.sleep(random.randint(10, 30))

            # 点击视频或者图片下一步
            next_button2 = self.device_session.find_by_id("com.xingin.xhs:id/aqe")
            next_button2.click()

            return True

        except Exception as e:
            logger.error(f"点击发布后续流程失败: {e}")
            return False

    def extract_and_publish(self, data):
        """提取数据并填写发布内容"""
        try:
            # 提取标题和正文
            title = data.get("title", "默认标题")
            zhengwen = data.get("zhengwen", "默认正文")
            topics = data.get("topic_word", "")
            topic_list = topics.split(",") if topics else []

            # 输入标题
            title_input = self.device_session.find_by_id("com.xingin.xhs:id/c3j")
            title_input.input(title)

            # 输入正文
            logger.info(f"输入正文: {zhengwen}")
            zhengwen_input = self.device_session.find_by_id("com.xingin.xhs:id/gqy")
            zhengwen_input.click()
            zhengwen_input.input(zhengwen)

            # 处理话题词
            if topic_list:
                zhengwen_input.input(" #", append=True)

                # 拼接话题词
                topic_text_for_clipboard = " ".join([
                    topic.strip() if idx == 0 else f"#{topic.strip()}"
                    for idx, topic in enumerate(topic_list)
                ])

                # 设置剪切板内容
                self.device_session.set_clipboard_text(topic_text_for_clipboard)
                logger.info(f"话题词已复制到剪切板: {topic_text_for_clipboard}")
            else:
                logger.info("没有话题词，跳过输入#")

            return True

        except Exception as e:
            logger.error(f"提取和发布内容失败: {e}")
            return False

    def click_photo(self):
        """点击相册按钮"""
        try:
            photo_element = self.device_session.find_by_text("相册")
            if photo_element:
                photo_element.click()
                return True
            else:
                logger.warning("未找到相册按钮")
                return False
        except Exception as e:
            logger.error(f"点击相册按钮失败: {e}")
            return False

    def click_allow(self):
        """点击允许按钮"""
        try:
            allow_element = self.device_session.find_by_text("允许")
            if allow_element:
                allow_element.click()
                return True
            else:
                logger.info("未找到允许按钮")
                return True  # 可能已经有权限了
        except Exception as e:
            logger.warning(f"点击允许按钮失败: {e}")
            return True  # 继续执行

    def click_refresh(self):
        """点击刷新按钮"""
        try:
            refresh_element = self.device_session.find_by_text("刷新")
            if refresh_element:
                refresh_element.click()
                return True
            else:
                logger.info("未找到刷新按钮")
                return True
        except Exception as e:
            logger.warning(f"点击刷新按钮失败: {e}")
            return True

    def verify_release(self):
        """验证发布是否成功"""
        try:
            time.sleep(1)
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/aql" in page_source or "com.xingin.xhs:id/apg" in page_source:
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"验证发布状态失败: {e}")
            return False

    def update_mingdao_status(self, item, status="已发布"):
        """更新明道云状态"""
        try:
            url = "https://api.mingdao.com/v2/open/worksheet/editRows"
            headers = {"Content-Type": "application/json"}

            row_id = item.get("rowid")
            if not row_id:
                logger.error("未找到有效的rowid数据")
                return False

            data = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "jzzhnrff",
                "rows": [
                    {
                        "rowid": row_id,
                        "controls": [
                            {
                                "controlId": "release_status",
                                "value": status
                            }
                        ]
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)
            if response.status_code == 200:
                logger.info(f"明道云状态更新成功: {status}")
                return True
            else:
                logger.error(f"明道云状态更新失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"更新明道云状态失败: {e}")
            return False

    def run_publish_flow(self, account_number):
        """运行完整的发布流程"""
        try:
            logger.info(f"🚀 开始执行发布流程，账号: {account_number}")

            # 1. 清理设备相册
            self.clean_device_media()

            # 2. 检查是否有待发布内容
            publish_data = self.check_fabu_config(account_number)
            if not publish_data:
                logger.info("没有待发布内容，跳过发布流程")
                return True

            # 3. 下载媒体文件
            media_type = self.download_media(publish_data)
            if not media_type or media_type == 'unknown':
                logger.error("下载媒体文件失败")
                return False

            logger.info(f"媒体类型: {media_type}")

            # 4. 确保应用状态正常
            self.ensure_app_ready()

            # 5. 点击发布按钮
            if not self.click_publish_button():
                logger.error("点击发布按钮失败")
                return False

            time.sleep(2)

            # 6. 处理权限和相册
            self.click_allow()
            time.sleep(1)
            self.click_photo()
            time.sleep(2)
            self.click_refresh()
            time.sleep(3)

            # 7. 选择媒体文件
            if not self.click_fabu_follow(media_type):
                logger.error("选择媒体文件失败")
                return False

            time.sleep(3)

            # 8. 填写发布内容
            if not self.extract_and_publish(publish_data):
                logger.error("填写发布内容失败")
                return False

            time.sleep(2)

            # 9. 最终发布
            publish_final_button = self.device_session.find_by_id("com.xingin.xhs:id/alv")
            if publish_final_button:
                publish_final_button.click()
                logger.info("点击最终发布按钮")
            else:
                logger.error("未找到最终发布按钮")
                return False

            # 10. 等待发布完成并验证
            time.sleep(10)
            if self.verify_release():
                logger.info("✅ 发布成功！")
                # 更新明道云状态
                self.update_mingdao_status(publish_data, "已发布")

                # 11. 清理设备相册
                self.clean_device_media()
                return True
            else:
                logger.error("❌ 发布失败")
                return False

        except Exception as e:
            logger.error(f"发布流程执行失败: {e}")
            return False
        finally:
            # 确保最后清理相册
            try:
                self.clean_device_media()
            except:
                pass
            
    def push_image_to_device(self, local_image_path, device_path="/sdcard/DCIM/Camera/"):
        """将图片推送到设备相册目录"""
        try:
            # 生成唯一的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(local_image_path)[1]
            device_filename = f"IMG_{timestamp}{file_extension}"
            full_device_path = f"{device_path}{device_filename}"
            
            # 推送文件到设备
            result = subprocess.run(
                ['adb', '-s', self.custom_udid, 'push', local_image_path, full_device_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info(f"图片推送成功: {full_device_path}")
                
                # 刷新媒体库，让相册能识别新图片
                self.device_session.execute_script("mobile: shell", {
                    "command": f"am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{full_device_path}"
                })
                
                return full_device_path
            else:
                logger.error(f"图片推送失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"推送图片到设备失败: {e}")
            return None
            
    def start_publish_flow(self):
        """开始发布流程"""
        try:
            # 确保应用状态正常
            self.ensure_app_ready()
            
            # 点击发布按钮（加号）
            publish_button = self.device_session.find_by_id("com.xingin.xhs:id/du7")
            if publish_button:
                publish_button.click()
                time.sleep(2)
                logger.info("点击发布按钮成功")
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"开始发布流程失败: {e}")
            return False
            
    def select_images_from_album(self, image_count=1):
        """从相册选择图片（通用方法）"""
        try:
            # 等待相册界面加载
            time.sleep(3)
            
            # 查找图片元素（通常是ImageView或类似的容器）
            # 这里使用通用的查找方法，适配不同手机
            image_elements = []
            
            # 方法1：通过常见的图片容器ID查找
            common_image_ids = [
                "com.xingin.xhs:id/image",
                "com.xingin.xhs:id/photo",
                "com.xingin.xhs:id/thumbnail",
                "com.xingin.xhs:id/item_image"
            ]
            
            for image_id in common_image_ids:
                elements = self.device_session.find_all_by_id(image_id)
                if elements:
                    image_elements = elements
                    logger.info(f"通过ID {image_id} 找到 {len(elements)} 个图片")
                    break
            
            # 方法2：如果通过ID找不到，尝试通过class查找
            if not image_elements:
                elements = self.device_session.find_all_by_class_name("android.widget.ImageView")
                # 过滤掉太小的ImageView（可能是图标）
                image_elements = [elem for elem in elements if self._is_valid_image_element(elem)]
                logger.info(f"通过class找到 {len(image_elements)} 个有效图片")
            
            # 选择指定数量的图片
            selected_count = 0
            for i, image_elem in enumerate(image_elements[:image_count]):
                try:
                    image_elem.click()
                    selected_count += 1
                    logger.info(f"选择第 {selected_count} 张图片")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"选择第 {i+1} 张图片失败: {e}")
                    
            if selected_count > 0:
                # 点击确认/下一步按钮
                self._click_confirm_button()
                return True
            else:
                logger.error("未能选择任何图片")
                return False
                
        except Exception as e:
            logger.error(f"从相册选择图片失败: {e}")
            return False
            
    def _is_valid_image_element(self, element):
        """判断是否是有效的图片元素"""
        try:
            # 获取元素的bounds
            bounds = element.get_attribute("bounds")
            if bounds:
                # 解析bounds字符串，例如: "[0,0][100,100]"
                import re
                match = re.search(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    width = x2 - x1
                    height = y2 - y1
                    # 过滤掉太小的元素（可能是图标）
                    return width > 50 and height > 50
            return True
        except:
            return True
            
    def _click_confirm_button(self):
        """点击确认/下一步按钮"""
        try:
            # 常见的确认按钮文本
            confirm_texts = ["确定", "下一步", "完成", "OK", "Next", "Done"]
            
            for text in confirm_texts:
                elements = self.device_session.find_all_by_xpath(f'//*[@text="{text}"]')
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮: {text}")
                    time.sleep(2)
                    return True
                    
            # 如果找不到文本按钮，尝试通过ID查找
            confirm_ids = [
                "com.xingin.xhs:id/confirm",
                "com.xingin.xhs:id/next",
                "com.xingin.xhs:id/done",
                "android:id/button1"
            ]
            
            for confirm_id in confirm_ids:
                elements = self.device_session.find_all_by_id(confirm_id)
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮ID: {confirm_id}")
                    time.sleep(2)
                    return True
                    
            logger.warning("未找到确认按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击确认按钮失败: {e}")
            return False
            
    def add_title_and_content(self, title, content):
        """添加标题和内容"""
        try:
            # 等待编辑界面加载
            time.sleep(3)
            
            # 查找标题输入框
            title_input = self.device_session.find_by_id("com.xingin.xhs:id/title_edit")
            if title_input:
                title_input.clear()
                title_input.send_keys(title)
                logger.info(f"输入标题: {title}")
            
            # 查找内容输入框
            content_input = self.device_session.find_by_id("com.xingin.xhs:id/content_edit")
            if content_input:
                content_input.clear()
                content_input.send_keys(content)
                logger.info(f"输入内容: {content[:50]}...")
                
            return True
            
        except Exception as e:
            logger.error(f"添加标题和内容失败: {e}")
            return False
            
    def publish_note(self):
        """发布笔记"""
        try:
            # 查找发布按钮
            publish_button = self.device_session.find_by_xpath('//*[@text="发布"]')
            if not publish_button:
                publish_button = self.device_session.find_by_id("com.xingin.xhs:id/publish")
                
            if publish_button:
                publish_button.click()
                logger.info("点击发布按钮")
                time.sleep(5)  # 等待发布完成
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"发布笔记失败: {e}")
            return False
            
    def publish_complete_flow(self, image_paths, title, content):
        """完整的发布流程"""
        try:
            logger.info(f"开始发布笔记: {title}")
            
            # 1. 推送图片到设备
            pushed_images = []
            for image_path in image_paths:
                device_path = self.push_image_to_device(image_path)
                if device_path:
                    pushed_images.append(device_path)
                    
            if not pushed_images:
                logger.error("没有成功推送任何图片")
                return False
                
            # 2. 开始发布流程
            if not self.start_publish_flow():
                return False
                
            # 3. 选择图片
            if not self.select_images_from_album(len(pushed_images)):
                return False
                
            # 4. 添加标题和内容
            if not self.add_title_and_content(title, content):
                return False
                
            # 5. 发布笔记
            if not self.publish_note():
                return False
                
            logger.info("笔记发布成功！")
            return True
            
        except Exception as e:
            logger.error(f"完整发布流程失败: {e}")
            return False
        finally:
            # 清理推送的图片（可选）
            self._cleanup_pushed_images(pushed_images)
            
    def _cleanup_pushed_images(self, image_paths):
        """清理推送的图片"""
        try:
            for image_path in image_paths:
                subprocess.run(
                    ['adb', '-s', self.custom_udid, 'shell', 'rm', image_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=10
                )
            logger.info("清理推送的图片完成")
        except Exception as e:
            logger.warning(f"清理推送的图片失败: {e}")
