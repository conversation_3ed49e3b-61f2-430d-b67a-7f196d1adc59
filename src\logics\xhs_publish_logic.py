import time
import random
import os
import subprocess
from datetime import datetime

from ..utils import logger, glv, CommonMobileUtils
from ..config.phone_model_config import get_phone_config, XHS_ALBUM_CONFIG


class XhsPublishLogic:
    """小红书笔记发布逻辑类"""
    
    def __init__(self, custom_udid, phone_model, domain=None):
        self.domain = domain
        self.custom_udid = custom_udid
        self.phone_model = phone_model
        self.device_session = None
        self.phone_config = get_phone_config(phone_model)
        logger.info(f"设备 {custom_udid} 型号: {phone_model}, 使用配置: {self.phone_config}")
        
    def init(self, device_session):
        """初始化设备会话"""
        self.device_session = device_session
        
    def ensure_app_ready(self):
        """确保小红书应用状态正常"""
        try:
            # 激活小红书应用
            self.device_session.activate_app("com.xingin.xhs")
            time.sleep(3)
            
            # 检查是否在首页，如果不在则点击首页
            home_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du5")
            if home_tabs:
                home_tab = home_tabs[0]
                is_selected = home_tab.get_attribute("selected") == "true"
                if not is_selected:
                    home_tab.click()
                    time.sleep(2)
                    
        except Exception as e:
            logger.error(f"确保应用状态失败: {e}")
            
    def push_image_to_device(self, local_image_path, device_path="/sdcard/DCIM/Camera/"):
        """将图片推送到设备相册目录"""
        try:
            # 生成唯一的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(local_image_path)[1]
            device_filename = f"IMG_{timestamp}{file_extension}"
            full_device_path = f"{device_path}{device_filename}"
            
            # 推送文件到设备
            result = subprocess.run(
                ['adb', '-s', self.custom_udid, 'push', local_image_path, full_device_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info(f"图片推送成功: {full_device_path}")
                
                # 刷新媒体库，让相册能识别新图片
                self.device_session.execute_script("mobile: shell", {
                    "command": f"am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{full_device_path}"
                })
                
                return full_device_path
            else:
                logger.error(f"图片推送失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"推送图片到设备失败: {e}")
            return None
            
    def start_publish_flow(self):
        """开始发布流程"""
        try:
            # 确保应用状态正常
            self.ensure_app_ready()
            
            # 点击发布按钮（加号）
            publish_button = self.device_session.find_by_id("com.xingin.xhs:id/du7")
            if publish_button:
                publish_button.click()
                time.sleep(2)
                logger.info("点击发布按钮成功")
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"开始发布流程失败: {e}")
            return False
            
    def select_images_from_album(self, image_count=1):
        """从相册选择图片（通用方法）"""
        try:
            # 等待相册界面加载
            time.sleep(3)
            
            # 查找图片元素（通常是ImageView或类似的容器）
            # 这里使用通用的查找方法，适配不同手机
            image_elements = []
            
            # 方法1：通过常见的图片容器ID查找
            common_image_ids = [
                "com.xingin.xhs:id/image",
                "com.xingin.xhs:id/photo",
                "com.xingin.xhs:id/thumbnail",
                "com.xingin.xhs:id/item_image"
            ]
            
            for image_id in common_image_ids:
                elements = self.device_session.find_all_by_id(image_id)
                if elements:
                    image_elements = elements
                    logger.info(f"通过ID {image_id} 找到 {len(elements)} 个图片")
                    break
            
            # 方法2：如果通过ID找不到，尝试通过class查找
            if not image_elements:
                elements = self.device_session.find_all_by_class_name("android.widget.ImageView")
                # 过滤掉太小的ImageView（可能是图标）
                image_elements = [elem for elem in elements if self._is_valid_image_element(elem)]
                logger.info(f"通过class找到 {len(image_elements)} 个有效图片")
            
            # 选择指定数量的图片
            selected_count = 0
            for i, image_elem in enumerate(image_elements[:image_count]):
                try:
                    image_elem.click()
                    selected_count += 1
                    logger.info(f"选择第 {selected_count} 张图片")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"选择第 {i+1} 张图片失败: {e}")
                    
            if selected_count > 0:
                # 点击确认/下一步按钮
                self._click_confirm_button()
                return True
            else:
                logger.error("未能选择任何图片")
                return False
                
        except Exception as e:
            logger.error(f"从相册选择图片失败: {e}")
            return False
            
    def _is_valid_image_element(self, element):
        """判断是否是有效的图片元素"""
        try:
            # 获取元素的bounds
            bounds = element.get_attribute("bounds")
            if bounds:
                # 解析bounds字符串，例如: "[0,0][100,100]"
                import re
                match = re.search(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    width = x2 - x1
                    height = y2 - y1
                    # 过滤掉太小的元素（可能是图标）
                    return width > 50 and height > 50
            return True
        except:
            return True
            
    def _click_confirm_button(self):
        """点击确认/下一步按钮"""
        try:
            # 常见的确认按钮文本
            confirm_texts = ["确定", "下一步", "完成", "OK", "Next", "Done"]
            
            for text in confirm_texts:
                elements = self.device_session.find_all_by_xpath(f'//*[@text="{text}"]')
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮: {text}")
                    time.sleep(2)
                    return True
                    
            # 如果找不到文本按钮，尝试通过ID查找
            confirm_ids = [
                "com.xingin.xhs:id/confirm",
                "com.xingin.xhs:id/next",
                "com.xingin.xhs:id/done",
                "android:id/button1"
            ]
            
            for confirm_id in confirm_ids:
                elements = self.device_session.find_all_by_id(confirm_id)
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮ID: {confirm_id}")
                    time.sleep(2)
                    return True
                    
            logger.warning("未找到确认按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击确认按钮失败: {e}")
            return False
            
    def add_title_and_content(self, title, content):
        """添加标题和内容"""
        try:
            # 等待编辑界面加载
            time.sleep(3)
            
            # 查找标题输入框
            title_input = self.device_session.find_by_id("com.xingin.xhs:id/title_edit")
            if title_input:
                title_input.clear()
                title_input.send_keys(title)
                logger.info(f"输入标题: {title}")
            
            # 查找内容输入框
            content_input = self.device_session.find_by_id("com.xingin.xhs:id/content_edit")
            if content_input:
                content_input.clear()
                content_input.send_keys(content)
                logger.info(f"输入内容: {content[:50]}...")
                
            return True
            
        except Exception as e:
            logger.error(f"添加标题和内容失败: {e}")
            return False
            
    def publish_note(self):
        """发布笔记"""
        try:
            # 查找发布按钮
            publish_button = self.device_session.find_by_xpath('//*[@text="发布"]')
            if not publish_button:
                publish_button = self.device_session.find_by_id("com.xingin.xhs:id/publish")
                
            if publish_button:
                publish_button.click()
                logger.info("点击发布按钮")
                time.sleep(5)  # 等待发布完成
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"发布笔记失败: {e}")
            return False
            
    def publish_complete_flow(self, image_paths, title, content):
        """完整的发布流程"""
        try:
            logger.info(f"开始发布笔记: {title}")
            
            # 1. 推送图片到设备
            pushed_images = []
            for image_path in image_paths:
                device_path = self.push_image_to_device(image_path)
                if device_path:
                    pushed_images.append(device_path)
                    
            if not pushed_images:
                logger.error("没有成功推送任何图片")
                return False
                
            # 2. 开始发布流程
            if not self.start_publish_flow():
                return False
                
            # 3. 选择图片
            if not self.select_images_from_album(len(pushed_images)):
                return False
                
            # 4. 添加标题和内容
            if not self.add_title_and_content(title, content):
                return False
                
            # 5. 发布笔记
            if not self.publish_note():
                return False
                
            logger.info("笔记发布成功！")
            return True
            
        except Exception as e:
            logger.error(f"完整发布流程失败: {e}")
            return False
        finally:
            # 清理推送的图片（可选）
            self._cleanup_pushed_images(pushed_images)
            
    def _cleanup_pushed_images(self, image_paths):
        """清理推送的图片"""
        try:
            for image_path in image_paths:
                subprocess.run(
                    ['adb', '-s', self.custom_udid, 'shell', 'rm', image_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=10
                )
            logger.info("清理推送的图片完成")
        except Exception as e:
            logger.warning(f"清理推送的图片失败: {e}")
