
from flask_apscheduler import APScheduler

scheduler = APScheduler()

def init_scheduler(app):
    """初始化定时任务"""
    from .services.task_service import append_device_tasks,update_run_config

    scheduler.add_job(
        id='device_discovery',
        func=append_device_tasks,
        trigger='interval',
        seconds=2,
        args=[app]
    )
    if app.config.get('ISUPDATE_RUNCONFIG', False):
        scheduler.add_job(
            id='update_run_config',
            func=update_run_config,
            trigger='interval',
            seconds=60,
            args=[app]
        )
