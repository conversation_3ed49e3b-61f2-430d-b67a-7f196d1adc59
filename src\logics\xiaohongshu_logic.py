import time
import requests
import random
import json
from urllib.parse import quote
import urllib.parse
from lxml import etree

from ..utils import logger,glv,CommonMobileUtils
from ..services.tool_service import ToolService
from .xhs_comment_logic import XhsCommentLogic
from .xhs_follow_logic import XhsFollowLogic

class XiaohongshuLogic:

    def __init__(self, custom_udid, domain):
        self.domain = domain
        self.custom_udid = custom_udid
        self.device_session = None
        self.wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
        self.comment_service = None
        self.follow_service = None
        ToolService.build_common_var(self.custom_udid, "xhs_last_run_timestamp", 0)

    def init(self, device_session):
        self.device_session = device_session
        # 初始化评论和关注服务
        self.comment_service = XhsCommentLogic(self.custom_udid, device_session)
        self.follow_service = XhsFollowLogic(self.custom_udid, device_session)

    def collect_latest_messages(self, message_count):
        message_els = self.device_session.find_all_by_id("com.xingin.xhs:id/ayc")
        total = len(message_els)
        logger.info(f"共找到 {total} 条聊天内容，准备取最后 {message_count} 条")
        selected = message_els[-message_count:] if message_count <= total else message_els

        messages = []
        for i, el in enumerate(selected):
            try:
                msg = el.get_attribute("text")
                logger.info(f"[最新消息 {i+1}] {msg}")
                messages.append(msg)
            except Exception as e:
                logger.info(f"[最新消息 {i+1}] 获取失败: {e}")
        return messages

    def send_to_wechat_group(self, webhook_url: str, message: str, userid: str = None):
        # 如果有 userid，前面加 @
        if userid:
            content = f"<@{userid}>\n{message}"
        else:
            content = message

        payload = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

        try:
            response = requests.post(webhook_url, json=payload)
            if response.status_code == 200:
                logger.info("✅ 企业微信通知发送成功")
            else:
                logger.info(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
        except Exception as e:
            logger.info(f"❌ 发送企业微信通知时出错：{e}")

    def fetch_qw_phone(self, monitor_xhs_number: str) -> str:
        url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
        payload = {
            "appKey": "f08bf7f7cfe8c038",
            "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
            "worksheetId": "account_config",
            "filters": [
                {
                    "controlId": "id",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 1,
                    "value": monitor_xhs_number
                }
            ]
        }

        headers = {"Content-Type": "application/json"}
        try:
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            data = response.json()
            if data.get("success") and data.get("data", {}).get("rows"):
                row = data["data"]["rows"][0]
                return row.get("qw_phone", "")  # 只返回手机号
        except Exception as e:
            logger.info(f"获取企业微信手机号失败：{e}")
        return ""

    # 全局缓存
    wechat_token_cache = {
        "access_token": None,
        "expires_at": 0
    }

    def get_userid_by_mobile_from_corp(self, corpid: str, corpsecret: str, mobile: str) -> str:
        def get_access_token(corpid: str, corpsecret: str) -> str:
            now = time.time()
            if self.wechat_token_cache["access_token"] and now < self.wechat_token_cache["expires_at"] - 100:
                return self.wechat_token_cache["access_token"]

            url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
            response = requests.get(url)
            data = response.json()

            if data.get("errcode") == 0:
                access_token = data["access_token"]
                expires_in = data["expires_in"]
                self.wechat_token_cache["access_token"] = access_token
                self.wechat_token_cache["expires_at"] = now + expires_in
                logger.info("✅ 成功获取 access_token")
                return access_token
            else:
                raise Exception(f"❌ 获取 access_token 失败: {data}")

        def get_userid_by_mobile(access_token: str, mobile: str) -> str:
            url = f"https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token={access_token}"
            payload = {"mobile": mobile}
            response = requests.post(url, json=payload)
            data = response.json()

            if data.get("errcode") == 0:
                logger.info(f"✅ 成功获取 userid：{data['userid']}")
                return data["userid"]
            else:
                raise Exception(f"❌ 获取 userid 失败: {data}")

        access_token = get_access_token(corpid, corpsecret)
        return get_userid_by_mobile(access_token, mobile)
    
    def format_wecom_message(self, monitor_nickname, user_nickname, user_xhs_id, messages):
        """
        构造企业微信通知格式内容（类型固定为"私信"），新版格式：
        A -> 我，B -> user_nickname
        """
        message_text = "发现新的用户行为：\n"
        message_text += "类型：私信\n"
        message_text += f"监控账号昵称：{monitor_nickname}\n"
        message_text += f"用户昵称：{user_nickname}\n"
        message_text += f"用户小红书号：{user_xhs_id}\n"
        message_text += "====内容开始====\n"

        if isinstance(messages, list):
            for msg in messages:
                sender_label = "我" if msg.get("sender") == "self" else user_nickname
                message_text += f"{sender_label}: {msg.get('text')}\n"
        else:
            message_text += f"我: {messages}\n"

        message_text += "====内容结束===="
        return message_text.strip()

    def collect_all_messages(self):
        xml_str = self.device_session.get_page_source()
        root = etree.fromstring(xml_str.encode('utf-8'))

        # 找所有的消息 TextView (ayc)
        ayc_nodes = root.xpath('//*[@resource-id="com.xingin.xhs:id/ayc"]')
        logger.info(f"共找到 {len(ayc_nodes)} 条消息")

        messages = []
        for i, ayc_node in enumerate(ayc_nodes):
            msg_text = ayc_node.attrib.get('text')

            # 往上找父节点，找到对应的 ViewGroup (kim)
            kim_node = ayc_node.getparent().getparent().getparent().getparent().getparent()

            # 在 kim 节点下找头像 hgt
            # hgt_node = kim_node.xpath('.//*[@resource-id="com.xingin.xhs:id/hgt"]')[0]
            # bounds = hgt_node.attrib.get('bounds')
            hgt_nodes = kim_node.xpath('.//*[@resource-id="com.xingin.xhs:id/hgt"]')
            if not hgt_nodes:
                logger.warning(f"[消息 {i+1}] 未找到头像节点，跳过")
                continue
            bounds = hgt_nodes[0].attrib.get('bounds')
            avatar_x1 = int(bounds.split('[')[1].split(',')[0])

            sender = 'user' if avatar_x1 < 200 else 'self'
            logger.info(f"[消息 {i+1}] {sender}: {msg_text}")

            messages.append({
                "sender": sender,
                "text": msg_text
            })

        return messages

    def handle_private_message(self, monitor_nickname, monitor_xhs_number, username, webhook_url, msg_count=None):
        try:
            user_nickname, user_xhs_number = self.get_xhs_user_info()
            messages = self.collect_all_messages()

            # 取最后一条用户发送的消息入库
            private_content = ""
            for msg in reversed(messages):
                if msg["sender"] == "user":
                    private_content = msg["text"]
                    break

            payload = {
                "monitorXhsNumber": monitor_xhs_number,
                "monitorUsername": monitor_nickname or "",
                "userXhsNumber": user_xhs_number,
                "userUsername": user_nickname,
                "actionType": "private",
                "messageContent": private_content,
                "status": 0
            }
            try:
                response = requests.post("https://api.open.hctalent.cn/channel/xhsUserActions/save", json=payload)
                logger.info(f"✅ 入库成功：{response.status_code} - {response.text}")
            except Exception as e:
                logger.info(f"❌ 入库失败：{e}")

            # 企业微信通知
            wecom_text = self.format_wecom_message(
                monitor_nickname=monitor_nickname,
                user_nickname=user_nickname or username,
                user_xhs_id=user_xhs_number,
                messages=messages
            )

            reply_url = f"https://api.open.hctalent.cn/reply-form/private_reply.html?user={urllib.parse.quote(user_xhs_number)}&content={urllib.parse.quote(private_content)}"
            wecom_text += f"\n[点击回复这条私信]({reply_url})\n"

            # mentioned_phone = self.fetch_qw_phone(monitor_xhs_number)
            # qywx_userid = self.get_userid_by_mobile_from_corp(
            #     "ww066d99826a459f4b",
            #     "HJAyqAQ5Eg1CLktLCzG1CF8ddrmCaHQ1gyBVX-DN9Z0",
            #     mentioned_phone
            # )
            qywx_userid = self.fetch_qw_phone(monitor_xhs_number)
            self.send_to_wechat_group(webhook_url, wecom_text, qywx_userid)
        except Exception as e:
            logger.info(f"❌ 通用消息处理失败：{e}")

    def process_private_message_list(self, monitor_nickname, monitor_xhs_number, webhook_url):
        # 1. 获取所有消息条目
        message_items = self.device_session.find_all_by_id("com.xingin.xhs:id/bgf")
        logger.info(f"共找到 {len(message_items)} 个消息条目")

        for idx, item in enumerate(message_items):
            try:
                # 2. 查找用户名
                try:
                    username_el = item.find_element_by_id("com.xingin.xhs:id/fqh")
                    username = username_el.get_attribute("text")
                except Exception:
                    logger.info(f"[跳过] 第{idx}条未找到用户名")
                    continue

                # 3. 查找红点
                try:
                    red_dot_el = item.find_element_by_id("com.xingin.xhs:id/a3u")
                    red_dot_text = red_dot_el.get_attribute("text")
                    if not red_dot_text.strip().isdigit():
                        logger.info(f"[处理无效红点] 第{idx}条红点文本无效：'{red_dot_text}'，点击进入清除")
                        # 红点无效，直接点击进去然后back，清除无效红点
                        username_el.click()
                        time.sleep(1)
                        self.device_session.back()
                        time.sleep(1)
                        continue
                    msg_count = int(red_dot_text)
                except Exception:
                    # 没有红点，跳过
                    continue

                logger.info(f"用户名：{username}，未读数：{msg_count}")

                # 特殊处理：系统消息或活动消息
                if username in ["系统消息", "活动消息"]:
                    logger.info(f"[跳过处理] {username}，只点击返回")
                    username_el.click()
                    time.sleep(1)
                    self.device_session.back()
                    time.sleep(1)
                    continue

                # 点击用户名进入会话
                username_el.click()
                time.sleep(1)

                # 群聊判断逻辑
                try:
                    group_marks = self.device_session.find_all_by_id("com.xingin.xhs:id/ito")
                    if group_marks:
                        group_text = group_marks[0].get_attribute("text") or ""
                        if group_text.startswith("(") and group_text.endswith(")"):
                            logger.info(f"识别为群聊 ({group_text})，跳过处理")
                            self.device_session.back()
                            time.sleep(1)
                            continue
                except Exception as e:
                    logger.error(f"群聊判断异常：{e}")

                self.handle_private_message(
                    monitor_nickname, monitor_xhs_number,
                    username=username,
                    msg_count=msg_count,
                    webhook_url=webhook_url
                )

                self.device_session.back()
                time.sleep(1)
            except Exception as e:
                logger.info(f"[用户 {username}] 处理失败：{e}")

    def get_xhs_user_info(self):
        try:
            nickname_el = self.device_session.find_by_id("com.xingin.xhs:id/it3")
            nickname_text = nickname_el.get_attribute("text")
            nickname_el.click()
            time.sleep(1)

            xhs_id_el = self.device_session.find_by_id("com.xingin.xhs:id/gxr")
            xhs_id_text = xhs_id_el.get_attribute("text").replace("小红书号：", "").strip()

            self.device_session.back()
            time.sleep(1)

            return nickname_text, xhs_id_text
        except Exception as e:
            logger.info(f"[用户主页] 获取失败：{e}")
            return "", ""

    def ensure_app_ready(self):
        """确保应用已启动并处于正常状态"""
        try:
            # 检查页面是否在小红书
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs" not in page_source:
                self.device_session.activate_app("com.xingin.xhs")
                wait_time = random.randint(10, 15)
                logger.info(f"等待 {wait_time} 秒让 App 启动...")
                time.sleep(wait_time)

            # 检查页面是否有稍后再说按钮
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/ali" in page_source:
                self.device_session.find_by_id("com.xingin.xhs:id/ali").click()
                time.sleep(2)

            # 确保回到首页
            self.ensure_homepage()

        except Exception as e:
            logger.error(f"应用初始化失败: {e}")

    def ensure_homepage(self):
        """确保当前在首页"""
        try:
            # 首先检查当前是否已经在首页
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/du5" in page_source:
                logger.info("当前已在首页，无需返回")
                return

            # 最多返回8次，每次都检查是否有首页元素
            for i in range(8):
                # 返回一次
                logger.info(f"第{i+1}次返回操作")
                self.device_session.back()
                time.sleep(2)  # 等待页面加载

                # 检查是否有首页元素
                page_source = self.device_session.get_page_source()
                if "com.xingin.xhs:id/du5" in page_source:
                    logger.info(f"第{i+1}次检查：已找到首页元素")
                    return  # 找到首页元素就退出

            # 8次都没找到首页元素
            logger.warning("返回8次后仍未找到首页元素")

        except Exception as e:
            logger.error(f"回到首页失败: {e}")

    def get_current_account_info(self):
        try:
            print("🔍 获取当前登录账号信息...")
            me_tab = self.device_session.find_by_id("com.xingin.xhs:id/du6")
            me_tab.click()
            time.sleep(2)

            xhs_id_el = self.device_session.find_by_id("com.xingin.xhs:id/gxr")
            xhs_id_text = xhs_id_el.get_attribute("text").replace("小红书号：", "").strip()

            nickname = ""
            try:
                nickname_el = self.device_session.find_by_id("com.xingin.xhs:id/gxp")
                nickname = nickname_el.get_attribute("text")
            except:
                pass

            print("🆔 当前登录账号信息：")
            print(f"账号昵称：{nickname}")
            print(f"小红书号：{xhs_id_text}")
            return nickname, xhs_id_text

        except Exception as e:
            print(f"[我] 获取当前账号信息失败：{e}")
            return "", ""

    def do_share_b_account(self, user_username):
        # 点击我
        me_tab = self.device_session.find_by_id("com.xingin.xhs:id/du6")
        me_tab.click()

        # 点击关注
        follow_tab = self.device_session.find_by_id("com.xingin.xhs:id/y5")
        follow_tab.click()

        # 点击关注列表第一个小助理号
        assistant_els = self.device_session.find_all_by_id("com.xingin.xhs:id/jg6")
        assistant_els[0].click()

        # 点击分享
        more_el = self.device_session.find_by_id("com.xingin.xhs:id/gwc")
        more_el.click()

        # 点击要分享的人
        # xpath = f'//*[@text="{text_value}" and @resource-id="com.xingin.xhs:id/j_8"]'
        xpath = f'//*[@text="{user_username}" and @resource-id="com.xingin.xhs:id/j_8"]'
        user_share_el = self.device_session.find_by_xpath(xpath)
        user_share_el.click()
        time.sleep(1)

        # 点击发送
        send_share_btn = self.device_session.find_by_id("com.xingin.xhs:id/hx6")
        send_share_btn.click()
        time.sleep(1)

        self.device_session.back()
        time.sleep(1)
        self.device_session.back()

    def handle_mobile_pending_reply(self, wechat_webhook_url, monitor_xhs_number):
        BASE_API = "https://api.open.hctalent.cn/channel/reply-form"   
        """
        检查待回复消息（仅处理私信类型），优先尝试在陌生人消息中查找用户并回复，找不到再在普通消息中查找。
        """
        try:
            # 获取待回复记录，仅获取 actionType = "private" 的记录（私信）
            resp = requests.post(
                f"{BASE_API}/pending-replies",
                json={
                    "monitorXhsNumber": monitor_xhs_number,
                    "actionType": "private"
                }
            )
            resp.raise_for_status()
            data = resp.json().get("data")
        except Exception as e:
            print(f"获取待回复记录失败：{e}")
            return

        if not data:
            print("当前无待回复记录")
            return

        # 提取第一条待处理记录
        reply_item = data[0]
        message_content = reply_item.get("messageContent")
        reply_content = reply_item.get("replyContent")
        record_id = reply_item.get("id")
        user_xhs_number = reply_item.get("userXhsNumber")
        share_b_account = reply_item.get("shareBAccount")
        monitor_username = reply_item.get("monitorUsername")
        user_username = reply_item.get("userUsername")

        try:
            print(f"正在准备回复用户：{user_xhs_number}，内容：{reply_content}")

            time.sleep(1)

            # === 1. 判断是否已经在消息页面 ===
            is_in_message_page = False
            try:
                self.device_session.find_by_xpath('//*[@text="消息" and @resource-id="com.xingin.xhs:id/jjd"]')
                is_in_message_page = True
                print("当前已在消息页面")
            except:
                print("不在消息页面，准备点击消息按钮进入")

            # === 2. 如果不在消息页面，点击"消息"按钮进入 ===
            if not is_in_message_page:
                try:
                    msg_btn = self.device_session.find_by_xpath('//*[@content-desc="消息" and @resource-id="com.xingin.xhs:id/du7"]')
                    msg_btn.click()
                    print("已点击消息按钮")
                    time.sleep(2)
                except Exception as e:
                    print("点击消息按钮失败：", e)
                    return

            # === 3. 先尝试在陌生人消息中查找用户 ===
            user_found = False
            try:
                stranger_nodes = self.device_session.find_all_by_xpath('//*[contains(@content-desc, "陌生人消息")]')
                if stranger_nodes:
                    stranger_nodes[0].click()
                    print("已点击进入陌生人消息")
                    time.sleep(2)

                    stranger_xpath = f'//*[@text="{user_username}" and @resource-id="com.xingin.xhs:id/fqh"]'
                    stranger_user_entries = self.device_session.find_all_by_xpath(stranger_xpath)

                    if stranger_user_entries:
                        stranger_user_entries[0].click()
                        user_found = True
                        print(f"已在陌生人消息中找到用户 {user_username} 并进入对话")
                        time.sleep(1)
                    else:
                        print(f"陌生人消息中未找到用户 {user_username}")
                        self.device_session.back()
                        time.sleep(1)
                else:
                    print("当前没有陌生人消息入口，跳过该入口检查")
            except Exception as e:
                print(f"陌生人消息中查找用户 {user_username} 出现异常：{e}")

            # === 4. 如果陌生人消息找不到，再从普通消息列表中查找 ===
            if not user_found:
                max_swipe_attempts = 5  # 最多滑动5次
                swipe_count = 0
                while swipe_count < max_swipe_attempts:
                    user_xpath = f'//*[@text="{user_username}" and @resource-id="com.xingin.xhs:id/fqh"]'
                    user_entries = self.device_session.find_all_by_xpath(user_xpath)

                    if len(user_entries) > 0:
                        user_entries[0].click()
                        print(f"✅ 已点击普通消息用户 {user_username} 的对话框")
                        user_found = True
                        time.sleep(1)
                        break
                    else:
                        print(f"未找到用户 {user_username}，准备向上滑动继续查找（第 {swipe_count+1} 次）")
                        try:
                            self.device_session.swipe(170, 1400, 170, 600, 50)  # 向上滑动一屏
                        except Exception as swipe_e:
                            print(f"滑动失败：{swipe_e}")
                            break
                        time.sleep(1)
                        swipe_count += 1

            if not user_found:
                print(f"❌ 在普通消息列表中滑动多次也未找到用户 {user_username}，更新状态为回复失败")
                # 更新状态为回复失败
                try:
                    update_resp = requests.post(
                        "https://api.open.hctalent.cn/channel/xhsUserActions/update",
                        json={
                            "id": record_id,
                            "isReplied": 3
                        }
                    )
                    update_resp.raise_for_status()
                    print(f"记录 {record_id} 回复失败，状态已更新为3")
                except Exception as e:
                    print(f"更新回复失败状态失败：{e}")
                return

            # === 5. 找到输入框，输入回复内容 ===
            input_box = self.device_session.find_by_id('com.xingin.xhs:id/ayo')
            input_box.input(reply_content)
            time.sleep(0.5)

            # === 6. 点击"发送"按钮 ===
            send_btn = self.device_session.find_by_id('com.xingin.xhs:id/ayx')
            send_btn.click()

            print("回复发送成功")
            self.device_session.back()
            time.sleep(1)

            # 判断是否停留在"陌生人消息"页
            try:
                stranger_tabs = self.device_session.find_all_by_xpath(
                    '//*[@resource-id="com.xingin.xhs:id/jjd" and @text="陌生人消息"]'
                )
                if stranger_tabs:
                    print("⚠️ 当前停留在陌生人消息页面，点击返回主消息页")
                    self.device_session.back()
                    time.sleep(1)
            except Exception as e:
                print(f"ℹ️ 未停留在陌生人消息页：{e}")

            # === 7. 回传接口，标记为已回复 ===
            try:
                resp = requests.post(
                    f"{BASE_API}/mark-replied",
                    json={"id": record_id}
                )
                resp.raise_for_status()
                print(f"已标记记录 {record_id} 为已回复")

                # 成功时通知
                message = (
                    f"✅ 私信回复成功\n"
                    f"> 用户号：{user_xhs_number}\n"
                    f"> 原私信：{message_content}\n"
                    f"> 回复内容：{reply_content}"
                )

                # 如果有分享了小助理号，加一句提示
                if share_b_account == 1:
                    message += "\n> 分享小助理号成功"

                self.send_to_wechat_group(wechat_webhook_url, message)

                # 如果需要分享小助理号
                if share_b_account == 1:
                    try:
                        self.do_share_b_account(user_username)
                        print("已成功分享小助理号")
                    
                    except Exception as e:
                        print(f"分享小助理号失败：{e}")

            except Exception as e:
                print(f"标记为已回复失败：{e}")

        except Exception as e:
            print("发送消息失败：", e)

    def clear_likes_and_favorites(self, monitor_nickname):
        """处理赞和收藏红点，点击进入再返回清除红点"""
        try:
            # 查找赞和收藏按钮
            likes_buttons = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/egu" and contains(@content-desc, "赞和收藏") and contains(@content-desc, "未读")]'
            )
            if len(likes_buttons) > 0:
                logger.info(f"[{monitor_nickname}] 检测到赞和收藏未读消息，点击清除红点")
                likes_buttons[0].click()
                time.sleep(2)
                self.device_session.back()
                time.sleep(1)
                return True
            else:
                logger.info(f"[{monitor_nickname}] ✅ 无赞和收藏未读消息")
                return False
        except Exception as e:
            logger.error(f"[{monitor_nickname}] 处理赞和收藏失败: {e}")
            return False

    def should_process_private_messages_new(self):
        """判断是否应该处理私信：当前屏幕有私信红点，或者其他模块都没有红点但消息tab有未读"""
        try:
            # 检查赞和收藏红点
            likes_buttons = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/egu" and contains(@content-desc, "赞和收藏") and contains(@content-desc, "未读")]'
            )
            has_likes_red_dot = len(likes_buttons) > 0

            # 检查评论红点
            comment_buttons = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/b_4" and contains(@content-desc, "未读")]'
            )
            has_comment_red_dot = len(comment_buttons) > 0

            # 检查关注红点
            follow_buttons = self.device_session.find_all_by_xpath(
                '//*[contains(@content-desc, "新增关注") and contains(@content-desc, "未读")]'
            )
            has_follow_red_dot = len(follow_buttons) > 0

            # 检查当前屏幕私信红点
            current_screen_has_red_dots = self.check_current_screen_red_dots()

            # 检查消息tab总红点
            msg_tabs = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/du7" and contains(@content-desc, "未读")]'
            )
            has_message_red_dot = len(msg_tabs) > 0

            # 应该处理私信的条件：
            # 1. 当前屏幕有私信红点（必须处理）
            # 2. 或者：其他模块都没有红点，但消息tab有未读（说明有不在当前屏幕的私信）
            should_process = (
                current_screen_has_red_dots or  # 当前屏幕有私信红点，必须处理
                (not has_likes_red_dot and not has_comment_red_dot and not has_follow_red_dot and has_message_red_dot)
            )

            logger.info(f"私信处理判断: 赞和收藏={has_likes_red_dot}, 评论={has_comment_red_dot}, "
                       f"关注={has_follow_red_dot}, 当前屏幕私信={current_screen_has_red_dots}, "
                       f"消息总计={has_message_red_dot}, 应处理私信={should_process}")

            return should_process
        except Exception as e:
            logger.error(f"判断是否处理私信失败: {e}")
            return False

    def restart_app(self):
        """重启小红书app"""
        try:
            logger.info("开始重启小红书app")
            self.device_session.close_app("com.xingin.xhs")
            time.sleep(3)
            self.device_session.activate_app("com.xingin.xhs")
            wait_time = random.randint(10, 15)
            logger.info(f"等待 {wait_time} 秒让 App 重新启动...")
            time.sleep(wait_time)

            # 重新初始化应用状态
            self.ensure_app_ready()
            logger.info("✅ app重启完成")
        except Exception as e:
            logger.error(f"❌ app重启失败: {e}")

    def check_current_screen_red_dots(self):
        """检查当前屏幕是否有红点用户"""
        try:
            message_items = self.device_session.find_all_by_id("com.xingin.xhs:id/bgf")

            for item in message_items:
                try:
                    # 查找红点
                    red_dot_el = item.find_element_by_id("com.xingin.xhs:id/a3u")
                    red_dot_text = red_dot_el.get_attribute("text")
                    if red_dot_text.strip().isdigit():
                        return True  # 找到红点用户
                except Exception:
                    # 没有红点，继续检查下一个
                    continue

            return False  # 没有找到任何红点用户
        except Exception as e:
            logger.error(f"检查当前屏幕红点失败: {e}")
            return False

    def check_other_modules_red_dots(self):
        """检查其他模块（赞和收藏、关注、评论）是否有红点"""
        try:
            # 检查赞和收藏红点
            likes_buttons = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/egu" and contains(@content-desc, "赞和收藏") and contains(@content-desc, "未读")]'
            )
            has_likes_red_dot = len(likes_buttons) > 0

            # 检查评论红点
            comment_buttons = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/b_4" and contains(@content-desc, "未读")]'
            )
            has_comment_red_dot = len(comment_buttons) > 0

            # 检查关注红点
            follow_buttons = self.device_session.find_all_by_xpath(
                '//*[contains(@content-desc, "新增关注") and contains(@content-desc, "未读")]'
            )
            has_follow_red_dot = len(follow_buttons) > 0

            return has_likes_red_dot or has_comment_red_dot or has_follow_red_dot
        except Exception as e:
            logger.error(f"检查其他模块红点失败: {e}")
            return False

    def process_current_screen_private_messages(self, monitor_nickname, monitor_xhs_number, wechat_webhook_url):
        """处理当前屏幕的私信消息（包括陌生人消息和普通私信）"""
        # 检查是否有"陌生人消息"
        stranger_entries = self.device_session.find_all_by_xpath(
            '//*[contains(@content-desc, "陌生人消息") and contains(@content-desc, "未读")]'
        )

        if stranger_entries:
            logger.info(f"[{monitor_nickname}] 检测到陌生人未读消息，点击进入陌生人消息列表")
            stranger_entries[0].click()
            time.sleep(2)

            # 统一处理陌生人消息红点列表（跟普通消息逻辑一样）
            self.process_private_message_list(monitor_nickname, monitor_xhs_number, wechat_webhook_url)
            self.device_session.back()
            time.sleep(1)
        else:
            logger.info(f"[{monitor_nickname}] ✅ 无陌生人未读消息")

        # 处理普通私信消息
        self.process_private_message_list(monitor_nickname, monitor_xhs_number, wechat_webhook_url)

    def check_message_red_dot(self):
        """检查消息tab是否还有红点"""
        try:
            msg_tabs = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/du7" and contains(@content-desc, "未读")]'
            )
            return len(msg_tabs) > 0
        except Exception as e:
            logger.error(f"检查消息红点失败: {e}")
            return False

    def check_likes_and_favorites_visible(self):
        """检查赞和收藏元素是否在当前屏幕可见"""
        try:
            likes_elements = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/egu" and contains(@content-desc, "赞和收藏")]'
            )
            return len(likes_elements) > 0
        except Exception as e:
            logger.error(f"检查赞和收藏可见性失败: {e}")
            return False

    def PM_handler(self, monitor_nickname, monitor_xhs_number):
        wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
        # wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"

        logger.info(f"[{monitor_nickname}] 开始处理私信消息")

        max_clicks = 3  # 最多点击3次消息按钮
        click_count = 0

        # 先处理当前屏幕的私信（包括陌生人消息和普通私信）
        self.process_current_screen_private_messages(monitor_nickname, monitor_xhs_number, wechat_webhook_url)

        # 然后检查是否需要点击消息按钮继续处理
        while click_count < max_clicks:
            # 检查消息tab是否还有未读
            message_has_unread = self.check_message_red_dot()

            if not message_has_unread:
                logger.info(f"[{monitor_nickname}] 消息红点已清除，停止处理")
                break

            # 如果消息有未读，先检查赞和收藏是否在当前屏幕
            # likes_visible = self.check_likes_and_favorites_visible()
            # if not likes_visible:
            #     logger.warning(f"[{monitor_nickname}] 赞和收藏不在当前屏幕，需要重启app")
            #     click_count = max_clicks  # 标记需要重启
            #     break

            logger.info(f"[{monitor_nickname}] 消息有未读，点击消息按钮检查是否真的有私信")

            # 点击消息按钮
            msg_tab = self.device_session.find_all_by_xpath(
                '//*[@resource-id="com.xingin.xhs:id/du7"]'
            )
            if len(msg_tab) > 0:
                msg_tab[0].click()
                time.sleep(2)
                click_count += 1

                # 点击后检查赞和收藏是否还在当前屏幕
                likes_visible_after_click = self.check_likes_and_favorites_visible()

                if likes_visible_after_click:
                    logger.info(f"[{monitor_nickname}] 点击消息按钮后赞和收藏仍在屏幕，说明实际无私信（小红书UI bug）")
                    break
                else:
                    logger.info(f"[{monitor_nickname}] 点击消息按钮后赞和收藏不在屏幕，说明确实有私信要处理")
                    # 处理点击后的当前屏幕私信
                    self.process_current_screen_private_messages(monitor_nickname, monitor_xhs_number, wechat_webhook_url)
            else:
                logger.error(f"[{monitor_nickname}] 找不到消息按钮")
                break

        if click_count >= max_clicks:
            logger.info(f"[{monitor_nickname}] 已达到最大点击次数{max_clicks}，停止处理")

        logger.info(f"[{monitor_nickname}] 私信红点处理完毕")

        # 返回点击消息按钮的次数
        return click_count

    def run(self, monitor_nickname=None, monitor_xhs_number=None):
        """
        循环执行 PM_handler，每次执行完等待 interval 秒
        """
        # 错峰处理
        delay = random.randint(5, 10)
        time.sleep(delay)

        # 初始化应用状态
        self.ensure_app_ready()

        # 获取当前账号信息（如果没有传入的话）
        if not monitor_nickname or not monitor_xhs_number:
            monitor_nickname, monitor_xhs_number = self.get_current_account_info()

        # 点击消息 Tab（仅在未选中时点击）
        msg_tab = self.device_session.find_by_id("com.xingin.xhs:id/du7")
        if msg_tab:
            try:
                is_selected = msg_tab.get_attribute("selected") == "true"
                if not is_selected:
                    msg_tab.click()
                    time.sleep(1)
                else:
                    logger.debug("已在消息页面，跳过点击")
            except Exception as e:
                logger.warning(f"判断消息按钮是否选中失败: {e}")

        # 检测当前屏幕有没有赞和收藏，如果没有就重启app
        likes_visible = self.check_likes_and_favorites_visible()
        if not likes_visible:
            logger.warning(f"[{monitor_nickname}] 当前屏幕没有赞和收藏元素，准备重启app")
            self.restart_app()
            return  # 重启后直接返回，不执行下拉刷新
        else:
            logger.info(f"[{monitor_nickname}] ✅ 赞和收藏元素正常，无需重启")

        # 下拉刷新
        time.sleep(1)
        self.device_session.swipe(170, 500, 170, 1100, 50)

        # 1. 先处理赞和收藏红点
        self.clear_likes_and_favorites(monitor_nickname)

        # 2. 处理关注通知
        self.handle_follows(monitor_xhs_number, monitor_nickname)

        # 3. 处理评论通知
        self.handle_comments(monitor_xhs_number, monitor_nickname)

        # 4. 处理私信（包括红点处理和待回复处理）
        logger.info(f"[{monitor_nickname}] 开始处理私信")

        # 4.1 处理私信红点（如果有的话）
        if self.should_process_private_messages_new():
            logger.info(f"[{monitor_nickname}] 检测到需要处理私信红点，开始处理")
            self.PM_handler(monitor_nickname, monitor_xhs_number)
        else:
            logger.info(f"[{monitor_nickname}] ✅ 无私信红点需要处理")

        # 4.2 处理待回复私信（无论是否有红点都要检查）
        logger.info(f"[{monitor_nickname}] 检查待回复私信")
        wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
        self.handle_mobile_pending_reply(wechat_webhook_url, monitor_xhs_number)

        # 每次循环结束点击首页
        home_tabs = self.device_session.find_all_by_id("com.xingin.xhs:id/du5")
        if home_tabs:
            home_tab = home_tabs[0]
            home_tab.click()
            time.sleep(1)
            logger.info(f"[{monitor_nickname}] 已点击首页按钮，回到首页")
            # 下拉刷新
            time.sleep(1)
            self.device_session.swipe(170, 500, 170, 1100, 50)
            logger.info(f"[{monitor_nickname}] 监听逻辑执行完成")



    def handle_comments(self, monitor_xhs_number, monitor_nickname):
        """处理评论通知"""
        try:
            logger.info(f"🔍 开始处理 {monitor_nickname} 的评论通知...")

            # 处理新评论通知
            comment_count = self.comment_service.process_comment_notifications(monitor_xhs_number, monitor_nickname)

            # 处理待回复的评论
            self.comment_service.handle_pending_reply(monitor_xhs_number)

            logger.info(f"✅ {monitor_nickname} 评论处理完成，共处理 {comment_count} 条评论")

        except Exception as e:
            logger.error(f"❌ 处理 {monitor_nickname} 评论通知失败: {e}")

    def handle_follows(self, monitor_xhs_number, monitor_nickname):
        """处理关注通知"""
        try:
            logger.info(f"🔍 开始处理 {monitor_nickname} 的关注通知...")

            # 通过消息页面检查关注通知
            follow_count1 = self.follow_service.check_follow_notifications(monitor_xhs_number, monitor_nickname)

            logger.info(f"✅ {monitor_nickname} 关注处理完成，共处理 {follow_count1} 个关注通知")

        except Exception as e:
            logger.error(f"❌ 处理 {monitor_nickname} 关注通知失败: {e}")

    def get_and_store_xhs_account_info(self):
        """获取小红书账号信息并调用API更新"""
        try:
            # 获取账号信息
            xhs_nickname, xhs_number = self.get_current_account_info()

            if xhs_nickname and xhs_number:
                # 调用远程API更新设备信息
                self.update_remote_device_info(xhs_nickname, xhs_number)
                return xhs_nickname, xhs_number
            else:
                return "未知账号", "未知号码"

        except Exception as e:
            logger.error(f"获取小红书账号信息失败: {e}")
            return "获取失败", "获取失败"

    def update_remote_device_info(self, xhs_nickname, xhs_number):
        """更新远程设备信息"""
        try:
            headers = {
                'x-token': '2W93grF60JDqnFEI',
                'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Host': 'automatic-android-api.jobeyond.cn',
                'Connection': 'keep-alive'
            }

            # 更新小红书账号描述
            xhs_payload = {
                "id": self.custom_udid,
                "xiaohongshuAccountDescribe": f"{xhs_nickname}({xhs_number})"
            }

            requests.post(
                'https://automatic-android-api.jobeyond.cn/a/device/updateXiaohongshuAccountDescribe',
                headers=headers,
                json=xhs_payload,
                timeout=10
            )

            # 更新当前任务描述
            task_payload = {
                "id": self.custom_udid,
                "currentTaskDescribe": "小红书养号+监听"
            }

            requests.post(
                'https://automatic-android-api.jobeyond.cn/a/device/updateCurrentTaskDescribe',
                headers=headers,
                json=task_payload,
                timeout=10
            )

            logger.info(f"成功更新设备信息: {xhs_nickname}({xhs_number})")

        except Exception as e:
            logger.error(f"更新远程设备信息失败: {e}")

