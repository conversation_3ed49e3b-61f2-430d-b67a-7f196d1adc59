import threading
import time
import schedule
from datetime import datetime, time as dt_time
from concurrent.futures import ThreadPoolExecutor

from ..utils import logger, glv, DeviceSession, appium_manager
from ..logics.xhs_notes_stats import XhsNotesStatsLogic


class ScheduledTaskService:
    """定时任务服务"""
    
    def __init__(self):
        self.domain = None
        self.running = False
        self.scheduler_thread = None
        self.executor = ThreadPoolExecutor(max_workers=2)
        
    def init_app(self, app):
        """初始化服务配置"""
        self.domain = app.config.get('DOMAIN', '')
        
    def start_scheduler(self):
        """启动定时任务调度器"""
        if self.running:
            logger.warning("定时任务调度器已在运行")
            return

        self.running = True

        # 注释掉独立的定时任务，因为现在统计逻辑集成到主任务循环中
        # schedule.every().day.at("00:00").do(self.run_notes_stats_for_all_devices)

        # 启动调度器线程（保留框架，以备将来其他定时任务使用）
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info("✅ 定时任务调度器已启动（笔记统计已集成到主任务循环）")
        
    def stop_scheduler(self):
        """停止定时任务调度器"""
        self.running = False
        schedule.clear()
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("⏹️ 定时任务调度器已停止")
        
    def _run_scheduler(self):
        """运行调度器的内部方法"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"定时任务调度器异常: {e}")
                time.sleep(60)
                
    def run_notes_stats_for_all_devices(self):
        """为所有设备运行笔记统计"""
        try:
            logger.info("🕛 开始执行定时笔记统计任务...")
            
            # 获取所有连接的设备
            from .task_service import devices_udids_local
            udids_connected = devices_udids_local()
            
            if not udids_connected:
                logger.warning("⚠️ 没有找到连接的设备，跳过笔记统计")
                return
                
            # 为每个设备提交统计任务
            futures = []
            for udid_dict in udids_connected:
                future = self.executor.submit(self._run_stats_for_device, udid_dict)
                futures.append(future)
                
            # 等待所有任务完成
            for future in futures:
                try:
                    future.result(timeout=1800)  # 30分钟超时
                except Exception as e:
                    logger.error(f"设备统计任务异常: {e}")
                    
            logger.info("✅ 所有设备的笔记统计任务已完成")
            
        except Exception as e:
            logger.error(f"❌ 定时笔记统计任务失败: {e}")
            
    def _run_stats_for_device(self, udid_dict):
        """为单个设备运行笔记统计"""
        udid = udid_dict["udid"]
        custom_udid = udid_dict["custom_udid"]
        
        try:
            logger.info(f"📱 开始为设备 {custom_udid} 执行笔记统计...")
            
            # 启动Appium服务器
            server_info = appium_manager.start_server_for_device(udid)
            if not server_info:
                logger.error(f"❌ 无法为设备 {udid} 启动Appium服务器")
                return
                
            appium_server_url = f"http://127.0.0.1:{server_info['port']}"
            system_port = server_info['system_port']
            
            # 创建设备会话
            device_session = DeviceSession(
                udid, 
                custom_name=custom_udid, 
                custom_udid=custom_udid, 
                appium_server_url=appium_server_url, 
                system_port=system_port
            )
            
            # 连接设备
            device_session.connect()
            
            # 激活小红书应用
            device_session.activate_app("com.xingin.xhs")
            time.sleep(3)
            
            # 创建统计逻辑实例
            stats_logic = XhsNotesStatsLogic(custom_udid, self.domain)
            stats_logic.init(device_session)
            
            # 执行统计
            success = stats_logic.run_stats_collection(max_scrolls=10)  # 最多滚动10次
            
            if success:
                logger.info(f"✅ 设备 {custom_udid} 笔记统计完成")
            else:
                logger.warning(f"⚠️ 设备 {custom_udid} 笔记统计失败")
                
        except Exception as e:
            logger.error(f"❌ 设备 {custom_udid} 笔记统计异常: {e}")
        finally:
            try:
                device_session.quit()
            except:
                pass
                
    def run_manual_stats(self, udid_dict):
        """手动执行笔记统计"""
        try:
            logger.info("🔧 手动执行笔记统计...")
            self._run_stats_for_device(udid_dict)
        except Exception as e:
            logger.error(f"❌ 手动笔记统计失败: {e}")


# 服务单例
scheduled_task_service = ScheduledTaskService()


def init_scheduled_tasks(app):
    """初始化定时任务"""
    try:
        scheduled_task_service.init_app(app)
        scheduled_task_service.start_scheduler()
        logger.info("✅ 定时任务服务已初始化")
    except Exception as e:
        logger.error(f"❌ 定时任务服务初始化失败: {e}")


def stop_scheduled_tasks():
    """停止定时任务"""
    try:
        scheduled_task_service.stop_scheduler()
        logger.info("✅ 定时任务服务已停止")
    except Exception as e:
        logger.error(f"❌ 停止定时任务服务失败: {e}")
