from ..utils import glv, CommonMobileUtils
import re
import datetime

class ToolService:

    @staticmethod
    def build_common_var(custom_udid, key, value):
        if custom_udid not in glv.keys():
            glv[custom_udid] = {}
        glv[custom_udid][key] = value

    @staticmethod
    def get_today_is_complete(custom_udid, flag):
        if custom_udid not in glv:
            ToolService.build_common_var(custom_udid,flag,[])
        today = datetime.date.today().strftime('%Y-%m-%d')
        if flag not in glv[custom_udid]:
            ToolService.build_common_var(custom_udid,flag,[])
        if today in glv[custom_udid][flag]:
            return True
        return False

    @staticmethod
    def set_today_is_complete(custom_udid,flag):
        if custom_udid not in glv:
            ToolService.build_common_var(custom_udid,flag,[])
        if flag not in glv[custom_udid]:
            ToolService.build_common_var(custom_udid,flag,[])
        today = datetime.date.today().strftime('%Y-%m-%d')
        call_complete_flag = glv[custom_udid][flag]
        call_complete_flag.append(today)
        ToolService.build_common_var(custom_udid,flag,call_complete_flag)
        return True

    @staticmethod
    def is_index_valid(lst, index):
        try:
            _ = lst[index] 
            return True
        except IndexError:
            return False

    @staticmethod
    def str_to_int(input):
        try:
            output = int(input)
        except:
            output = 0
        return output

    @staticmethod
    def convert_l_c_c(text):
        if "万" in text:
            match = re.search(r'(\d+\.\d+|\d+)', text)
            if match:
                return int(float(match.group(1))*10000)
        if "亿" in text:
            match = re.search(r'(\d+\.\d+|\d+)', text)
            if match:
                return int(float(match.group(1))*100000000)
        match = re.search(r'(\d+)', text)
        if match:
            return int(match.group(1))
        else:
            return 0