from flask import Flask
from .extensions import scheduler, init_scheduler
from .config import BaseConfig, AiRecruitConfig, NancAiRecruitConfig, XiaohongshuConfig, SpiderConfig
import os
from .utils import logger

CONFIG_MAP = {
    'airecruit': AiRecruitConfig,
    'nancairecruit': NancAiRecruitConfig,
    'xiaohongshu': XiaohongshuConfig,
    'spider': SpiderConfig,
}

def create_app():
    env = os.environ.get('APP_ENV', '')
    config_class = CONFIG_MAP.get(env, '')
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 设置日志级别
    log_level = app.config.get('LOG_LEVEL', 'ERROR')
    logger.set_level(log_level)

    # 初始化扩展
    initialize_extensions(app)

    # 注册蓝图
    register_blueprints(app)

    return app


def initialize_extensions(app):
    """初始化所有扩展"""
    scheduler.init_app(app)

    # 初始化服务
    from .services.task_service import task_service
    task_service.init_app(app)

    # 初始化服务
    from .services.common_service import common_service
    common_service.init_app(app)
    common_service.login()


    # 初始化调度器任务
    init_scheduler(app)

    # 启动调度器
    if not scheduler.running:
        scheduler.start()


def register_blueprints(app):
    """注册所有蓝图"""
    from .blueprints import api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api')