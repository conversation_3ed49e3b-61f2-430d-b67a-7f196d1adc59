#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书笔记统计功能测试脚本
"""

import requests
import json
import time

def test_manual_trigger():
    """测试手动触发笔记统计"""
    print("🧪 测试手动触发笔记统计...")
    
    url = "http://localhost:8080/api/xhs/notes-stats"
    
    try:
        response = requests.post(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 触发成功: {result['message']}")
            print(f"📱 设备: {result['data']['device']}")
            print(f"📊 状态: {result['data']['status']}")
        else:
            print(f"❌ 触发失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_status_check():
    """测试状态检查"""
    print("\n🔍 测试状态检查...")
    
    url = "http://localhost:8080/api/xhs/notes-stats/status"
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 状态获取成功:")
            print(f"📅 调度器运行状态: {result['data']['scheduler_running']}")
            print(f"⏰ 下次运行时间: {result['data']['next_run_time']}")
        else:
            print(f"❌ 状态获取失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_adb_devices():
    """测试ADB设备连接"""
    print("\n📱 测试ADB设备连接...")
    
    import subprocess
    
    try:
        result = subprocess.run(
            ['adb', 'devices'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            check=True
        )
        
        output = result.stdout
        lines = output.strip().split('\n')
        
        print(f"ADB输出:")
        for line in lines:
            print(f"  {line}")
            
        device_count = len([line for line in lines[1:] if 'device' in line and 'unauthorized' not in line])
        print(f"\n✅ 找到 {device_count} 个已连接的设备")
        
    except FileNotFoundError:
        print("❌ 找不到ADB命令，请确保Android SDK已安装")
    except subprocess.CalledProcessError as e:
        print(f"❌ ADB命令执行失败: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def main():
    """主测试函数"""
    print("🚀 小红书笔记统计功能测试")
    print("=" * 50)
    
    # 测试ADB设备连接
    test_adb_devices()
    
    # 测试状态检查
    test_status_check()
    
    # 询问是否要手动触发统计
    print("\n" + "=" * 50)
    user_input = input("是否要手动触发笔记统计？(y/N): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        test_manual_trigger()
        print("\n⏳ 统计任务已在后台运行，请查看应用日志获取详细进度...")
    else:
        print("⏭️ 跳过手动触发测试")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
