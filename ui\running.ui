<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>459</width>
    <height>160</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>正在运行！！！</string>
  </property>
  <property name="windowIcon">
   <iconset theme="QIcon::ThemeIcon::MediaSeekForward"/>
  </property>
  <widget class="QPlainTextEdit" name="log_output">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>6</x>
     <y>6</y>
     <width>450</width>
     <height>100</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>450</width>
     <height>100</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>100</width>
     <height>100</height>
    </size>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="runtime_label">
   <property name="geometry">
    <rect>
     <x>6</x>
     <y>132</y>
     <width>301</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>2025-08-09 20:00:00</string>
   </property>
  </widget>
  <widget class="QPushButton" name="stop_button">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>6</x>
     <y>110</y>
     <width>22</width>
     <height>18</height>
    </rect>
   </property>
   <property name="inputMethodHints">
    <set>Qt::InputMethodHint::ImhLatinOnly</set>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="icon">
    <iconset theme="QIcon::ThemeIcon::Battery"/>
   </property>
   <property name="flat">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
