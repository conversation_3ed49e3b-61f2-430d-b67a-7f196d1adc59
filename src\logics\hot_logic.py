import datetime
import time
import re

from lxml import etree

from ..utils import logger, glv, CommonMobileUtils, CommonUtils
from ..services.tool_service import ToolService

class HotLogic:

    def __init__(self, custom_udid):
        self.domain = "https://api.open.hctalent.cn/channel"
        self.custom_udid = custom_udid
        self.device_session = None
        self.commonUtils = CommonUtils()

        self.sign = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="
        self.app_key = "f08bf7f7cfe8c038"

    def init(self, device_session):
        self.device_session = device_session

    def is_spider(self, flag):
        hour = int(datetime.datetime.now().strftime("%H"))
        if hour >= 21:
            hour = 21
        elif hour >= 15:
            hour = 15
        elif hour >= 9:
            hour = 9
        elif hour >= 3:
            hour = 3
        else:
            return ""
        top_complete_flag = flag + "_top_is_complete_" + str(hour)
        if not ToolService.get_today_is_complete(self.custom_udid, top_complete_flag):
            return top_complete_flag
        return ""

    def spider_weibo_top(self):
        top_complete_flag = self.is_spider("weibo")
        if top_complete_flag == "":
            return
        self.device_session.activate_app("com.sina.weibo")
        time.sleep(8)
        xpath_str = ".//android.widget.FrameLayout[@class='android.widget.FrameLayout' and @content-desc='首页']"
        i = 0
        while True:
            i = i + 1
            if i > 10:
                break
            index_obj_list = self.device_session.find_all_by_xpath(xpath_str)
            if len(index_obj_list) > 0:
                index_obj_list[-1].click()
                break
            CommonMobileUtils.back(self.device_session, 1)

        # 点击发现
        xpath_find_str = ".//android.widget.FrameLayout[@class='android.widget.FrameLayout' and @content-desc='发现']"
        find_obj_list = self.device_session.find_all_by_xpath(xpath_find_str)

        if len(find_obj_list)>0:
            find_obj_list[-1].click()
            time.sleep(5)

        bound = self.device_session.find_by_xpath(".//android.view.View[contains(@content-desc,'更多热搜')]").get_bounding()
        print(bound)
        self.device_session.tap(450,521)

        spider_time = datetime.datetime.now().strftime("%Y-%m-%d %H:00:00")

        for tab_title in ["热搜", "文娱"]:
            if tab_title == "热搜":
                catgory = "综合"
            else:
                self.device_session.find_by_xpath(f".//android.widget.TextView[@resource-id='com.sina.weibo:id/search_item_text' and @text='{tab_title}']").click()
                catgory = tab_title
            sort = 0
            is_success = False
            contents = []
            contents_todb = []
            already_spider_title = []
            end_i = 0
            while True:
                end_i = end_i + 1
                if end_i > 30:
                    break
                start_count = len(contents)
                page_source = self.device_session.get_page_source()
                etree_xml = etree.fromstring(page_source.encode('utf-8'))
                title_obj_list = etree_xml.xpath(".//androidx.recyclerview.widget.RecyclerView[@class='androidx.recyclerview.widget.RecyclerView']/android.widget.RelativeLayout",namespaces={'x': 'urn:h17-org:v3'})
                for title_obj in title_obj_list:
                    if sort > 55:
                        is_success = True
                        break
                    title_list = title_obj.xpath(".//android.widget.TextView[@resource-id='com.sina.weibo:id/tv_coupon_des']/@text")
                    if len(title_list) == 0:
                        break
                    title = title_list[0] + catgory
                    if title in already_spider_title:
                        continue
                    already_spider_title.append(title)
                    sort = sort + 1

                    topnum_list = title_obj.xpath(".//android.widget.TextView[@resource-id='com.sina.weibo:id/tv_coupon_des_extr']/@text")
                    topNum = 0
                    if len(topnum_list) > 0:
                        topNum = ToolService.convert_l_c_c(topnum_list[0])
                    con = {
                        "title": title_list[0],
                        "source": "微博",
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": topNum,
                        "sort": sort,
                        "spiderTime": spider_time
                    }
                    contents.append(con)

                    contents_todb.append({
                        "title": title_list[0],
                        "source": 7,
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": topNum,
                        "sort": sort,
                        "spiderTime": int(time.time())
                    })

                if is_success or start_count == len(contents):
                    break
                self.device_session.swipe(300, 1200, 300, 500)

            if len(contents) > 0:
                self.commonUtils.push_mingdao_multi_add(contents, self.sign, self.app_key, "top")
                self.commonUtils.http_post("https://api.open.hctalent.cn/channel/grab/add-top", json_data=contents_todb)
        ToolService.set_today_is_complete(self.custom_udid, top_complete_flag)

    def spider_baidu_top(self):
        top_complete_flag = self.is_spider("baidu")
        if top_complete_flag == "":
            return
        self.device_session.activate_app("com.baidu.searchbox")
        time.sleep(8)
        # 点击关闭按钮
        close_button_list = self.device_session.find_all_by_xpath(".//android.widget.ImageView[@class='android.widget.ImageView' and @content-desc='关闭']")
        if len(close_button_list) > 0:
            close_button_list[0].click()

        # 回退到首页
        index_xpath_list = [".//android.widget.TabWidget[@resource-id='android:id/tabs']"]
        CommonMobileUtils.back_to_appoint_page_use_xpath(self.device_session, index_xpath_list)
        # 点击百度首页的按钮
        CommonMobileUtils.click_use_xpath(self.device_session,".//android.widget.TabWidget[@resource-id='android:id/tabs']/android.widget.FrameLayout[1]","click")
        # 双击热搜按钮
        CommonMobileUtils.click_use_xpath(self.device_session, ".//*[@resource-id='com.baidu.searchbox:id/obfuscated' and @text='热搜']", "dbclick")
        spider_time = datetime.datetime.now().strftime("%Y-%m-%d %H:00:00")
        already_spider_title = []
        contents = []
        contents_todb = []
        end_i = 0
        while True:
            end_i = end_i + 1
            if end_i > 30:
                break
            # 点击查看更多
            more_xpath = ".//android.widget.TextView[@resource-id='com.baidu.searchbox:id/obfuscated' and @text='展开更多']"
            more_obj_list = self.device_session.find_all_by_xpath(more_xpath)
            for more_obj in more_obj_list:
                try:
                    more_obj.click()
                except Exception as e:
                    pass
            page_source = self.device_session.get_page_source()
            etree_xml = etree.fromstring(page_source.encode('utf-8'))
            viewpager_obj_list = etree_xml.xpath(".//androidx.viewpager.widget.ViewPager[@class='androidx.viewpager.widget.ViewPager']", namespaces={'x': 'urn:h17-org:v3'})
            toptitle_list = viewpager_obj_list[2].xpath(".//android.widget.TextView[@resource-id='com.baidu.searchbox:id/obfuscated']/@text", namespaces={'x': 'urn:h17-org:v3'})
            i = 0
            start_count = len(contents)
            catgory = "综合"
            for toptitle in toptitle_list:
                if len(toptitle) == 3 and "榜" in toptitle:
                    catgory = toptitle
                catgory = catgory.replace("榜", "")
                i = i + 1
                if ToolService.str_to_int(toptitle) > 0 and i < len(toptitle_list):
                    title = toptitle_list[i] + catgory
                    if title in already_spider_title:
                        continue

                    already_spider_title.append(title)
                    con = {
                        "title": toptitle_list[i],
                        "source": "百度",
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": 0,
                        "sort": toptitle,
                        "spiderTime": spider_time
                    }
                    contents.append(con)

                    contents_todb.append({
                        "title": toptitle_list[i],
                        "source": 8,
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": 0,
                        "sort": toptitle,
                        "spiderTime": int(time.time())
                    })

            if start_count == len(contents):
                break
            self.device_session.swipe(300, 1200, 300, 500)
        if len(contents) > 0:
            self.commonUtils.push_mingdao_multi_add(contents, self.sign, self.app_key, "top")
            self.commonUtils.http_post("https://api.open.hctalent.cn/channel/grab/add-top", json_data=contents_todb)
        ToolService.set_today_is_complete(self.custom_udid, top_complete_flag)

    def spider_douyin_top(self):
        top_complete_flag = self.is_spider("douyin")
        if top_complete_flag == "":
            return
        self.device_session.activate_app("com.ss.android.ugc.aweme")
        time.sleep(8)
        # 关闭按键
        ids = ["com.xingin.xhs:id/dao"]
        for idstr in ids:
            page_source = self.device_session.get_page_source()
            if idstr in page_source:
                CommonMobileUtils.click_use_xpath(self.device_session, f".//*[@resource-id='{idstr}']", "click")


        # 回到首页
        flags = [
            ["翻转", "设置", "闪光灯"],
            ["拍摄", "com.ss.android.ugc.aweme:id/j+-", "语音", "com.ss.android.ugc.aweme:id/j+_"],
            ["新朋友", "android:id/text1", "添加朋友", "com.ss.android.ugc.aweme:id/right_btn"],
            ["全部消息", "com.ss.android.ugc.aweme:id/z69", "设置", "com.ss.android.ugc.aweme:id/ldo", "返回", "com.ss.android.ugc.aweme:id/iv_back"],
            ["评论", "com.ss.android.ugc.aweme:id/dbb", "收藏", "赞", "浏览"],
            ["收藏", "com.ss.android.ugc.aweme:id/c5a", "评论", "com.ss.android.ugc.aweme:id/c_g"],
            ["陌生人消息", "com.ss.android.ugc.aweme:id/v6i"],
            ["确认", "com.ss.android.ugc.aweme:id/c+o", "删除", "com.ss.android.ugc.aweme:id/tfe"],
            ["种草榜", "娱乐榜", "社会榜"],

        ]
        CommonMobileUtils.back_to_appoint_page_use_not_flags(self.device_session, flags)

        top_button_list = self.device_session.find_all_by_xpath(".//*[@resource-id='com.ss.android.ugc.aweme:id/2+b' and @text='热点']")
        if len(top_button_list) > 0:
            top_button_list[0].click()
        else:
            bound = self.device_session.find_by_xpath(".//*[@resource-id='com.ss.android.ugc.aweme:id/2+b' and @text='关注']").get_bounding()
            self.device_session.swipe(bound[0], bound[1] + 10, bound[0] + 400, bound[1] + 10)
            self.device_session.find_by_xpath(".//*[@resource-id='com.ss.android.ugc.aweme:id/2+b' and @text='热点']").click()

        bound = self.device_session.find_by_xpath(".//*[@resource-id='com.ss.android.ugc.aweme:id/b=o']").get_bounding()

        self.device_session.tap(bound[0] + 200, bound[1] + 220)
        already_spider_title = []
        spider_time = datetime.datetime.now().strftime("%Y-%m-%d %H:00:00")
        for tap_title in ["热点榜", "种草榜", "娱乐榜", "社会榜"]:
            if tap_title != "热点榜":
                tap_title_text = tap_title + "，按钮"
                self.device_session.find_by_xpath(f".//com.bytedance.ies.xelement.viewpager.childitem.LynxTabbarItem[@text='{tap_title_text}' and @class='com.bytedance.ies.xelement.viewpager.childitem.LynxTabbarItem']").click()
            contents = []
            contents_todb = []
            end_i = 0
            while True:
                end_i = end_i + 1
                if end_i>30:
                    break
                start_count = len(already_spider_title)
                page_source = self.device_session.get_page_source()
                etree_xml = etree.fromstring(page_source.encode('utf-8'))
                top_title_list = etree_xml.xpath(".//com.lynx.tasm.behavior.ui.view.UIComponent[@class='com.lynx.tasm.behavior.ui.view.UIComponent']/@text", namespaces={'x': 'urn:h17-org:v3'})
                top_title_list = list({}.fromkeys(top_title_list).keys())
                i = -1
                for top_title in top_title_list:
                    i = i + 1
                    match = re.match(r"(.*?)第(\d+)名，(.*?),，热度(.*?)，按钮", top_title)
                    if not match:
                        continue

                    title = match.group(3) + tap_title
                    if title in already_spider_title:
                        continue
                    already_spider_title.append(title)
                    catgory = match.group(1).replace("榜", "")
                    con = {
                        "title": match.group(3),
                        "source": "抖音",
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": ToolService.convert_l_c_c(match.group(4)),
                        "sort": match.group(2),
                        "spiderTime": spider_time
                    }
                    contents.append(con)

                    contents_todb.append({
                        "title": match.group(3),
                        "source": 9,
                        "url": "",
                        "catgory": catgory,
                        "content": "",
                        "topNum": ToolService.convert_l_c_c(match.group(4)),
                        "sort": match.group(2),
                        "spiderTime": int(time.time())
                    })

                if start_count == len(already_spider_title):
                    break

                self.device_session.swipe(300, 1200, 300, 400)
            if len(contents) > 0:
                self.commonUtils.push_mingdao_multi_add(contents, self.sign, self.app_key, "top")
                self.commonUtils.http_post("https://api.open.hctalent.cn/channel/grab/add-top", json_data=contents_todb)

        ToolService.set_today_is_complete(self.custom_udid, top_complete_flag)

    def spider_xhs_top(self):
        top_complete_flag = self.is_spider("xhs")
        if top_complete_flag == "":
            return
        self.device_session.activate_app("com.xingin.xhs")
        time.sleep(8)
        # 回退到首页
        xpath_list = [".//android.view.ViewGroup[@content-desc='首页']",
                      ".//android.view.ViewGroup[@content-desc='我']"]
        CommonMobileUtils.back_to_appoint_page_use_xpath_all_have(self.device_session, xpath_list)
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.view.ViewGroup[@content-desc='首页']","click")

        # 点击右上角的搜索标记
        CommonMobileUtils.click_use_xpath(self.device_session, ".//android.widget.Button[@content-desc='搜索']","click")
        already_spider_title = []
        contents = []
        contents_todb = []
        spider_time = datetime.datetime.now().strftime("%Y-%m-%d %H:00:00")
        for i in range(0, 2):
            end_i = 0
            while True:
                end_i = end_i + 1
                if end_i > 30:
                    break
                start_count = len(already_spider_title)
                title_obj_list = self.device_session.find_all_by_xpath(".//androidx.recyclerview.widget.RecyclerView/android.widget.Button//android.widget.TextView[2]")
                for title_obj in title_obj_list:
                    try:
                        title = title_obj.get_attribute("text")
                    except Exception as e:
                        logger.warning(f"{e}")
                        continue
                    if title in already_spider_title:
                        continue
                    already_spider_title.append(title)
                    con = {
                        "title": title,
                        "source": "小红书",
                        "url": "",
                        "catgory": "综合",
                        "content": "",
                        "topNum": len(already_spider_title),
                        "sort": len(already_spider_title),
                        "spiderTime": spider_time
                    }
                    contents.append(con)

                    contents_todb.append({
                        "title": title,
                        "source": 5,
                        "url": "",
                        "catgory": "综合",
                        "content": "",
                        "topNum": len(already_spider_title),
                        "sort": len(already_spider_title),
                        "spiderTime": int(time.time())
                    })

                if start_count == len(already_spider_title):
                    break

            self.device_session.swipe(300, 600, 300, 300)
        if len(contents) > 0:
            print(contents)
            self.commonUtils.push_mingdao_multi_add(contents, self.sign, self.app_key, "top")
            self.commonUtils.http_post("https://api.open.hctalent.cn/channel/grab/add-top", json_data=contents_todb)

        ToolService.set_today_is_complete(self.custom_udid, top_complete_flag)