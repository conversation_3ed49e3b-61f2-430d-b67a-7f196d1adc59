class BaseConfig:
    # Flask-APScheduler配置
    SCHEDULER_API_ENABLED = True
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'
    APPIUM_MORE_THREAD = True

class SpiderConfig(BaseConfig):
    MAX_CONCURRENT_TASKS = 100
    NEZHA_USERNAME = "linco"
    NEZHA_PASSWORD = "123456"
    LOG_LEVEL = "debug"
    RUN_FLAGS = ["spider"]
    ISUPDATE_RUNCONFIG = False

class AiRecruitConfig(BaseConfig):
    MAX_CONCURRENT_TASKS = 100
    DOMAIN = "https://api.open.hctalent.cn/channel"
    NEZHA_USERNAME = "linco"
    NEZHA_PASSWORD = "123456"
    LOG_LEVEL = "debug"
    RUN_FLAGS = ["airecruit"]
    ISUPDATE_RUNCONFIG = True

class NancAiRecruitConfig(BaseConfig):
    MAX_CONCURRENT_TASKS = 100
    DOMAIN = "https://nanc.open.hctalent.cn/channel"
    NEZHA_USERNAME = "linco"
    NEZHA_PASSWORD = "123456"
    LOG_LEVEL = "debug"
    RUN_FLAGS = ["airecruit"]
    ISUPDATE_RUNCONFIG = True

class XiaohongshuConfig(BaseConfig):
    MAX_CONCURRENT_TASKS = 100
    DOMAIN = "https://api.open.hctalent.cn/channel"
    NEZHA_USERNAME = "linco"
    NEZHA_PASSWORD = "123456"
    LOG_LEVEL = "debug"
    RUN_FLAGS = ["xiaohongshu"]
    ISUPDATE_RUNCONFIG = False

# class XiaohongshuAutoConfig(BaseConfig):
#     MAX_CONCURRENT_TASKS = 100
#     DOMAIN = "https://api.open.hctalent.cn/channel"
#     NEZHA_USERNAME = "linco"
#     NEZHA_PASSWORD = "123456"
#     LOG_LEVEL = "info"
#     RUN_FLAGS = ["xiaohongshu_auto"]
#     ISUPDATE_RUNCONFIG = False
